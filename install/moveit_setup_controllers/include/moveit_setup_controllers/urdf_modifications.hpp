/*********************************************************************
 * Software License Agreement (BSD License)
 *
 *  Copyright (c) 2022, Metro Robots
 *  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *   * Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *   * Redistributions in binary form must reproduce the above
 *     copyright notice, this list of conditions and the following
 *     disclaimer in the documentation and/or other materials provided
 *     with the distribution.
 *   * Neither the name of Metro Robots nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 *  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 *********************************************************************/

/* Author: David V. Lu!! */
#pragma once

#include <moveit_setup_framework/setup_step.hpp>
#include <moveit_setup_controllers/control_xacro_config.hpp>

namespace moveit_setup
{
namespace controllers
{
class UrdfModifications : public SetupStep
{
public:
  std::string getName() const override
  {
    return "ros2_control URDF Modifications";
  }

  void onInit() override;

  bool isReady() const override
  {
    return !srdf_config_->getGroups().empty();
  }

  void refresh()
  {
    control_xacro_config_->loadFromDescription();
  }

  /**
   * @brief Return true if there aren't ros2_control tags for all the joints
   */
  bool needsModification() const;

  /**
   * @brief Add ros2_control tags for all unconfigured joints with the specified interfaces
   */
  void setInterfaces(const std::vector<std::string>& command_interfaces,
                     const std::vector<std::string>& state_interfaces);

  std::string getJointsXML() const
  {
    return control_xacro_config_->getJointsXML();
  }

  const ControlInterfaces& getDefaultControlInterfaces() const
  {
    return control_xacro_config_->getDefaultControlInterfaces();
  }

  const ControlInterfaces& getAvailableControlInterfaces() const
  {
    return control_xacro_config_->getAvailableControlInterfaces();
  }

protected:
  std::shared_ptr<SRDFConfig> srdf_config_;
  std::shared_ptr<ControlXacroConfig> control_xacro_config_;
};
}  // namespace controllers
}  // namespace moveit_setup
