Panels:
  - Class: rviz_common/Displays
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /MotionPlanning1
  - Class: rviz_common/Help
    Name: Help
  - Class: rviz_common/Views
    Name: Views
Visualization Manager:
  Displays:
    - Class: rviz_default_plugins/Grid
      Name: Grid
      Value: true
    - Class: moveit_rviz_plugin/MotionPlanning
      Name: MotionPlanning
      Planned Path:
        Loop Animation: true
        State Display Time: 0.05 s
        Trajectory Topic: display_planned_path
      Planning Scene Topic: monitored_planning_scene
      Robot Description: robot_description
      Scene Geometry:
        Scene Alpha: 1
      Scene Robot:
        Robot Alpha: 0.5
      Value: true
  Global Options:
    Fixed Frame: [PLANNING_FRAME]
  Tools:
    - Class: rviz_default_plugins/Interact
    - Class: rviz_default_plugins/MoveCamera
    - Class: rviz_default_plugins/Select
  Value: true
  Views:
    Current:
      Class: rviz_default_plugins/Orbit
      Distance: 2.0
      Focal Point:
        X: -0.1
        Y: 0.25
        Z: 0.30
      Name: Current View
      Pitch: 0.5
      Target Frame: [PLANNING_FRAME]
      Yaw: -0.623
Window Geometry:
  Height: 975
  QMainWindow State: 000000ff00000000fd0000000100000000000002b400000375fc0200000005fb00000044004d006f00740069006f006e0050006c0061006e006e0069006e00670020002d0020005400720061006a006500630074006f0072007900200053006c00690064006500720000000000ffffffff0000004100fffffffb000000100044006900730070006c006100790073010000003d00000123000000c900fffffffb0000001c004d006f00740069006f006e0050006c0061006e006e0069006e00670100000166000001910000018800fffffffb0000000800480065006c0070000000029a0000006e0000006e00fffffffb0000000a0056006900650077007301000002fd000000b5000000a400ffffff000001f60000037500000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Width: 1200
