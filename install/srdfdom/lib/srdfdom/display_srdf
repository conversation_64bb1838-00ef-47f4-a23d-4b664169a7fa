#!/usr/bin/env python3
from __future__ import print_function
import argparse

from srdfdom.srdf import SRDF

parser = argparse.ArgumentParser(usage="Load an SRDF file")
parser.add_argument(
    "file",
    type=argparse.FileType("r"),
    help="File to load. Use - for stdin",
)
parser.add_argument(
    "-o", "--output", type=argparse.FileType("w"), default=None, help="Dump file to XML"
)
args = parser.parse_args()

robot = SRDF.from_xml_string(args.file.read())

print(robot)

if args.output is not None:
    args.output.write(robot.to_xml_string())
