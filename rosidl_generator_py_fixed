#!/usr/bin/env python3

import argparse
import os
import sys

# Add the ROS2 Python path explicitly
sys.path.insert(0, '/opt/ros/humble/local/lib/python3.10/dist-packages')

try:
    from rosidl_generator_py import generate_py
except ImportError:
    try:
        from rosidl_generator_py.generate_py_impl import generate_py
    except ImportError:
        # modifying sys.path and importing the Python package with the same
        # name as this script does not work on Windows
        rosidl_generator_py_root = os.path.dirname(os.path.dirname(__file__))
        rosidl_generator_py_module = os.path.join(
            rosidl_generator_py_root, 'rosidl_generator_py', '__init__.py')
        if not os.path.exists(rosidl_generator_py_module):
            rosidl_generator_py_root = '/opt/ros/humble/local/lib/python3.10/dist-packages'
            rosidl_generator_py_module = os.path.join(
                rosidl_generator_py_root, 'rosidl_generator_py', '__init__.py')
        if os.path.exists(rosidl_generator_py_module):
            sys.path.insert(0, rosidl_generator_py_root)
            from rosidl_generator_py import generate_py


def main(argv=sys.argv[1:]):
    parser = argparse.ArgumentParser(description='Generate the Python ROS interfaces.')
    parser.add_argument(
        '--generator-arguments-file',
        required=True,
        help='The location of the file containing the generator arguments')
    parser.add_argument(
        '--typesupport-impls',
        required=True,
        help='All the available typesupport implementations')
    args = parser.parse_args(argv)

    generate_py(args.generator_arguments_file, args.typesupport_impls.split(';'))
    return 0

if __name__ == '__main__':
    sys.exit(main())
