-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.5s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit
-- Install configuration: "Release"
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/ament_index/resource_index/package_run_dependencies/moveit
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/ament_index/resource_index/parent_prefix_path/moveit
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/environment/path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/local_setup.bash
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/local_setup.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/package.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/ament_index/resource_index/packages/moveit
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/cmake/moveitConfig.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/cmake/moveitConfig-version.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit/share/moveit/package.xml
