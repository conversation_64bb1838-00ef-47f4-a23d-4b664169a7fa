-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
-- Found moveit_ros_visualization: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread date_time system filesystem
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found FastRTPS: /opt/ros/humble/include
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found Threads: TRUE
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found Eigen3: TRUE (found version "3.4.0")
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
-- Setting OGRE_DIR to: '/opt/ros/humble/share/rviz_ogre_vendor/cmake/../../../opt/rviz_ogre_vendor/lib/OGRE/cmake'
-- Found OGRE
--   static     : OFF
--   components : HLMS;MeshLodGenerator;Overlay;Paging;Property;RTShaderSystem;Terrain;Volume
--   plugins    : Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
--   media      : /opt/ros/humble/opt/rviz_ogre_vendor/share/OGRE/Media
-- OGRE_LIBRARIES: OgreHLMS;OgreMeshLodGenerator;OgreOverlay;OgrePaging;OgreProperty;OgreRTShaderSystem;OgreTerrain;OgreVolume;OgreMain
-- OGRE_LIBRARY_DIRS: /opt/ros/humble/opt/rviz_ogre_vendor/lib
-- OGRE_PLUGINS: Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
-- OGRE_PLUGIN_DIR: /opt/ros/humble/opt/rviz_ogre_vendor/lib/OGRE
-- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
-- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
-- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
-- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
-- Could not find freeimage library
-- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")
-- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so
-- Found X11: /usr/include
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
-- Looking for gethostbyname
-- Looking for gethostbyname - found
-- Looking for connect
-- Looking for connect - found
-- Looking for remove
-- Looking for remove - found
-- Looking for shmat
-- Looking for shmat - found
-- Looking for IceConnectionNumber in ICE
-- Looking for IceConnectionNumber in ICE - found
-- Setting assimp_DIR to: '/opt/ros/humble/share/rviz_assimp_vendor/cmake/../../../opt/rviz_assimp_vendor/lib/cmake/assimp-4.1'
-- Found moveit_setup_framework: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done (3.6s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_setup_app_plugins
[  0%] Built target moveit_setup_app_plugins_autogen_timestamp_deps
[  0%] Built target gtest_autogen_timestamp_deps
[  0%] Built target gtest_main_autogen_timestamp_deps
[  3%] [34m[1mAutomatic MOC for target moveit_setup_app_plugins[0m
[  7%] [34m[1mAutomatic MOC for target gtest[0m
[ 11%] [34m[1mAutomatic MOC for target gtest_main[0m
[ 11%] Built target moveit_setup_app_plugins_autogen
[ 14%] [34m[1mGenerating include/moveit_setup_app_plugins/moc_perception_widget.cpp[0m
[ 18%] [34m[1mGenerating include/moveit_setup_app_plugins/moc_launches_widget.cpp[0m
[ 18%] Built target gtest_autogen
[ 18%] Built target gtest_main_autogen
[ 22%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/src/gtest-all.cc.o[0m
[ 25%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/gtest_autogen/mocs_compilation.cpp.o[0m
[ 29%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/src/launches_widget.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/src/launches_config.cpp.o[0m
[ 37%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/moveit_setup_app_plugins_autogen/mocs_compilation.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/src/launches.cpp.o[0m
[ 44%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/gtest_main_autogen/mocs_compilation.cpp.o[0m
[ 48%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/src/perception_config.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/src/perception.cpp.o[0m
[ 59%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/src/perception_widget.cpp.o[0m
[ 62%] [32m[1mLinking CXX static library libgtest_main.a[0m
[ 66%] Built target gtest_main
[ 70%] [32m[1mLinking CXX static library libgtest.a[0m
[ 74%] Built target gtest
[ 77%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/include/moveit_setup_app_plugins/moc_perception_widget.cpp.o[0m
[ 81%] [32mBuilding CXX object CMakeFiles/moveit_setup_app_plugins.dir/include/moveit_setup_app_plugins/moc_launches_widget.cpp.o[0m
[ 85%] [32m[1mLinking CXX shared library libmoveit_setup_app_plugins.so[0m
[ 85%] Built target moveit_setup_app_plugins
[ 85%] Built target test_perception_autogen_timestamp_deps
[ 88%] [34m[1mAutomatic MOC for target test_perception[0m
[ 88%] Built target test_perception_autogen
[ 92%] [32mBuilding CXX object CMakeFiles/test_perception.dir/test_perception_autogen/mocs_compilation.cpp.o[0m
[ 96%] [32mBuilding CXX object CMakeFiles/test_perception.dir/test/test_perception.cpp.o[0m
[100%] [32m[1mLinking CXX executable test_perception[0m
[100%] Built target test_perception
-- Install configuration: "Release"
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/config
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/config/moveit.rviz
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/config/sensors_3d.yaml
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/launch
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/launch/generic.launch.py.template
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib/libmoveit_setup_app_plugins.so
-- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib/libmoveit_setup_app_plugins.so" to ""
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/moveit_setup_framework_plugins.xml
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launch_bundle.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launches.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launches_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launches_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/perception.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/perception_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/perception_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/moveit_setup_framework_plugins.xml
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/package_run_dependencies/moveit_setup_app_plugins
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/parent_prefix_path/moveit_setup_app_plugins
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.bash
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/package.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/packages/moveit_setup_app_plugins
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/moveit_setup_framework__pluginlib__plugin/moveit_setup_app_plugins
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/export_moveit_setup_app_pluginsExport.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/export_moveit_setup_app_pluginsExport-release.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/moveit_setup_app_pluginsConfig.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/moveit_setup_app_pluginsConfig-version.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/package.xml
