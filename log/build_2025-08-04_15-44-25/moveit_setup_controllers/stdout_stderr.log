-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
-- Found moveit_setup_framework: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found FastRTPS: /opt/ros/humble/include
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Found Eigen3: TRUE (found version "3.4.0")
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread date_time system filesystem
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:11 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
-- Setting OGRE_DIR to: '/opt/ros/humble/share/rviz_ogre_vendor/cmake/../../../opt/rviz_ogre_vendor/lib/OGRE/cmake'
-- Found OGRE
--   static     : OFF
--   components : HLMS;MeshLodGenerator;Overlay;Paging;Property;RTShaderSystem;Terrain;Volume
--   plugins    : Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
--   media      : /opt/ros/humble/opt/rviz_ogre_vendor/share/OGRE/Media
-- OGRE_LIBRARIES: OgreHLMS;OgreMeshLodGenerator;OgreOverlay;OgrePaging;OgreProperty;OgreRTShaderSystem;OgreTerrain;OgreVolume;OgreMain
-- OGRE_LIBRARY_DIRS: /opt/ros/humble/opt/rviz_ogre_vendor/lib
-- OGRE_PLUGINS: Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
-- OGRE_PLUGIN_DIR: /opt/ros/humble/opt/rviz_ogre_vendor/lib/OGRE
-- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
-- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
-- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
-- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
-- Could not find freeimage library
-- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")
-- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so
-- Found X11: /usr/include
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
-- Looking for gethostbyname
-- Looking for gethostbyname - found
-- Looking for connect
-- Looking for connect - found
-- Looking for remove
-- Looking for remove - found
-- Looking for shmat
-- Looking for shmat - found
-- Looking for IceConnectionNumber in ICE
-- Looking for IceConnectionNumber in ICE - found
-- Setting assimp_DIR to: '/opt/ros/humble/share/rviz_assimp_vendor/cmake/../../../opt/rviz_assimp_vendor/lib/cmake/assimp-4.1'
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done (3.6s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_setup_controllers
[  0%] Built target gtest_main_autogen_timestamp_deps
[  0%] Built target moveit_setup_controllers_autogen_timestamp_deps
[  0%] Built target gtest_autogen_timestamp_deps
[  2%] [34m[1mAutomatic MOC for target gtest[0m
[  5%] [34m[1mAutomatic MOC for target moveit_setup_controllers[0m
[  8%] [34m[1mAutomatic MOC for target gtest_main[0m
[  8%] Built target gtest_autogen
[  8%] Built target gtest_main_autogen
[  8%] Built target moveit_setup_controllers_autogen
[ 11%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/gtest_autogen/mocs_compilation.cpp.o[0m
[ 14%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/gtest_main_autogen/mocs_compilation.cpp.o[0m
[ 20%] [34m[1mGenerating include/moveit_setup_controllers/moc_urdf_modifications_widget.cpp[0m
[ 17%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/src/gtest-all.cc.o[0m
[ 22%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o[0m
[ 25%] [34m[1mGenerating include/moveit_setup_controllers/moc_controller_edit_widget.cpp[0m
[ 28%] [34m[1mGenerating include/moveit_setup_controllers/moc_controllers_widget.cpp[0m
[ 31%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/moveit_setup_controllers_autogen/mocs_compilation.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/controllers.cpp.o[0m
[ 37%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/control_xacro_config.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/controller_edit_widget.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/controllers_config.cpp.o[0m
[ 45%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/modified_urdf_config.cpp.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/controllers_widget.cpp.o[0m
[ 51%] [32m[1mLinking CXX static library libgtest_main.a[0m
[ 54%] Built target gtest_main
[ 57%] [32m[1mLinking CXX static library libgtest.a[0m
[ 60%] Built target gtest
[ 62%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/include/moveit_setup_controllers/moc_controller_edit_widget.cpp.o[0m
[ 65%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/include/moveit_setup_controllers/moc_controllers_widget.cpp.o[0m
[ 68%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/include/moveit_setup_controllers/moc_urdf_modifications_widget.cpp.o[0m
[ 71%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/urdf_modifications_widget.cpp.o[0m
[ 74%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/urdf_modifications.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/ros2_controllers_widget.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/moveit_controllers_widget.cpp.o[0m
[ 82%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/ros2_controllers_config.cpp.o[0m
[ 85%] [32mBuilding CXX object CMakeFiles/moveit_setup_controllers.dir/src/moveit_controllers_config.cpp.o[0m
[ 88%] [32m[1mLinking CXX shared library libmoveit_setup_controllers.so[0m
[ 88%] Built target moveit_setup_controllers
[ 88%] Built target test_controllers_autogen_timestamp_deps
[ 91%] [34m[1mAutomatic MOC for target test_controllers[0m
[ 91%] Built target test_controllers_autogen
[ 94%] [32mBuilding CXX object CMakeFiles/test_controllers.dir/test_controllers_autogen/mocs_compilation.cpp.o[0m
[ 97%] [32mBuilding CXX object CMakeFiles/test_controllers.dir/test/test_controllers.cpp.o[0m
[100%] [32m[1mLinking CXX executable test_controllers[0m
[100%] Built target test_controllers
-- Install configuration: "Release"
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/templates
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/templates/config
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/templates/config/gazebo_controllers.yaml
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/templates/config/modified.urdf.xacro
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/templates/config/ros2_control.xacro
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib/libmoveit_setup_controllers.so
-- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib/libmoveit_setup_controllers.so" to ""
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/moveit_setup_framework_plugins.xml
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/control_xacro_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/controller_edit_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/controllers.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/controllers_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/controllers_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/included_xacro_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/modified_urdf_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/moveit_controllers.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/moveit_controllers_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/ros2_controllers.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/ros2_controllers_config.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/urdf_modifications.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/include/moveit_setup_controllers/urdf_modifications_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/launch
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/launch/control.launch.py
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/moveit_setup_framework_plugins.xml
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/ament_index/resource_index/package_run_dependencies/moveit_setup_controllers
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/ament_index/resource_index/parent_prefix_path/moveit_setup_controllers
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/environment/path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/local_setup.bash
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/local_setup.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/package.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/ament_index/resource_index/packages/moveit_setup_controllers
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/ament_index/resource_index/moveit_setup_framework__pluginlib__plugin/moveit_setup_controllers
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/export_moveit_setup_controllersExport.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/export_moveit_setup_controllersExport-release.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/moveit_setup_controllersConfig.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/cmake/moveit_setup_controllersConfig-version.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/package.xml
