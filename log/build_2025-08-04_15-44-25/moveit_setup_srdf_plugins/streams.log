[0.054s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins
[0.140s] -- The C compiler identification is GNU 11.4.0
[0.226s] -- The CXX compiler identification is GNU 11.4.0
[0.232s] -- Detecting C compiler ABI info
[0.301s] -- Detecting C compiler ABI info - done
[0.324s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.324s] -- Detecting C compile features
[0.324s] -- Detecting C compile features - done
[0.337s] -- Detecting CXX compiler ABI info
[0.400s] -- Detecting CXX compiler ABI info - done
[0.412s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.412s] -- Detecting CXX compile features
[0.413s] -- Detecting CXX compile features - done
[0.422s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.426s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.540s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.727s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[0.738s] -- Found moveit_setup_framework: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake)
[0.796s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.804s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.816s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.829s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.854s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.919s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.924s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.011s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.046s] -- Found FastRTPS: /opt/ros/humble/include
[1.095s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.109s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.200s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.202s] -- Found Threads: TRUE
[1.272s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.273s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.273s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.273s]   set the policy and suppress this warning.
[1.273s] 
[1.273s] Call Stack (most recent call first):
[1.273s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.273s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.273s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[1.273s]   CMakeLists.txt:10 (find_package)
[1.273s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.273s] [0m
[1.324s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.422s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.473s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.544s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.545s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.545s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.545s]   set the policy and suppress this warning.
[1.546s] 
[1.548s] Call Stack (most recent call first):
[1.548s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.549s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.549s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.549s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.549s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[1.549s]   CMakeLists.txt:10 (find_package)
[1.549s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.549s] [0m
[1.554s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.729s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.836s] -- Found Eigen3: TRUE (found version "3.4.0")
[1.836s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[2.429s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[2.430s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.430s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.430s]   set the policy and suppress this warning.
[2.430s] 
[2.430s] Call Stack (most recent call first):
[2.430s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[2.430s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.430s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.430s]   CMakeLists.txt:10 (find_package)
[2.430s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.431s] [0m
[2.433s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[2.462s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ConfigExtras.cmake:3 (find_package):
[2.462s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.462s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.462s]   set the policy and suppress this warning.
[2.462s] 
[2.462s] Call Stack (most recent call first):
[2.462s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.462s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.463s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.463s]   CMakeLists.txt:10 (find_package)
[2.463s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.463s] [0m
[2.467s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread date_time system filesystem
[2.480s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[2.480s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.480s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.481s]   set the policy and suppress this warning.
[2.481s] 
[2.481s] Call Stack (most recent call first):
[2.481s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.481s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.481s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.481s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.481s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.481s]   CMakeLists.txt:10 (find_package)
[2.481s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.481s] [0m
[2.491s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[2.502s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[2.502s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.502s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.502s]   set the policy and suppress this warning.
[2.502s] 
[2.502s] Call Stack (most recent call first):
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.502s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.502s]   CMakeLists.txt:10 (find_package)
[2.503s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.503s] [0m
[2.509s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[2.534s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[2.535s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.536s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.536s]   set the policy and suppress this warning.
[2.536s] 
[2.536s] Call Stack (most recent call first):
[2.536s]   /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[2.536s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.536s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.536s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.536s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.537s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.537s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.537s]   CMakeLists.txt:10 (find_package)
[2.537s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.537s] [0m
[2.542s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[2.656s] -- Setting OGRE_DIR to: '/opt/ros/humble/share/rviz_ogre_vendor/cmake/../../../opt/rviz_ogre_vendor/lib/OGRE/cmake'
[2.657s] -- Found OGRE
[2.657s] --   static     : OFF
[2.658s] --   components : HLMS;MeshLodGenerator;Overlay;Paging;Property;RTShaderSystem;Terrain;Volume
[2.658s] --   plugins    : Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
[2.658s] --   media      : /opt/ros/humble/opt/rviz_ogre_vendor/share/OGRE/Media
[2.658s] -- OGRE_LIBRARIES: OgreHLMS;OgreMeshLodGenerator;OgreOverlay;OgrePaging;OgreProperty;OgreRTShaderSystem;OgreTerrain;OgreVolume;OgreMain
[2.658s] -- OGRE_LIBRARY_DIRS: /opt/ros/humble/opt/rviz_ogre_vendor/lib
[2.658s] -- OGRE_PLUGINS: Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
[2.658s] -- OGRE_PLUGIN_DIR: /opt/ros/humble/opt/rviz_ogre_vendor/lib/OGRE
[2.658s] -- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
[2.659s] -- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
[2.659s] -- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
[2.659s] -- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
[2.660s] -- Could not find freeimage library
[2.697s] -- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")
[2.716s] -- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so
[2.948s] -- Found X11: /usr/include
[2.949s] -- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
[3.049s] -- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
[3.049s] -- Looking for gethostbyname
[3.144s] -- Looking for gethostbyname - found
[3.145s] -- Looking for connect
[3.230s] -- Looking for connect - found
[3.230s] -- Looking for remove
[3.359s] -- Looking for remove - found
[3.359s] -- Looking for shmat
[3.440s] -- Looking for shmat - found
[3.440s] -- Looking for IceConnectionNumber in ICE
[3.513s] -- Looking for IceConnectionNumber in ICE - found
[3.535s] -- Setting assimp_DIR to: '/opt/ros/humble/share/rviz_assimp_vendor/cmake/../../../opt/rviz_assimp_vendor/lib/cmake/assimp-4.1'
[3.641s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[3.669s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[3.671s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[3.671s]   Compatibility with CMake < 3.10 will be removed from a future version of
[3.671s]   CMake.
[3.671s] 
[3.671s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[3.671s]   to tell CMake that the project requires at least <min> but has been updated
[3.671s]   to work with policies introduced by <max> or earlier.
[3.671s] 
[3.672s] [0m
[3.679s] -- Added test 'lint_cmake' to check CMake code style
[3.679s] -- Added test 'xmllint' to check XML markup files
[3.684s] -- Configuring done (3.6s)
[3.759s] -- Generating done (0.1s)
[3.770s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins
[3.787s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins
[3.787s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins -- -j8 -l8
[3.813s] [  2%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_collision_matrix_model.cpp[0m
[3.813s] [  4%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_virtual_joints_widget.cpp[0m
[3.814s] [  6%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_collision_linear_model.cpp[0m
[3.817s] [  8%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_default_collisions_widget.cpp[0m
[3.818s] [ 10%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/src/gtest-all.cc.o[0m
[3.822s] [ 12%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_group_edit_widget.cpp[0m
[3.826s] [ 14%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o[0m
[3.826s] [ 16%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_passive_joints_widget.cpp[0m
[3.828s] [ 18%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_kinematic_chain_widget.cpp[0m
[3.828s] [ 20%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_end_effectors_widget.cpp[0m
[3.839s] [ 22%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_robot_poses_widget.cpp[0m
[3.839s] [ 25%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_planning_groups_widget.cpp[0m
[3.840s] [ 27%] [34m[1mGenerating include/moveit_setup_srdf_plugins/moc_rotated_header_view.cpp[0m
[3.850s] /home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/rotated_header_view.hpp:0: Note: No relevant classes found. No output generated.
[3.868s] [ 29%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/collision_matrix_model.cpp.o[0m
[3.868s] [ 31%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/collision_linear_model.cpp.o[0m
[3.874s] [ 33%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/default_collisions.cpp.o[0m
[3.874s] [ 35%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/compute_default_collisions.cpp.o[0m
[4.900s] [ 37%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/default_collisions_widget.cpp.o[0m
[4.912s] [ 39%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/end_effectors.cpp.o[0m
[9.163s] [ 41%] [32m[1mLinking CXX static library libgtest_main.a[0m
[9.270s] [ 41%] Built target gtest_main
[193.761s] [ 43%] [32m[1mLinking CXX static library libgtest.a[0m
[194.009s] [ 43%] Built target gtest
[1015.266s] [ 45%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/planning_groups.cpp.o[0m
[1028.609s] [ 47%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/planning_groups_widget.cpp.o[0m
[1040.508s] [ 50%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/robot_poses.cpp.o[0m
[1047.367s] [ 52%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/robot_poses_widget.cpp.o[0m
[1055.854s] [ 54%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/rotated_header_view.cpp.o[0m
[1056.524s] [ 56%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/virtual_joints.cpp.o[0m
[1061.831s] [ 58%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/virtual_joints_widget.cpp.o[0m
[1063.842s] [ 60%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_collision_linear_model.cpp.o[0m
[1063.842s] [ 62%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/passive_joints_widget.cpp.o[0m
[1064.441s] [ 64%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/passive_joints.cpp.o[0m
[1065.447s] [ 68%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/kinematic_chain_widget.cpp.o[0m
[1065.448s] [ 68%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_collision_matrix_model.cpp.o[0m
[1066.469s] [ 72%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/group_meta_config.cpp.o[0m
[1066.469s] [ 72%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/group_edit_widget.cpp.o[0m
[1066.548s] [ 75%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/src/end_effectors_widget.cpp.o[0m
[1067.641s] [ 77%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_default_collisions_widget.cpp.o[0m
[1182.147s] [ 79%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_rotated_header_view.cpp.o[0m
[1182.164s] [ 81%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_virtual_joints_widget.cpp.o[0m
[1191.615s] [ 83%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_robot_poses_widget.cpp.o[0m
[1200.552s] [ 87%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_planning_groups_widget.cpp.o[0m
[1200.552s] [ 87%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_passive_joints_widget.cpp.o[0m
[1208.076s] [ 93%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_group_edit_widget.cpp.o[0m
[1208.076s] [ 93%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_kinematic_chain_widget.cpp.o[0m
[1208.076s] [ 93%] [32mBuilding CXX object CMakeFiles/moveit_setup_srdf_plugins.dir/include/moveit_setup_srdf_plugins/moc_end_effectors_widget.cpp.o[0m
[1217.042s] [ 95%] [32m[1mLinking CXX shared library libmoveit_setup_srdf_plugins.so[0m
[1217.427s] [ 95%] Built target moveit_setup_srdf_plugins
[1217.436s] [ 97%] [32mBuilding CXX object CMakeFiles/test_srdf.dir/test/test_srdf.cpp.o[0m
[1226.588s] [100%] [32m[1mLinking CXX executable test_srdf[0m
[1227.298s] [100%] Built target test_srdf
[1227.320s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins -- -j8 -l8
[1227.336s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins
[1227.358s] -- Install configuration: "Release"
[1227.359s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib/libmoveit_setup_srdf_plugins.so
[1227.368s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib/libmoveit_setup_srdf_plugins.so" to ""
[1227.368s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/moveit_setup_framework_plugins.xml
[1227.369s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include
[1227.369s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins
[1227.369s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/collision_linear_model.hpp
[1227.369s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/collision_matrix_model.hpp
[1227.369s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/compute_default_collisions.hpp
[1227.369s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/default_collisions.hpp
[1227.370s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/default_collisions_widget.hpp
[1227.370s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/end_effectors.hpp
[1227.370s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/end_effectors_widget.hpp
[1227.370s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/group_edit_widget.hpp
[1227.370s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/group_meta_config.hpp
[1227.371s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/kinematic_chain_widget.hpp
[1227.371s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/passive_joints.hpp
[1227.371s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/passive_joints_widget.hpp
[1227.371s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/planning_groups.hpp
[1227.371s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/planning_groups_widget.hpp
[1227.372s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/robot_poses.hpp
[1227.372s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/robot_poses_widget.hpp
[1227.372s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/rotated_header_view.hpp
[1227.372s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/srdf_step.hpp
[1227.372s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/virtual_joints.hpp
[1227.372s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/virtual_joints_widget.hpp
[1227.373s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/library_path.sh
[1227.373s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/library_path.dsv
[1227.373s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/moveit_setup_framework_plugins.xml
[1227.373s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/package_run_dependencies/moveit_setup_srdf_plugins
[1227.373s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/parent_prefix_path/moveit_setup_srdf_plugins
[1227.373s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/ament_prefix_path.sh
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/ament_prefix_path.dsv
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/path.sh
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/path.dsv
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.bash
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.sh
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.zsh
[1227.374s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.dsv
[1227.375s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/package.dsv
[1227.375s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/packages/moveit_setup_srdf_plugins
[1227.375s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/moveit_setup_framework__pluginlib__plugin/moveit_setup_srdf_plugins
[1227.377s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/export_moveit_setup_srdf_pluginsExport.cmake
[1227.377s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/export_moveit_setup_srdf_pluginsExport-release.cmake
[1227.378s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/ament_cmake_export_include_directories-extras.cmake
[1227.378s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/ament_cmake_export_libraries-extras.cmake
[1227.378s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/ament_cmake_export_targets-extras.cmake
[1227.378s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/moveit_setup_srdf_pluginsConfig.cmake
[1227.378s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/moveit_setup_srdf_pluginsConfig-version.cmake
[1227.378s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/package.xml
[1227.378s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins
