[0.097s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_assistant': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_assistant -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_setup_assistant
[0.265s] -- The C compiler identification is GNU 11.4.0
[0.337s] -- The CXX compiler identification is GNU 11.4.0
[0.347s] -- Detecting C compiler ABI info
[0.385s] -- Detecting C compiler ABI info - done
[0.394s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.395s] -- Detecting C compile features
[0.395s] -- Detecting C compile features - done
[0.405s] -- Detecting CXX compiler ABI info
[0.448s] -- Detecting CXX compiler ABI info - done
[0.458s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.459s] -- Detecting CXX compile features
[0.459s] -- Detecting CXX compile features - done
[0.468s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.485s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.582s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.755s] -- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
[0.756s] [33mCMake Warning (dev) at CMakeLists.txt:11 (find_package):
[0.756s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.756s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.756s]   set the policy and suppress this warning.
[0.756s] 
[0.757s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.757s] [0m
[0.782s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: program_options
[0.784s] -- Found moveit_setup_framework: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake)
[0.852s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.861s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.874s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.891s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.910s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.974s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.980s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.173s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.201s] -- Found FastRTPS: /opt/ros/humble/include
[1.243s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.259s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.312s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.313s] -- Found Threads: TRUE
[1.383s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.383s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.383s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.383s]   set the policy and suppress this warning.
[1.383s] 
[1.383s] Call Stack (most recent call first):
[1.383s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.383s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.383s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[1.383s]   CMakeLists.txt:12 (find_package)
[1.383s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.383s] [0m
[1.422s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.487s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.506s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.531s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.531s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.531s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.531s]   set the policy and suppress this warning.
[1.531s] 
[1.531s] Call Stack (most recent call first):
[1.531s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.531s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.531s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.531s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.531s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[1.532s]   CMakeLists.txt:12 (find_package)
[1.532s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.532s] [0m
[1.534s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.627s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.701s] -- Found Eigen3: TRUE (found version "3.4.0")
[1.701s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.996s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[1.997s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.997s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.997s]   set the policy and suppress this warning.
[1.997s] 
[1.997s] Call Stack (most recent call first):
[1.997s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[1.997s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.997s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[1.997s]   CMakeLists.txt:12 (find_package)
[1.997s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.997s] [0m
[2.001s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[2.024s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ConfigExtras.cmake:3 (find_package):
[2.024s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.024s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.024s]   set the policy and suppress this warning.
[2.024s] 
[2.024s] Call Stack (most recent call first):
[2.024s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.024s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.024s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.024s]   CMakeLists.txt:12 (find_package)
[2.024s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.024s] [0m
[2.027s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread date_time system filesystem
[2.038s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[2.038s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.038s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.038s]   set the policy and suppress this warning.
[2.038s] 
[2.038s] Call Stack (most recent call first):
[2.038s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.038s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.038s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.038s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.038s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.038s]   CMakeLists.txt:12 (find_package)
[2.038s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.038s] [0m
[2.042s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[2.049s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[2.049s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.049s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.049s]   set the policy and suppress this warning.
[2.049s] 
[2.050s] Call Stack (most recent call first):
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.050s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.050s]   CMakeLists.txt:12 (find_package)
[2.050s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.050s] [0m
[2.053s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[2.072s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[2.072s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.072s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.072s]   set the policy and suppress this warning.
[2.072s] 
[2.072s] Call Stack (most recent call first):
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.072s]   /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
[2.072s]   CMakeLists.txt:12 (find_package)
[2.072s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.072s] [0m
[2.076s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[2.133s] -- Setting OGRE_DIR to: '/opt/ros/humble/share/rviz_ogre_vendor/cmake/../../../opt/rviz_ogre_vendor/lib/OGRE/cmake'
[2.135s] -- Found OGRE
[2.135s] --   static     : OFF
[2.135s] --   components : HLMS;MeshLodGenerator;Overlay;Paging;Property;RTShaderSystem;Terrain;Volume
[2.135s] --   plugins    : Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
[2.135s] --   media      : /opt/ros/humble/opt/rviz_ogre_vendor/share/OGRE/Media
[2.135s] -- OGRE_LIBRARIES: OgreHLMS;OgreMeshLodGenerator;OgreOverlay;OgrePaging;OgreProperty;OgreRTShaderSystem;OgreTerrain;OgreVolume;OgreMain
[2.135s] -- OGRE_LIBRARY_DIRS: /opt/ros/humble/opt/rviz_ogre_vendor/lib
[2.135s] -- OGRE_PLUGINS: Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
[2.135s] -- OGRE_PLUGIN_DIR: /opt/ros/humble/opt/rviz_ogre_vendor/lib/OGRE
[2.135s] -- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
[2.135s] -- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
[2.135s] -- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
[2.135s] -- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
[2.136s] -- Could not find freeimage library
[2.165s] -- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")
[2.173s] -- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so
[2.294s] -- Found X11: /usr/include
[2.295s] -- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
[2.356s] -- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
[2.357s] -- Looking for gethostbyname
[2.414s] -- Looking for gethostbyname - found
[2.414s] -- Looking for connect
[2.466s] -- Looking for connect - found
[2.466s] -- Looking for remove
[2.515s] -- Looking for remove - found
[2.515s] -- Looking for shmat
[2.567s] -- Looking for shmat - found
[2.567s] -- Looking for IceConnectionNumber in ICE
[2.614s] -- Looking for IceConnectionNumber in ICE - found
[2.634s] -- Setting assimp_DIR to: '/opt/ros/humble/share/rviz_assimp_vendor/cmake/../../../opt/rviz_assimp_vendor/lib/cmake/assimp-4.1'
[2.737s] -- Found moveit_setup_srdf_plugins: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake)
[2.758s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.792s] -- Added test 'lint_cmake' to check CMake code style
[2.792s] -- Added test 'xmllint' to check XML markup files
[2.794s] -- Configuring done (2.7s)
[2.871s] -- Generating done (0.1s)
[2.879s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_setup_assistant
[2.892s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_assistant' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_assistant -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_setup_assistant
[2.894s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_assistant': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_assistant -- -j8 -l8
[2.914s] [  0%] Built target moveit_setup_assistant_autogen_timestamp_deps
[2.914s] [  0%] Built target moveit_setup_assistant_updater_autogen_timestamp_deps
[2.920s] [ 14%] [34m[1mAutomatic MOC for target moveit_setup_assistant_updater[0m
[2.920s] [  7%] [34m[1mAutomatic MOC for target moveit_setup_assistant[0m
[2.940s] [ 14%] Built target moveit_setup_assistant_updater_autogen
[2.940s] [ 14%] Built target moveit_setup_assistant_autogen
[2.944s] [ 21%] [34m[1mGenerating include/moveit_setup_assistant/moc_setup_assistant_widget.cpp[0m
[2.945s] [ 28%] [34m[1mGenerating include/moveit_setup_assistant/moc_navigation_widget.cpp[0m
[2.949s] [ 42%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant_updater.dir/moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.o[0m
[2.950s] [ 42%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant_updater.dir/src/collisions_updater.cpp.o[0m
[2.958s] [ 50%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/moveit_setup_assistant_autogen/mocs_compilation.cpp.o[0m
[2.958s] [ 57%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/src/main.cpp.o[0m
[2.958s] [ 64%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/src/setup_assistant_widget.cpp.o[0m
[2.958s] [ 71%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/src/navigation_widget.cpp.o[0m
[2.959s] [ 78%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_navigation_widget.cpp.o[0m
[2.960s] [ 85%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.o[0m
[20.450s] [ 92%] [32m[1mLinking CXX executable collisions_updater[0m
[21.087s] [ 92%] Built target moveit_setup_assistant_updater
[23.272s] [100%] [32m[1mLinking CXX executable moveit_setup_assistant[0m
[23.937s] [100%] Built target moveit_setup_assistant
[23.950s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_assistant' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_assistant -- -j8 -l8
[23.952s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_assistant': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_assistant
[23.956s] -- Install configuration: "Release"
[23.956s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/moveit_setup_assistant
[23.956s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/moveit_setup_assistant" to ""
[23.956s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/collisions_updater
[23.957s] -- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/collisions_updater" to ""
[23.957s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include
[23.957s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include/moveit_setup_assistant
[23.957s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include/moveit_setup_assistant/navigation_widget.hpp
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include/moveit_setup_assistant/setup_assistant_widget.hpp
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/launch
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/launch/setup_assistant.launch.py
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/resources
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/resources/MoveIt_Setup_Assistant2.png
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/library_path.sh
[23.958s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/library_path.dsv
[23.959s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/ament_index/resource_index/package_run_dependencies/moveit_setup_assistant
[23.959s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/ament_index/resource_index/parent_prefix_path/moveit_setup_assistant
[23.959s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/ament_prefix_path.sh
[23.959s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/ament_prefix_path.dsv
[23.961s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/path.sh
[23.961s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/path.dsv
[23.961s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.bash
[23.961s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.sh
[23.962s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.zsh
[23.962s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.dsv
[23.962s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/package.dsv
[23.962s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/ament_index/resource_index/packages/moveit_setup_assistant
[23.962s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantTargetsExport.cmake
[23.963s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantTargetsExport-release.cmake
[23.963s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/ament_cmake_export_include_directories-extras.cmake
[23.963s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/ament_cmake_export_targets-extras.cmake
[23.963s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/ament_cmake_export_dependencies-extras.cmake
[23.963s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantConfig.cmake
[23.963s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantConfig-version.cmake
[23.964s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/package.xml
[23.968s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_assistant' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_assistant
