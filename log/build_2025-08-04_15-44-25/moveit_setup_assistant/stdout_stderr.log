-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Found ament_index_cpp: 1.4.0 (/opt/ros/humble/share/ament_index_cpp/cmake)
[33mCMake Warning (dev) at CMakeLists.txt:11 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: program_options
-- Found moveit_setup_framework: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found FastRTPS: /opt/ros/humble/include
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Found Eigen3: TRUE (found version "3.4.0")
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread date_time system filesystem
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/cmake/moveit_setup_frameworkConfig.cmake:41 (include)
  CMakeLists.txt:12 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
-- Setting OGRE_DIR to: '/opt/ros/humble/share/rviz_ogre_vendor/cmake/../../../opt/rviz_ogre_vendor/lib/OGRE/cmake'
-- Found OGRE
--   static     : OFF
--   components : HLMS;MeshLodGenerator;Overlay;Paging;Property;RTShaderSystem;Terrain;Volume
--   plugins    : Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
--   media      : /opt/ros/humble/opt/rviz_ogre_vendor/share/OGRE/Media
-- OGRE_LIBRARIES: OgreHLMS;OgreMeshLodGenerator;OgreOverlay;OgrePaging;OgreProperty;OgreRTShaderSystem;OgreTerrain;OgreVolume;OgreMain
-- OGRE_LIBRARY_DIRS: /opt/ros/humble/opt/rviz_ogre_vendor/lib
-- OGRE_PLUGINS: Plugin_BSPSceneManager;Plugin_OctreeSceneManager;Plugin_PCZSceneManager;Plugin_ParticleFX;RenderSystem_GL;RenderSystem_GL3Plus;Codec_STBI
-- OGRE_PLUGIN_DIR: /opt/ros/humble/opt/rviz_ogre_vendor/lib/OGRE
-- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
-- rviz_ogre_vendor::OgreOverlay for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so
-- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_RELEASE: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
-- rviz_ogre_vendor::OgreMain for IMPORTED_LOCATION_DEBUG: /opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so
-- Could not find freeimage library
-- Found ZLIB: /usr/lib/aarch64-linux-gnu/libz.so (found version "1.2.11")
-- Found OpenGL: /usr/lib/aarch64-linux-gnu/libOpenGL.so
-- Found X11: /usr/include
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so
-- Looking for XOpenDisplay in /usr/lib/aarch64-linux-gnu/libX11.so;/usr/lib/aarch64-linux-gnu/libXext.so - found
-- Looking for gethostbyname
-- Looking for gethostbyname - found
-- Looking for connect
-- Looking for connect - found
-- Looking for remove
-- Looking for remove - found
-- Looking for shmat
-- Looking for shmat - found
-- Looking for IceConnectionNumber in ICE
-- Looking for IceConnectionNumber in ICE - found
-- Setting assimp_DIR to: '/opt/ros/humble/share/rviz_assimp_vendor/cmake/../../../opt/rviz_assimp_vendor/lib/cmake/assimp-4.1'
-- Found moveit_setup_srdf_plugins: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done (2.7s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_setup_assistant
[  0%] Built target moveit_setup_assistant_autogen_timestamp_deps
[  0%] Built target moveit_setup_assistant_updater_autogen_timestamp_deps
[ 14%] [34m[1mAutomatic MOC for target moveit_setup_assistant_updater[0m
[  7%] [34m[1mAutomatic MOC for target moveit_setup_assistant[0m
[ 14%] Built target moveit_setup_assistant_updater_autogen
[ 14%] Built target moveit_setup_assistant_autogen
[ 21%] [34m[1mGenerating include/moveit_setup_assistant/moc_setup_assistant_widget.cpp[0m
[ 28%] [34m[1mGenerating include/moveit_setup_assistant/moc_navigation_widget.cpp[0m
[ 42%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant_updater.dir/moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant_updater.dir/src/collisions_updater.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/moveit_setup_assistant_autogen/mocs_compilation.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/src/main.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/src/setup_assistant_widget.cpp.o[0m
[ 71%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/src/navigation_widget.cpp.o[0m
[ 78%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_navigation_widget.cpp.o[0m
[ 85%] [32mBuilding CXX object CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.o[0m
[ 92%] [32m[1mLinking CXX executable collisions_updater[0m
[ 92%] Built target moveit_setup_assistant_updater
[100%] [32m[1mLinking CXX executable moveit_setup_assistant[0m
[100%] Built target moveit_setup_assistant
-- Install configuration: "Release"
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/moveit_setup_assistant
-- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/moveit_setup_assistant" to ""
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/collisions_updater
-- Set non-toolchain portion of runtime path of "/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib/moveit_setup_assistant/collisions_updater" to ""
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include/moveit_setup_assistant
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include/moveit_setup_assistant/navigation_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/include/moveit_setup_assistant/setup_assistant_widget.hpp
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/launch
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/launch/setup_assistant.launch.py
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/resources
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/resources/MoveIt_Setup_Assistant2.png
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/library_path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/ament_index/resource_index/package_run_dependencies/moveit_setup_assistant
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/ament_index/resource_index/parent_prefix_path/moveit_setup_assistant
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/environment/path.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.bash
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/package.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/ament_index/resource_index/packages/moveit_setup_assistant
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantTargetsExport.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantTargetsExport-release.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantConfig.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/cmake/moveit_setup_assistantConfig-version.cmake
-- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/package.xml
