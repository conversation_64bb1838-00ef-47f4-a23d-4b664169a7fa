[0.000000] (-) TimerEvent: {}
[0.000745] (-) JobUnselected: {'identifier': 'chomp_motion_planner'}
[0.000782] (-) JobUnselected: {'identifier': 'launch_param_builder'}
[0.000797] (-) JobUnselected: {'identifier': 'moveit'}
[0.000809] (-) JobUnselected: {'identifier': 'moveit2_tutorials'}
[0.000818] (-) JobUnselected: {'identifier': 'moveit_chomp_optimizer_adapter'}
[0.000826] (-) JobUnselected: {'identifier': 'moveit_common'}
[0.000834] (-) JobUnselected: {'identifier': 'moveit_configs_utils'}
[0.000841] (-) JobUnselected: {'identifier': 'moveit_core'}
[0.000848] (-) JobUnselected: {'identifier': 'moveit_hybrid_planning'}
[0.000857] (-) JobUnselected: {'identifier': 'moveit_kinematics'}
[0.000867] (-) JobUnselected: {'identifier': 'moveit_planners'}
[0.000875] (-) JobUnselected: {'identifier': 'moveit_planners_chomp'}
[0.000883] (-) JobUnselected: {'identifier': 'moveit_planners_ompl'}
[0.000890] (-) JobUnselected: {'identifier': 'moveit_plugins'}
[0.000897] (-) JobUnselected: {'identifier': 'moveit_resources'}
[0.000905] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_description'}
[0.000985] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_moveit_config'}
[0.001006] (-) JobUnselected: {'identifier': 'moveit_resources_panda_description'}
[0.001014] (-) JobUnselected: {'identifier': 'moveit_resources_panda_moveit_config'}
[0.001022] (-) JobUnselected: {'identifier': 'moveit_resources_pr2_description'}
[0.001030] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_ikfast_manipulator_plugin'}
[0.001038] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_moveit_config'}
[0.001048] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_pg70_support'}
[0.001056] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_support'}
[0.001063] (-) JobUnselected: {'identifier': 'moveit_ros'}
[0.001071] (-) JobUnselected: {'identifier': 'moveit_ros_benchmarks'}
[0.001078] (-) JobUnselected: {'identifier': 'moveit_ros_control_interface'}
[0.001085] (-) JobUnselected: {'identifier': 'moveit_ros_move_group'}
[0.001095] (-) JobUnselected: {'identifier': 'moveit_ros_occupancy_map_monitor'}
[0.001102] (-) JobUnselected: {'identifier': 'moveit_ros_perception'}
[0.001110] (-) JobUnselected: {'identifier': 'moveit_ros_planning'}
[0.001117] (-) JobUnselected: {'identifier': 'moveit_ros_planning_interface'}
[0.001124] (-) JobUnselected: {'identifier': 'moveit_ros_robot_interaction'}
[0.001131] (-) JobUnselected: {'identifier': 'moveit_ros_visualization'}
[0.001138] (-) JobUnselected: {'identifier': 'moveit_ros_warehouse'}
[0.001148] (-) JobUnselected: {'identifier': 'moveit_runtime'}
[0.001156] (-) JobUnselected: {'identifier': 'moveit_servo'}
[0.001163] (-) JobUnselected: {'identifier': 'moveit_setup_app_plugins'}
[0.001170] (-) JobUnselected: {'identifier': 'moveit_setup_assistant'}
[0.001177] (-) JobUnselected: {'identifier': 'moveit_setup_controllers'}
[0.001184] (-) JobUnselected: {'identifier': 'moveit_setup_core_plugins'}
[0.001289] (-) JobUnselected: {'identifier': 'moveit_setup_framework'}
[0.001331] (-) JobUnselected: {'identifier': 'moveit_setup_srdf_plugins'}
[0.001366] (-) JobUnselected: {'identifier': 'moveit_simple_controller_manager'}
[0.001550] (-) JobUnselected: {'identifier': 'moveit_task_constructor_capabilities'}
[0.001565] (-) JobUnselected: {'identifier': 'moveit_task_constructor_core'}
[0.001573] (-) JobUnselected: {'identifier': 'moveit_task_constructor_demo'}
[0.001581] (-) JobUnselected: {'identifier': 'moveit_task_constructor_visualization'}
[0.001588] (-) JobUnselected: {'identifier': 'moveit_visual_tools'}
[0.001595] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner'}
[0.001605] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner_testutils'}
[0.001612] (-) JobUnselected: {'identifier': 'rosparam_shortcuts'}
[0.001620] (-) JobUnselected: {'identifier': 'rviz_marker_tools'}
[0.001627] (-) JobUnselected: {'identifier': 'srdfdom'}
[0.001638] (moveit_task_constructor_msgs) JobQueued: {'identifier': 'moveit_task_constructor_msgs', 'dependencies': OrderedDict()}
[0.001681] (moveit_task_constructor_msgs) JobStarted: {'identifier': 'moveit_task_constructor_msgs'}
[0.007129] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'cmake'}
[0.007541] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.088938] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.100452] (-) TimerEvent: {}
[0.183847] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.190343] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.200678] (-) TimerEvent: {}
[0.240098] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.251023] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.251306] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.251819] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.259654] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.301066] (-) TimerEvent: {}
[0.324446] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.334894] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.335199] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.335696] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.346063] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.401818] (-) TimerEvent: {}
[0.443621] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.504301] (-) TimerEvent: {}
[0.550713] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)\n'}
[0.599720] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.605449] (-) TimerEvent: {}
[0.609301] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.622659] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.645037] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.669341] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.692983] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)\n'}
[0.706907] (-) TimerEvent: {}
[0.811402] (-) TimerEvent: {}
[0.857478] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.873971] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[0.914001] (-) TimerEvent: {}
[1.014857] (-) TimerEvent: {}
[1.115161] (-) TimerEvent: {}
[1.220971] (-) TimerEvent: {}
[1.264332] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[1.326078] (-) TimerEvent: {}
[1.428489] (-) TimerEvent: {}
[1.529558] (-) TimerEvent: {}
[1.631853] (-) TimerEvent: {}
[1.733855] (-) TimerEvent: {}
[1.839133] (-) TimerEvent: {}
[1.944395] (-) TimerEvent: {}
[1.971046] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[2.044688] (-) TimerEvent: {}
[2.149983] (-) TimerEvent: {}
[2.249685] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.250087] (-) TimerEvent: {}
[2.251599] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:20 (find_package):\n'}
[2.251774] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[2.251819] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[2.251846] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[2.251870] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[2.251893] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.251917] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[2.251941] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[2.251979] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[2.252003] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.252026] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[2.276318] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found PythonInterp: /usr/local/bin/python3.6 (found suitable version "3.6.15", minimum required is "3.6")\n'}
[2.281724] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[2.282072] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):\n'}
[2.282123] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[2.282154] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[2.282180] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[2.282204] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[2.282228] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.282253] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)\n'}
[2.282278] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[2.282311] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[2.282345] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[2.282369] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.282393] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[2.296647] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):\n'}
[2.296730] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[2.296761] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[2.296788] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[2.296813] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[2.296838] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.296863] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)\n'}
[2.296920] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[2.296959] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[2.296988] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[2.297014] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.297040] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[2.346073] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/aarch64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5")\n'}
[2.346199] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/local/bin/python3.6\n'}
[2.346232] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[2.346259] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so\n'}
[2.350166] (-) TimerEvent: {}
[2.360289] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found PythonExtra: .so\n'}
[2.453268] (-) TimerEvent: {}
[2.515341] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Configuring done (2.5s)\n'}
[2.553772] (-) TimerEvent: {}
[2.578130] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[2.589567] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs\n'}
[2.602162] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 0}
[2.602469] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'build'}
[2.602492] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[2.622762] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[2.625964] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[2.627347] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs\n'}
[2.658875] (-) TimerEvent: {}
[2.746214] (moveit_task_constructor_msgs) StdoutLine: {'line': b'running egg_info\n'}
[2.746563] (moveit_task_constructor_msgs) StdoutLine: {'line': b'creating moveit_task_constructor_msgs.egg-info\n'}
[2.746615] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing moveit_task_constructor_msgs.egg-info/PKG-INFO\n'}
[2.746746] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt\n'}
[2.746777] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt\n'}
[2.747441] (moveit_task_constructor_msgs) StdoutLine: {'line': b"writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[2.748344] (moveit_task_constructor_msgs) StdoutLine: {'line': b"reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[2.748588] (moveit_task_constructor_msgs) StdoutLine: {'line': b"writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[2.760475] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg\n'}
[2.760587] (-) TimerEvent: {}
[2.862657] (-) TimerEvent: {}
[2.966316] (-) TimerEvent: {}
[3.069567] (-) TimerEvent: {}
[3.173016] (-) TimerEvent: {}
[3.274246] (-) TimerEvent: {}
[3.375182] (-) TimerEvent: {}
[3.379598] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] Built target moveit_task_constructor_msgs__cpp\n'}
[3.384861] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[3.386256] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[3.386359] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  3%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[3.404092] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/solution__functions.c.o\x1b[0m\n'}
[3.405655] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/property__functions.c.o\x1b[0m\n'}
[3.405752] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__functions.c.o\x1b[0m\n'}
[3.407141] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/stage_description__functions.c.o\x1b[0m\n'}
[3.407694] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/solution_info__functions.c.o\x1b[0m\n'}
[3.478000] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/sub_solution__functions.c.o\x1b[0m\n'}
[3.478132] (-) TimerEvent: {}
[3.492493] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__functions.c.o\x1b[0m\n'}
[3.500141] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/task_description__functions.c.o\x1b[0m\n'}
[3.504606] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/task_statistics__functions.c.o\x1b[0m\n'}
[3.504728] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__functions.c.o\x1b[0m\n'}
[3.568254] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/srv/detail/get_solution__functions.c.o\x1b[0m\n'}
[3.570259] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__functions.c.o\x1b[0m\n'}
[3.579144] (-) TimerEvent: {}
[3.679565] (-) TimerEvent: {}
[3.781305] (-) TimerEvent: {}
[3.814287] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 14%] \x1b[32m\x1b[1mLinking C shared library libmoveit_task_constructor_msgs__rosidl_generator_c.so\x1b[0m\n'}
[3.843148] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 14%] Built target moveit_task_constructor_msgs__rosidl_generator_c\n'}
[3.848401] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 15%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[3.848542] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[3.858214] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[3.882136] (-) TimerEvent: {}
[3.923295] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/property__type_support.cpp.o\x1b[0m\n'}
[3.926984] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 19%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/solution_info__type_support.cpp.o\x1b[0m\n'}
[3.927147] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/solution__type_support.cpp.o\x1b[0m\n'}
[3.982210] (-) TimerEvent: {}
[4.045044] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/property__type_support.cpp.o\x1b[0m\n'}
[4.082366] (-) TimerEvent: {}
[4.146194] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 21%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/stage_description__type_support.cpp.o\x1b[0m\n'}
[4.179009] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution__type_support.cpp.o\x1b[0m\n'}
[4.183995] (-) TimerEvent: {}
[4.190551] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[4.215893] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/property__type_support.cpp.o\x1b[0m\n'}
[4.284162] (-) TimerEvent: {}
[4.367738] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/sub_solution__type_support.cpp.o\x1b[0m\n'}
[4.384238] (-) TimerEvent: {}
[4.392993] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/solution__type_support.cpp.o\x1b[0m\n'}
[4.419093] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution_info__type_support.cpp.o\x1b[0m\n'}
[4.484337] (-) TimerEvent: {}
[4.537781] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/property__type_support.cpp.o\x1b[0m\n'}
[4.562000] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/solution__type_support.cpp.o\x1b[0m\n'}
[4.581613] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[4.584408] (-) TimerEvent: {}
[4.599126] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/solution_info__type_support.cpp.o\x1b[0m\n'}
[4.611559] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_description__type_support.cpp.o\x1b[0m\n'}
[4.621435] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/stage_description__type_support.cpp.o\x1b[0m\n'}
[4.642581] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[4.659990] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/sub_solution__type_support.cpp.o\x1b[0m\n'}
[4.684507] (-) TimerEvent: {}
[4.706865] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[4.740279] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/task_description__type_support.cpp.o\x1b[0m\n'}
[4.763405] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/task_statistics__type_support.cpp.o\x1b[0m\n'}
[4.785889] (-) TimerEvent: {}
[4.789109] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[4.810183] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/property__type_support.c.o\x1b[0m\n'}
[4.810853] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/property__type_support_c.cpp.o\x1b[0m\n'}
[4.825071] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 39%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/srv/get_solution__type_support.cpp.o\x1b[0m\n'}
[4.825656] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/task_description__type_support.cpp.o\x1b[0m\n'}
[4.845247] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/solution__type_support.c.o\x1b[0m\n'}
[4.854855] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/action/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[4.884785] (-) TimerEvent: {}
[4.905338] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 43%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_c.so\x1b[0m\n'}
[4.918348] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/solution_info__type_support.c.o\x1b[0m\n'}
[4.951639] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 44%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c\n'}
[4.956986] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/solution_info__type_support.cpp.o\x1b[0m\n'}
[4.963548] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/stage_description__type_support.c.o\x1b[0m\n'}
[4.985014] (-) TimerEvent: {}
[4.996687] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support.c.o\x1b[0m\n'}
[5.032734] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support.c.o\x1b[0m\n'}
[5.069105] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/task_statistics__type_support.cpp.o\x1b[0m\n'}
[5.086032] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support.c.o\x1b[0m\n'}
[5.086790] (-) TimerEvent: {}
[5.108591] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution__type_support_c.cpp.o\x1b[0m\n'}
[5.115101] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[5.128195] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/task_description__type_support.c.o\x1b[0m\n'}
[5.186352] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support.c.o\x1b[0m\n'}
[5.188003] (-) TimerEvent: {}
[5.216427] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support.c.o\x1b[0m\n'}
[5.227402] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[5.253013] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/stage_description__type_support.cpp.o\x1b[0m\n'}
[5.258067] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/srv/detail/get_solution__type_support.c.o\x1b[0m\n'}
[5.288133] (-) TimerEvent: {}
[5.311270] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support.c.o\x1b[0m\n'}
[5.352604] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/srv/get_solution__type_support.cpp.o\x1b[0m\n'}
[5.388242] (-) TimerEvent: {}
[5.399231] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 58%] \x1b[32m\x1b[1mLinking C shared library libmoveit_task_constructor_msgs__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[5.431305] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 58%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c\n'}
[5.435386] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support.cpp.o\x1b[0m\n'}
[5.436272] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution_info__type_support_c.cpp.o\x1b[0m\n'}
[5.439411] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/action/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[5.460115] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[5.488338] (-) TimerEvent: {}
[5.563622] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support_c.cpp.o\x1b[0m\n'}
[5.566093] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_description__type_support_c.cpp.o\x1b[0m\n'}
[5.588439] (-) TimerEvent: {}
[5.647445] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[5.688537] (-) TimerEvent: {}
[5.726165] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/sub_solution__type_support.cpp.o\x1b[0m\n'}
[5.774610] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_description__type_support.cpp.o\x1b[0m\n'}
[5.788645] (-) TimerEvent: {}
[5.802587] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 65%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_cpp.so\x1b[0m\n'}
[5.811534] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support_c.cpp.o\x1b[0m\n'}
[5.838491] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 67%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support_c.cpp.o\x1b[0m\n'}
[5.842805] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 67%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp\n'}
[5.847484] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 68%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[5.867554] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support.cpp.o\x1b[0m\n'}
[5.889646] (-) TimerEvent: {}
[5.989940] (-) TimerEvent: {}
[6.090224] (-) TimerEvent: {}
[6.148180] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_description__type_support_c.cpp.o\x1b[0m\n'}
[6.173998] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/task_description__type_support.cpp.o\x1b[0m\n'}
[6.190333] (-) TimerEvent: {}
[6.280443] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[6.290909] (-) TimerEvent: {}
[6.319662] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/task_statistics__type_support.cpp.o\x1b[0m\n'}
[6.343019] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/srv/detail/get_solution__type_support.cpp.o\x1b[0m\n'}
[6.389112] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[6.391237] (-) TimerEvent: {}
[6.480173] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support_c.cpp.o\x1b[0m\n'}
[6.492013] (-) TimerEvent: {}
[6.539963] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support_c.cpp.o\x1b[0m\n'}
[6.593012] (-) TimerEvent: {}
[6.615000] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[6.627601] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/srv/detail/get_solution__type_support_c.cpp.o\x1b[0m\n'}
[6.695010] (-) TimerEvent: {}
[6.728042] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support_c.cpp.o\x1b[0m\n'}
[6.795194] (-) TimerEvent: {}
[6.798853] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/srv/detail/dds_fastrtps/get_solution__type_support.cpp.o\x1b[0m\n'}
[6.807773] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/action/detail/dds_fastrtps/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[6.895281] (-) TimerEvent: {}
[6.996381] (-) TimerEvent: {}
[7.053034] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 81%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[7.096485] (-) TimerEvent: {}
[7.098258] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 81%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c\n'}
[7.200995] (-) TimerEvent: {}
[7.239504] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 82%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[7.286731] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 82%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[7.302086] (-) TimerEvent: {}
[7.401858] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[7.402144] (-) TimerEvent: {}
[7.439343] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp\n'}
[7.446190] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs\n'}
[7.453567] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 84%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[7.503741] (-) TimerEvent: {}
[7.504045] (moveit_task_constructor_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[7.504138] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 11, in <module>\n'}
[7.504169] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_generator_py import generate_py\n'}
[7.504196] (moveit_task_constructor_msgs) StderrLine: {'line': b"ImportError: cannot import name 'generate_py'\n"}
[7.504222] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[7.504247] (moveit_task_constructor_msgs) StderrLine: {'line': b'During handling of the above exception, another exception occurred:\n'}
[7.504274] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[7.504298] (moveit_task_constructor_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[7.504328] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 14, in <module>\n'}
[7.504360] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_generator_py.generate_py_impl import generate_py\n'}
[7.504385] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_generator_py/generate_py_impl.py", line 21, in <module>\n'}
[7.504411] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_cmake import convert_camel_case_to_lower_case_underscore\n'}
[7.504436] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_cmake/__init__.py", line 22, in <module>\n'}
[7.504462] (moveit_task_constructor_msgs) StderrLine: {'line': b'    import em\n'}
[7.504487] (moveit_task_constructor_msgs) StderrLine: {'line': b"ModuleNotFoundError: No module named 'em'\n"}
[7.504512] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[7.504536] (moveit_task_constructor_msgs) StderrLine: {'line': b'During handling of the above exception, another exception occurred:\n'}
[7.504561] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[7.504586] (moveit_task_constructor_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[7.504610] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 27, in <module>\n'}
[7.504636] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_generator_py import generate_py\n'}
[7.504660] (moveit_task_constructor_msgs) StderrLine: {'line': b"ImportError: cannot import name 'generate_py'\n"}
[7.508742] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[2]: *** [/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/build.make:334: rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c] Error 1\n'}
[7.508916] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:725: moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/all] Error 2\n'}
[7.509098] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[7.510827] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 2}
[7.516083] (moveit_task_constructor_msgs) JobEnded: {'identifier': 'moveit_task_constructor_msgs', 'rc': 2}
[7.526512] (-) EventReactorShutdown: {}
