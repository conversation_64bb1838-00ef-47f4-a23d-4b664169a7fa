[0.000000] (-) TimerEvent: {}
[0.000621] (-) JobUnselected: {'identifier': 'chomp_motion_planner'}
[0.000644] (-) JobUnselected: {'identifier': 'launch_param_builder'}
[0.000653] (-) JobUnselected: {'identifier': 'moveit'}
[0.000661] (-) JobUnselected: {'identifier': 'moveit2_tutorials'}
[0.000669] (-) JobUnselected: {'identifier': 'moveit_chomp_optimizer_adapter'}
[0.000676] (-) JobUnselected: {'identifier': 'moveit_common'}
[0.000684] (-) JobUnselected: {'identifier': 'moveit_configs_utils'}
[0.000691] (-) JobUnselected: {'identifier': 'moveit_core'}
[0.000699] (-) JobUnselected: {'identifier': 'moveit_hybrid_planning'}
[0.000707] (-) JobUnselected: {'identifier': 'moveit_kinematics'}
[0.000714] (-) JobUnselected: {'identifier': 'moveit_planners'}
[0.000721] (-) JobUnselected: {'identifier': 'moveit_planners_chomp'}
[0.000729] (-) JobUnselected: {'identifier': 'moveit_planners_ompl'}
[0.000736] (-) JobUnselected: {'identifier': 'moveit_plugins'}
[0.000744] (-) JobUnselected: {'identifier': 'moveit_resources'}
[0.000751] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_description'}
[0.000759] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_moveit_config'}
[0.000767] (-) JobUnselected: {'identifier': 'moveit_resources_panda_description'}
[0.000774] (-) JobUnselected: {'identifier': 'moveit_resources_panda_moveit_config'}
[0.000782] (-) JobUnselected: {'identifier': 'moveit_resources_pr2_description'}
[0.000789] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_ikfast_manipulator_plugin'}
[0.000797] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_moveit_config'}
[0.000804] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_pg70_support'}
[0.000812] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_support'}
[0.000819] (-) JobUnselected: {'identifier': 'moveit_ros'}
[0.000826] (-) JobUnselected: {'identifier': 'moveit_ros_benchmarks'}
[0.000834] (-) JobUnselected: {'identifier': 'moveit_ros_control_interface'}
[0.000841] (-) JobUnselected: {'identifier': 'moveit_ros_move_group'}
[0.000849] (-) JobUnselected: {'identifier': 'moveit_ros_occupancy_map_monitor'}
[0.000856] (-) JobUnselected: {'identifier': 'moveit_ros_perception'}
[0.000864] (-) JobUnselected: {'identifier': 'moveit_ros_planning'}
[0.000871] (-) JobUnselected: {'identifier': 'moveit_ros_planning_interface'}
[0.000878] (-) JobUnselected: {'identifier': 'moveit_ros_robot_interaction'}
[0.000886] (-) JobUnselected: {'identifier': 'moveit_ros_visualization'}
[0.000893] (-) JobUnselected: {'identifier': 'moveit_ros_warehouse'}
[0.000900] (-) JobUnselected: {'identifier': 'moveit_runtime'}
[0.000924] (-) JobUnselected: {'identifier': 'moveit_servo'}
[0.000931] (-) JobUnselected: {'identifier': 'moveit_setup_app_plugins'}
[0.000939] (-) JobUnselected: {'identifier': 'moveit_setup_assistant'}
[0.000946] (-) JobUnselected: {'identifier': 'moveit_setup_controllers'}
[0.000953] (-) JobUnselected: {'identifier': 'moveit_setup_core_plugins'}
[0.000961] (-) JobUnselected: {'identifier': 'moveit_setup_framework'}
[0.000968] (-) JobUnselected: {'identifier': 'moveit_setup_srdf_plugins'}
[0.000976] (-) JobUnselected: {'identifier': 'moveit_simple_controller_manager'}
[0.000983] (-) JobUnselected: {'identifier': 'moveit_task_constructor_capabilities'}
[0.000990] (-) JobUnselected: {'identifier': 'moveit_task_constructor_core'}
[0.000998] (-) JobUnselected: {'identifier': 'moveit_task_constructor_demo'}
[0.001005] (-) JobUnselected: {'identifier': 'moveit_task_constructor_visualization'}
[0.001013] (-) JobUnselected: {'identifier': 'moveit_visual_tools'}
[0.001020] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner'}
[0.001027] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner_testutils'}
[0.001035] (-) JobUnselected: {'identifier': 'rosparam_shortcuts'}
[0.001042] (-) JobUnselected: {'identifier': 'rviz_marker_tools'}
[0.001050] (-) JobUnselected: {'identifier': 'srdfdom'}
[0.001058] (moveit_task_constructor_msgs) JobQueued: {'identifier': 'moveit_task_constructor_msgs', 'dependencies': OrderedDict()}
[0.001071] (moveit_task_constructor_msgs) JobStarted: {'identifier': 'moveit_task_constructor_msgs'}
[0.006463] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'cmake'}
[0.006813] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.055613] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.100067] (-) TimerEvent: {}
[0.120864] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.127216] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.170202] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.180036] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.180303] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.180621] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.189300] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.201097] (-) TimerEvent: {}
[0.231858] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.242634] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.242806] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.243220] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.255000] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.301899] (-) TimerEvent: {}
[0.330817] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.404445] (-) TimerEvent: {}
[0.433213] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)\n'}
[0.484913] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.497066] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.504669] (-) TimerEvent: {}
[0.510868] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.528868] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.551187] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.573978] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)\n'}
[0.605229] (-) TimerEvent: {}
[0.710548] (-) TimerEvent: {}
[0.742460] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.759601] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[0.815651] (-) TimerEvent: {}
[0.916977] (-) TimerEvent: {}
[1.017810] (-) TimerEvent: {}
[1.120705] (-) TimerEvent: {}
[1.148810] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[1.220978] (-) TimerEvent: {}
[1.326380] (-) TimerEvent: {}
[1.426843] (-) TimerEvent: {}
[1.527734] (-) TimerEvent: {}
[1.632961] (-) TimerEvent: {}
[1.738268] (-) TimerEvent: {}
[1.823048] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.838770] (-) TimerEvent: {}
[1.939245] (-) TimerEvent: {}
[2.041207] (-) TimerEvent: {}
[2.086183] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.088988] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:20 (find_package):\n'}
[2.089212] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[2.089317] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[2.089357] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[2.089386] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[2.089413] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.089439] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[2.089465] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[2.089492] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[2.089519] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.089544] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[2.104376] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found PythonInterp: /usr/local/bin/python3.6 (found suitable version "3.6.15", minimum required is "3.6")\n'}
[2.110280] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[2.111621] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):\n'}
[2.111732] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[2.111789] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[2.111830] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[2.111868] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[2.111899] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.111929] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)\n'}
[2.111971] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[2.112028] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[2.112067] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[2.112095] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.112123] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[2.126350] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):\n'}
[2.126592] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[2.126643] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[2.126686] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[2.126735] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[2.126775] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.126818] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)\n'}
[2.126865] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[2.126915] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[2.126959] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[2.126999] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[2.127040] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[2.141845] (-) TimerEvent: {}
[2.189545] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/aarch64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5")\n'}
[2.189707] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/local/bin/python3.6\n'}
[2.189748] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[2.189781] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so\n'}
[2.202863] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found PythonExtra: .so\n'}
[2.243589] (-) TimerEvent: {}
[2.344052] (-) TimerEvent: {}
[2.358258] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Configuring done (2.3s)\n'}
[2.419413] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[2.429069] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs\n'}
[2.437077] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 0}
[2.437365] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'build'}
[2.437380] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[2.444238] (-) TimerEvent: {}
[2.480404] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[2.489065] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[2.489989] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs\n'}
[2.546160] (-) TimerEvent: {}
[2.613281] (moveit_task_constructor_msgs) StdoutLine: {'line': b'running egg_info\n'}
[2.613617] (moveit_task_constructor_msgs) StdoutLine: {'line': b'creating moveit_task_constructor_msgs.egg-info\n'}
[2.613707] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing moveit_task_constructor_msgs.egg-info/PKG-INFO\n'}
[2.613757] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt\n'}
[2.613822] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt\n'}
[2.614612] (moveit_task_constructor_msgs) StdoutLine: {'line': b"writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[2.615426] (moveit_task_constructor_msgs) StdoutLine: {'line': b"reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[2.615714] (moveit_task_constructor_msgs) StdoutLine: {'line': b"writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[2.630558] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg\n'}
[2.646876] (-) TimerEvent: {}
[2.749185] (-) TimerEvent: {}
[2.850616] (-) TimerEvent: {}
[2.955967] (-) TimerEvent: {}
[3.058861] (-) TimerEvent: {}
[3.164293] (-) TimerEvent: {}
[3.223535] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] Built target moveit_task_constructor_msgs__cpp\n'}
[3.228786] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  1%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[3.229346] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[3.232091] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  3%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[3.247545] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/property__functions.c.o\x1b[0m\n'}
[3.247812] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  5%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/solution_info__functions.c.o\x1b[0m\n'}
[3.249259] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/solution__functions.c.o\x1b[0m\n'}
[3.250314] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/stage_description__functions.c.o\x1b[0m\n'}
[3.251385] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__functions.c.o\x1b[0m\n'}
[3.264372] (-) TimerEvent: {}
[3.323815] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/sub_solution__functions.c.o\x1b[0m\n'}
[3.335938] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__functions.c.o\x1b[0m\n'}
[3.354685] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/task_description__functions.c.o\x1b[0m\n'}
[3.358545] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/task_statistics__functions.c.o\x1b[0m\n'}
[3.364186] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__functions.c.o\x1b[0m\n'}
[3.364438] (-) TimerEvent: {}
[3.391826] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/srv/detail/get_solution__functions.c.o\x1b[0m\n'}
[3.429734] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_generator_c.dir/rosidl_generator_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__functions.c.o\x1b[0m\n'}
[3.464547] (-) TimerEvent: {}
[3.565030] (-) TimerEvent: {}
[3.665369] (-) TimerEvent: {}
[3.687959] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 14%] \x1b[32m\x1b[1mLinking C shared library libmoveit_task_constructor_msgs__rosidl_generator_c.so\x1b[0m\n'}
[3.706412] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 14%] Built target moveit_task_constructor_msgs__rosidl_generator_c\n'}
[3.711038] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[3.711141] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[3.711579] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 17%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[3.765459] (-) TimerEvent: {}
[3.774793] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/property__type_support.cpp.o\x1b[0m\n'}
[3.775347] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 19%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/solution__type_support.cpp.o\x1b[0m\n'}
[3.776787] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/solution_info__type_support.cpp.o\x1b[0m\n'}
[3.867123] (-) TimerEvent: {}
[3.968175] (-) TimerEvent: {}
[3.976775] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/property__type_support.cpp.o\x1b[0m\n'}
[4.068280] (-) TimerEvent: {}
[4.083931] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 21%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution__type_support.cpp.o\x1b[0m\n'}
[4.084547] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/stage_description__type_support.cpp.o\x1b[0m\n'}
[4.151272] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution_info__type_support.cpp.o\x1b[0m\n'}
[4.168401] (-) TimerEvent: {}
[4.217368] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/property__type_support.cpp.o\x1b[0m\n'}
[4.268564] (-) TimerEvent: {}
[4.326512] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[4.340354] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/sub_solution__type_support.cpp.o\x1b[0m\n'}
[4.369377] (-) TimerEvent: {}
[4.469638] (-) TimerEvent: {}
[4.494202] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/property__type_support.cpp.o\x1b[0m\n'}
[4.517342] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/solution__type_support.cpp.o\x1b[0m\n'}
[4.556028] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/solution_info__type_support.cpp.o\x1b[0m\n'}
[4.569746] (-) TimerEvent: {}
[4.580548] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/solution__type_support.cpp.o\x1b[0m\n'}
[4.599700] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/stage_description__type_support.cpp.o\x1b[0m\n'}
[4.609326] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[4.617497] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_description__type_support.cpp.o\x1b[0m\n'}
[4.633659] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/sub_solution__type_support.cpp.o\x1b[0m\n'}
[4.660155] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[4.669856] (-) TimerEvent: {}
[4.682012] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/task_description__type_support.cpp.o\x1b[0m\n'}
[4.682578] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/property__type_support.c.o\x1b[0m\n'}
[4.687326] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[4.703987] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/task_statistics__type_support.cpp.o\x1b[0m\n'}
[4.724897] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[4.734690] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/solution__type_support.c.o\x1b[0m\n'}
[4.746511] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 39%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/srv/get_solution__type_support.cpp.o\x1b[0m\n'}
[4.755148] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/solution_info__type_support.cpp.o\x1b[0m\n'}
[4.770194] (-) TimerEvent: {}
[4.780410] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/property__type_support_c.cpp.o\x1b[0m\n'}
[4.780734] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_c.dir/rosidl_typesupport_c/moveit_task_constructor_msgs/action/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[4.782629] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/solution_info__type_support.c.o\x1b[0m\n'}
[4.832655] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/stage_description__type_support.c.o\x1b[0m\n'}
[4.849926] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 44%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_c.so\x1b[0m\n'}
[4.870479] (-) TimerEvent: {}
[4.891602] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support.c.o\x1b[0m\n'}
[4.914872] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 45%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c\n'}
[4.920407] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/task_description__type_support.cpp.o\x1b[0m\n'}
[4.956039] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[4.971177] (-) TimerEvent: {}
[4.972279] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support.c.o\x1b[0m\n'}
[5.005477] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution__type_support_c.cpp.o\x1b[0m\n'}
[5.030662] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support.c.o\x1b[0m\n'}
[5.071268] (-) TimerEvent: {}
[5.072100] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/task_description__type_support.c.o\x1b[0m\n'}
[5.107372] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support.c.o\x1b[0m\n'}
[5.130067] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/stage_description__type_support.cpp.o\x1b[0m\n'}
[5.163289] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support.c.o\x1b[0m\n'}
[5.165804] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/task_statistics__type_support.cpp.o\x1b[0m\n'}
[5.171377] (-) TimerEvent: {}
[5.201564] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/srv/detail/get_solution__type_support.c.o\x1b[0m\n'}
[5.271560] (-) TimerEvent: {}
[5.276680] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding C object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support.c.o\x1b[0m\n'}
[5.331243] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution_info__type_support_c.cpp.o\x1b[0m\n'}
[5.350412] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 57%] \x1b[32m\x1b[1mLinking C shared library libmoveit_task_constructor_msgs__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[5.371641] (-) TimerEvent: {}
[5.383403] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support.cpp.o\x1b[0m\n'}
[5.401192] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/stage_statistics__type_support.cpp.o\x1b[0m\n'}
[5.401968] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 59%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c\n'}
[5.406354] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/srv/get_solution__type_support.cpp.o\x1b[0m\n'}
[5.414580] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/msg/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[5.475769] (-) TimerEvent: {}
[5.576774] (-) TimerEvent: {}
[5.593505] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_description__type_support_c.cpp.o\x1b[0m\n'}
[5.656452] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[5.662663] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/moveit_task_constructor_msgs/action/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[5.678309] (-) TimerEvent: {}
[5.684381] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/sub_solution__type_support.cpp.o\x1b[0m\n'}
[5.772528] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_description__type_support.cpp.o\x1b[0m\n'}
[5.778515] (-) TimerEvent: {}
[5.784794] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/sub_trajectory__type_support.cpp.o\x1b[0m\n'}
[5.843316] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support_c.cpp.o\x1b[0m\n'}
[5.872789] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/task_description__type_support.cpp.o\x1b[0m\n'}
[5.878609] (-) TimerEvent: {}
[5.980479] (-) TimerEvent: {}
[6.015056] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 67%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_cpp.so\x1b[0m\n'}
[6.053982] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 67%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp\n'}
[6.061918] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support.cpp.o\x1b[0m\n'}
[6.067343] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support_c.cpp.o\x1b[0m\n'}
[6.081383] (-) TimerEvent: {}
[6.118424] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support_c.cpp.o\x1b[0m\n'}
[6.149391] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_description__type_support_c.cpp.o\x1b[0m\n'}
[6.185069] (-) TimerEvent: {}
[6.216415] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/task_statistics__type_support.cpp.o\x1b[0m\n'}
[6.270115] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support_c.cpp.o\x1b[0m\n'}
[6.285229] (-) TimerEvent: {}
[6.381719] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/msg/detail/dds_fastrtps/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[6.384637] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support_c.cpp.o\x1b[0m\n'}
[6.387590] (-) TimerEvent: {}
[6.410369] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support.cpp.o\x1b[0m\n'}
[6.466485] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/srv/detail/dds_fastrtps/get_solution__type_support.cpp.o\x1b[0m\n'}
[6.467098] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/srv/detail/get_solution__type_support.cpp.o\x1b[0m\n'}
[6.487748] (-) TimerEvent: {}
[6.512079] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/srv/detail/get_solution__type_support_c.cpp.o\x1b[0m\n'}
[6.557375] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[6.587834] (-) TimerEvent: {}
[6.674147] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support_c.cpp.o\x1b[0m\n'}
[6.688388] (-) TimerEvent: {}
[6.714647] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/moveit_task_constructor_msgs/action/detail/dds_fastrtps/execute_task_solution__type_support.cpp.o\x1b[0m\n'}
[6.789398] (-) TimerEvent: {}
[6.889784] (-) TimerEvent: {}
[6.945536] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 81%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[6.984295] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 81%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c\n'}
[6.989874] (-) TimerEvent: {}
[7.084733] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 82%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[7.090156] (-) TimerEvent: {}
[7.121190] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 82%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[7.191472] (-) TimerEvent: {}
[7.215854] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] \x1b[32m\x1b[1mLinking CXX shared library libmoveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[7.250269] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp\n'}
[7.256243] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs\n'}
[7.264770] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 84%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[7.292281] (-) TimerEvent: {}
[7.317975] (moveit_task_constructor_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[7.318381] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 8, in <module>\n'}
[7.318560] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_generator_py import generate_py\n'}
[7.318614] (moveit_task_constructor_msgs) StderrLine: {'line': b"ImportError: cannot import name 'generate_py'\n"}
[7.322997] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[2]: *** [/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/build.make:334: rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c] Error 1\n'}
[7.323100] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:725: moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/all] Error 2\n'}
[7.323191] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[7.325067] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 2}
[7.330579] (moveit_task_constructor_msgs) JobEnded: {'identifier': 'moveit_task_constructor_msgs', 'rc': 2}
[7.343064] (-) EventReactorShutdown: {}
