[  0%] Built target moveit_task_constructor_msgs__cpp
[  0%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs
[ 12%] Built target moveit_task_constructor_msgs__rosidl_generator_c
[ 24%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp
[ 36%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp
[ 47%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp
[ 59%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c
[ 71%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c
[ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c
[ 83%] Built target moveit_task_constructor_msgs
[ 84%] [34m[1mGenerating Python code for ROS interfaces[0m
running egg_info
writing moveit_task_constructor_msgs.egg-info/PKG-INFO
writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt
writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt
reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'
writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'
[ 84%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg
