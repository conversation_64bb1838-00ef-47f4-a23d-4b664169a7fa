[0.065s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--mixin', 'release']
[0.065s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=['release'], verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffb65d5450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffb65d4f40>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffb65d4f40>>, mixin_verb=('build',))
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.135s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.135s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ws_moveit2'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'colcon_pkg'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['colcon_meta']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'colcon_meta'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['ros']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ros'
[0.144s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launch_param_builder' with type 'ros.ament_python' and name 'launch_param_builder'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ros'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['cmake', 'python']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'cmake'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'python'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['python_setup_py']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'python_setup_py'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ros'
[0.145s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit' with type 'ros.ament_cmake' and name 'moveit'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) ignored
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['ignore', 'ignore_ament_install']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ignore'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ros'
[0.146s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_common' with type 'ros.ament_cmake' and name 'moveit_common'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ignore_ament_install'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['colcon_pkg']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ros'
[0.146s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_configs_utils' with type 'ros.ament_python' and name 'moveit_configs_utils'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['ignore', 'ignore_ament_install']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ignore'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ignore_ament_install'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['colcon_pkg']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'colcon_pkg'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['colcon_meta']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'colcon_meta'
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['ros']
[0.146s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ros'
[0.148s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_core' with type 'ros.ament_cmake' and name 'moveit_core'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) ignored
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ignore_ament_install'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['colcon_pkg']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'colcon_pkg'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['colcon_meta']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'colcon_meta'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['ros']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ros'
[0.150s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_kinematics' with type 'ros.ament_cmake' and name 'moveit_kinematics'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ros'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['cmake', 'python']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['python_setup_py']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'python_setup_py'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ros'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['cmake', 'python']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['python_setup_py']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'python_setup_py'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ignore_ament_install'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['colcon_pkg']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'colcon_pkg'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['colcon_meta']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'colcon_meta'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['ros']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ros'
[0.151s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_interface' with type 'ros.ament_cmake' and name 'moveit_planners_chomp'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['ignore', 'ignore_ament_install']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ignore'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ignore_ament_install'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['colcon_pkg']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'colcon_pkg'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['colcon_meta']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'colcon_meta'
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['ros']
[0.151s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ros'
[0.152s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_motion_planner' with type 'ros.ament_cmake' and name 'chomp_motion_planner'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['ignore', 'ignore_ament_install']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ignore'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ignore_ament_install'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'colcon_meta'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['ros']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ros'
[0.152s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter' with type 'ros.ament_cmake' and name 'moveit_chomp_optimizer_adapter'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['ignore', 'ignore_ament_install']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ignore'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ignore_ament_install'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['colcon_pkg']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'colcon_pkg'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['colcon_meta']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'colcon_meta'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['ros']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ros'
[0.153s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/moveit_planners' with type 'ros.ament_cmake' and name 'moveit_planners'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['ignore', 'ignore_ament_install']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ignore'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ignore_ament_install'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['colcon_pkg']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'colcon_pkg'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['colcon_meta']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'colcon_meta'
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['ros']
[0.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ros'
[0.154s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/ompl' with type 'ros.ament_cmake' and name 'moveit_planners_ompl'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ignore_ament_install'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['colcon_pkg']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'colcon_pkg'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['colcon_meta']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'colcon_meta'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['ros']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ros'
[0.155s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/pilz_industrial_motion_planner' with type 'ros.ament_cmake' and name 'pilz_industrial_motion_planner'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ignore_ament_install'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['colcon_pkg']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'colcon_pkg'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['colcon_meta']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'colcon_meta'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['ros']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ros'
[0.156s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils' with type 'ros.ament_cmake' and name 'pilz_industrial_motion_planner_testutils'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ignore_ament_install'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['colcon_pkg']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'colcon_pkg'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['colcon_meta']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'colcon_meta'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['ros']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ros'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['cmake', 'python']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'cmake'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'python'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['python_setup_py']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'python_setup_py'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ignore'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ros'
[0.157s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_ikfast_manipulator_plugin'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ignore'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['colcon_pkg']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'colcon_pkg'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['colcon_meta']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'colcon_meta'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['ros']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ros'
[0.158s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_moveit_config'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['ignore', 'ignore_ament_install']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ignore'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ignore_ament_install'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['colcon_pkg']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'colcon_pkg'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['colcon_meta']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'colcon_meta'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['ros']
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ros'
[0.158s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_pg70_support' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_pg70_support'
[0.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['ignore', 'ignore_ament_install']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ignore'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ignore_ament_install'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['colcon_pkg']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'colcon_pkg'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['colcon_meta']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'colcon_meta'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['ros']
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ros'
[0.159s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_support' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_support'
[0.159s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) ignored
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'colcon_meta'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['ros']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ros'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['cmake', 'python']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'python_setup_py'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'colcon_meta'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['ros']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ros'
[0.160s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_plugins' with type 'ros.ament_cmake' and name 'moveit_plugins'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ros'
[0.161s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_ros_control_interface' with type 'ros.ament_cmake' and name 'moveit_ros_control_interface'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ros'
[0.162s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_simple_controller_manager' with type 'ros.ament_cmake' and name 'moveit_simple_controller_manager'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ros'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['cmake', 'python']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'cmake'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'python'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['python_setup_py']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'python_setup_py'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ros'
[0.168s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/benchmarks' with type 'ros.ament_cmake' and name 'moveit_ros_benchmarks'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ros'
[0.169s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/hybrid_planning' with type 'ros.ament_cmake' and name 'moveit_hybrid_planning'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ros'
[0.170s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/move_group' with type 'ros.ament_cmake' and name 'moveit_ros_move_group'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ros'
[0.171s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/moveit_ros' with type 'ros.ament_cmake' and name 'moveit_ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ros'
[0.172s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/moveit_servo' with type 'ros.ament_cmake' and name 'moveit_servo'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ros'
[0.173s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/occupancy_map_monitor' with type 'ros.ament_cmake' and name 'moveit_ros_occupancy_map_monitor'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ros'
[0.174s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/perception' with type 'ros.ament_cmake' and name 'moveit_ros_perception'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ros'
[0.175s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/planning' with type 'ros.ament_cmake' and name 'moveit_ros_planning'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/planning_interface' with type 'ros.ament_cmake' and name 'moveit_ros_planning_interface'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/robot_interaction' with type 'ros.ament_cmake' and name 'moveit_ros_robot_interaction'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/visualization' with type 'ros.ament_cmake' and name 'moveit_ros_visualization'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/warehouse' with type 'ros.ament_cmake' and name 'moveit_ros_warehouse'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_runtime' with type 'ros.ament_cmake' and name 'moveit_runtime'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ros'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['cmake', 'python']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'cmake'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'python'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['python_setup_py']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'python_setup_py'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ros'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['cmake', 'python']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'cmake'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'python'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['python_setup_py']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'python_setup_py'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ros'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['cmake', 'python']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'cmake'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'python'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['python_setup_py']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'python_setup_py'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ros'
[0.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_app_plugins'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_assistant' with type 'ros.ament_cmake' and name 'moveit_setup_assistant'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_controllers' with type 'ros.ament_cmake' and name 'moveit_setup_controllers'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_core_plugins'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_framework' with type 'ros.ament_cmake' and name 'moveit_setup_framework'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) ignored
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_srdf_plugins'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ros'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['cmake', 'python']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'cmake'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'python'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['python_setup_py']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'python_setup_py'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['ros']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2_tutorials' with type 'ros.ament_cmake' and name 'moveit2_tutorials'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ros'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['cmake', 'python']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'cmake'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'python'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['python_setup_py']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'python_setup_py'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/fanuc_description' with type 'ros.ament_cmake' and name 'moveit_resources_fanuc_description'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ros'
[0.188s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/fanuc_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_fanuc_moveit_config'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ros'
[0.188s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/moveit_resources' with type 'ros.ament_cmake' and name 'moveit_resources'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'colcon_pkg'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['colcon_meta']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'colcon_meta'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['ros']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ros'
[0.189s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/panda_description' with type 'ros.ament_cmake' and name 'moveit_resources_panda_description'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'colcon_pkg'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['colcon_meta']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'colcon_meta'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['ros']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ros'
[0.190s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/panda_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_panda_moveit_config'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ros'
[0.190s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/pr2_description' with type 'ros.ament_cmake' and name 'moveit_resources_pr2_description'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ros'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['cmake', 'python']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'cmake'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'python'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['python_setup_py']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'python_setup_py'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ros'
[0.191s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/capabilities' with type 'ros.ament_cmake' and name 'moveit_task_constructor_capabilities'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ros'
[0.192s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/core' with type 'ros.ament_cmake' and name 'moveit_task_constructor_core'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ros'
[0.193s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/demo' with type 'ros.ament_cmake' and name 'moveit_task_constructor_demo'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ros'
[0.193s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/msgs' with type 'ros.ament_cmake' and name 'moveit_task_constructor_msgs'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/rviz_marker_tools' with type 'ros.ament_cmake' and name 'rviz_marker_tools'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/visualization' with type 'ros.ament_cmake' and name 'moveit_task_constructor_visualization'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_visual_tools' with type 'ros.ament_cmake' and name 'moveit_visual_tools'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosparam_shortcuts' with type 'ros.ament_cmake' and name 'rosparam_shortcuts'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ros'
[0.197s] DEBUG:colcon.colcon_core.package_identification:Package 'src/srdfdom' with type 'ros.ament_cmake' and name 'srdfdom'
[0.197s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.197s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.197s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.197s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.197s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.238s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 335 installed packages in /opt/ros/humble
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_target' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_clean_cache' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_clean_first' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_force_configure' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'ament_cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'catkin_cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.261s] DEBUG:colcon.colcon_core.verb:Building package 'launch_param_builder' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/launch_param_builder', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/launch_param_builder', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/launch_param_builder', 'symlink_install': False, 'test_result_base': None}
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_target' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_clean_cache' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_clean_first' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_force_configure' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'ament_cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'catkin_cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.261s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_common' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_common', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_common', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_common', 'symlink_install': False, 'test_result_base': None}
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_target' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_clean_first' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_force_configure' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'ament_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.262s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_fanuc_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description', 'symlink_install': False, 'test_result_base': None}
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_target' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_clean_first' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_force_configure' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'ament_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.262s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_panda_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description', 'symlink_install': False, 'test_result_base': None}
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_target' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_clean_first' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_force_configure' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'ament_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.262s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_pr2_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description', 'symlink_install': False, 'test_result_base': None}
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_target' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_clean_cache' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_clean_first' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_force_configure' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'ament_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'catkin_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.262s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_support' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support', 'symlink_install': False, 'test_result_base': None}
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'rosparam_shortcuts' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/rosparam_shortcuts', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/rosparam_shortcuts', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/rosparam_shortcuts', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'srdfdom' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/srdfdom', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/srdfdom', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/srdfdom', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_configs_utils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_configs_utils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_configs_utils', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_fanuc_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_panda_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'rviz_marker_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/rviz_marker_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/rviz_marker_tools', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_core' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_core', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_core', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_core', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'chomp_motion_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/chomp_motion_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/chomp_motion_planner', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_ikfast_manipulator_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_occupancy_map_monitor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor', 'symlink_install': False, 'test_result_base': None}
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_target' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_clean_cache' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_clean_first' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_force_configure' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'ament_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'catkin_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.266s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_simple_controller_manager' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager', 'symlink_install': False, 'test_result_base': None}
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_target' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_clean_cache' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_clean_first' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_force_configure' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'ament_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'catkin_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.266s] DEBUG:colcon.colcon_core.verb:Building package 'pilz_industrial_motion_planner_testutils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils', 'symlink_install': False, 'test_result_base': None}
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_target' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_clean_cache' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_clean_first' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_force_configure' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'ament_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'catkin_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.266s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_chomp_optimizer_adapter' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter', 'symlink_install': False, 'test_result_base': None}
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_target' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_clean_cache' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_clean_first' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_force_configure' from command line to 'False'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'ament_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'catkin_cmake_args' from command line to 'None'
[0.266s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.266s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners_chomp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners_chomp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners_chomp', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface', 'symlink_install': False, 'test_result_base': None}
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_target' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.267s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins', 'symlink_install': False, 'test_result_base': None}
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_target' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.267s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_control_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface', 'symlink_install': False, 'test_result_base': None}
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_target' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_clean_cache' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_clean_first' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_force_configure' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'ament_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'catkin_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.267s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_planning' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_planning', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_planning', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning', 'symlink_install': False, 'test_result_base': None}
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_target' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_clean_cache' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_clean_first' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_force_configure' from command line to 'False'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'ament_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'catkin_cmake_args' from command line to 'None'
[0.267s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.267s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_kinematics' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_kinematics', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_kinematics', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics', 'symlink_install': False, 'test_result_base': None}
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_target' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_clean_cache' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_clean_first' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_force_configure' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'ament_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'catkin_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.268s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners_ompl' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners_ompl', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners_ompl', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl', 'symlink_install': False, 'test_result_base': None}
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_target' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_clean_cache' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_clean_first' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_force_configure' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'ament_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'catkin_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.268s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_perception' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_perception', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_perception', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception', 'symlink_install': False, 'test_result_base': None}
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_target' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_clean_cache' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_clean_first' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_force_configure' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'ament_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'catkin_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.268s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_robot_interaction' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_robot_interaction', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction', 'symlink_install': False, 'test_result_base': None}
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_target' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_clean_cache' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_clean_first' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_force_configure' from command line to 'False'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'ament_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'catkin_cmake_args' from command line to 'None'
[0.268s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.269s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_warehouse' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse', 'symlink_install': False, 'test_result_base': None}
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_target' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.269s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_visual_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_visual_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_visual_tools', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_visual_tools', 'symlink_install': False, 'test_result_base': None}
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_target' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_clean_cache' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_clean_first' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_force_configure' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'ament_cmake_args' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'catkin_cmake_args' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.269s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_benchmarks' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/benchmarks', 'symlink_install': False, 'test_result_base': None}
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_target' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_clean_cache' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_clean_first' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_force_configure' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'ament_cmake_args' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'catkin_cmake_args' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.269s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_move_group' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_move_group', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_move_group', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/move_group', 'symlink_install': False, 'test_result_base': None}
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.269s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.270s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_target' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.270s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_planning_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_planning_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning_interface', 'symlink_install': False, 'test_result_base': None}
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_target' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_clean_cache' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_clean_first' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_force_configure' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'ament_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'catkin_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.270s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_hybrid_planning' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_hybrid_planning', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_hybrid_planning', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/hybrid_planning', 'symlink_install': False, 'test_result_base': None}
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_target' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_clean_cache' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_clean_first' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_force_configure' from command line to 'False'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'ament_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'catkin_cmake_args' from command line to 'None'
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.270s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_pg70_support' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_pg70_support', 'symlink_install': False, 'test_result_base': None}
[0.270s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_target' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_clean_cache' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_clean_first' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_force_configure' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'ament_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'catkin_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.271s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_visualization' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_visualization', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_visualization', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization', 'symlink_install': False, 'test_result_base': None}
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_target' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_clean_cache' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_clean_first' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_force_configure' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'ament_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'catkin_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.271s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_servo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_servo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_servo', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_servo', 'symlink_install': False, 'test_result_base': None}
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_target' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_clean_cache' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_clean_first' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_force_configure' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'ament_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'catkin_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.271s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_ros', 'symlink_install': False, 'test_result_base': None}
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_target' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_clean_cache' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_clean_first' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_force_configure' from command line to 'False'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'ament_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'catkin_cmake_args' from command line to 'None'
[0.271s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.271s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_framework' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_framework', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_framework', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_framework', 'symlink_install': False, 'test_result_base': None}
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_target' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.272s] DEBUG:colcon.colcon_core.verb:Building package 'pilz_industrial_motion_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner', 'symlink_install': False, 'test_result_base': None}
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_target' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_clean_cache' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_clean_first' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_force_configure' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'ament_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'catkin_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.272s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/moveit_planners', 'symlink_install': False, 'test_result_base': None}
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_target' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.272s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_app_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins', 'symlink_install': False, 'test_result_base': None}
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_target' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_clean_cache' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_clean_first' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_force_configure' from command line to 'False'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'ament_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'catkin_cmake_args' from command line to 'None'
[0.272s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.272s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_controllers' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_controllers', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_controllers', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_controllers', 'symlink_install': False, 'test_result_base': None}
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_target' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.273s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_core_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_core_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins', 'symlink_install': False, 'test_result_base': None}
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_target' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.273s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_srdf_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins', 'symlink_install': False, 'test_result_base': None}
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_target' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_clean_cache' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_clean_first' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_force_configure' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'ament_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'catkin_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.273s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_runtime' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_runtime', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_runtime', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_runtime', 'symlink_install': False, 'test_result_base': None}
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_target' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_clean_cache' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_clean_first' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_force_configure' from command line to 'False'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'ament_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'catkin_cmake_args' from command line to 'None'
[0.273s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.273s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_assistant' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_assistant', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_assistant', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_assistant', 'symlink_install': False, 'test_result_base': None}
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_target' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_clean_cache' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_clean_first' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_force_configure' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'ament_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'catkin_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.274s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_core' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_core', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/core', 'symlink_install': False, 'test_result_base': None}
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_target' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_clean_cache' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_clean_first' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_force_configure' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'ament_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'catkin_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.274s] DEBUG:colcon.colcon_core.verb:Building package 'moveit' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit', 'symlink_install': False, 'test_result_base': None}
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_target' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_clean_cache' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_clean_first' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_force_configure' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'ament_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'catkin_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.274s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_capabilities' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_capabilities', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_capabilities', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/capabilities', 'symlink_install': False, 'test_result_base': None}
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_target' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_clean_cache' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_clean_first' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_force_configure' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'ament_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'catkin_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.275s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_visualization' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_visualization', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_visualization', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/visualization', 'symlink_install': False, 'test_result_base': None}
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_target' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_clean_cache' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_clean_first' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_force_configure' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'ament_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'catkin_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.275s] DEBUG:colcon.colcon_core.verb:Building package 'moveit2_tutorials' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit2_tutorials', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit2_tutorials', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2_tutorials', 'symlink_install': False, 'test_result_base': None}
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_target' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_clean_cache' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_clean_first' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_force_configure' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'ament_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'catkin_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.275s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_demo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_demo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_demo', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/demo', 'symlink_install': False, 'test_result_base': None}
[0.275s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.276s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.276s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description' with build type 'ament_cmake'
[0.277s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description'
[0.279s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.279s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.279s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.282s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_common' with build type 'ament_cmake'
[0.282s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_common'
[0.282s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.282s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.284s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description' with build type 'ament_cmake'
[0.284s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description'
[0.284s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.284s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.286s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/srdfdom' with build type 'ament_cmake'
[0.286s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/srdfdom'
[0.286s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.286s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.288s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_moveit2/src/launch_param_builder' with build type 'ament_python'
[0.288s] Level 1:colcon.colcon_core.shell:create_environment_hook('launch_param_builder', 'ament_prefix_path')
[0.288s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.ps1'
[0.289s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.dsv'
[0.289s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.sh'
[0.290s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.290s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.292s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description' with build type 'ament_cmake'
[0.292s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description'
[0.292s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.292s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.294s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support' with build type 'ament_cmake'
[0.295s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support'
[0.295s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.295s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.296s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs' with build type 'ament_cmake'
[0.297s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs'
[0.297s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.297s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.302s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_description -- -j8 -l8
[0.306s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.309s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description -- -j8 -l8
[0.310s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.429s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_moveit2/src/launch_param_builder'
[0.429s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.429s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.433s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description -- -j8 -l8
[0.435s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support -- -j8 -l8
[0.437s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[0.443s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description -- -j8 -l8
[0.446s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description
[0.453s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.454s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.474s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_common)
[0.476s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_description -- -j8 -l8
[0.477s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_description
[0.478s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description -- -j8 -l8
[0.482s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description
[0.484s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake module files
[0.484s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake config files
[0.484s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_common', 'cmake_prefix_path')
[0.484s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.ps1'
[0.485s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.dsv'
[0.485s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.sh'
[0.485s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/pkgconfig/moveit_common.pc'
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/python3.10/site-packages'
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.486s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.ps1'
[0.490s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.490s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[0.491s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.492s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv'
[0.492s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.bash'
[0.494s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.zsh'
[0.495s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_common/share/colcon-core/packages/moveit_common)
[0.496s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_common)
[0.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake module files
[0.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake config files
[0.496s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_common', 'cmake_prefix_path')
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.ps1'
[0.497s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.dsv'
[0.498s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.sh'
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/pkgconfig/moveit_common.pc'
[0.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/python3.10/site-packages'
[0.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.500s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.ps1'
[0.501s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv'
[0.501s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh'
[0.501s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.bash'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.zsh'
[0.502s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_common/share/colcon-core/packages/moveit_common)
[0.502s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_pr2_description)
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake module files
[0.503s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake config files
[0.503s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_pr2_description', 'cmake_prefix_path')
[0.503s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.ps1'
[0.504s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.dsv'
[0.504s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description
[0.505s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.sh'
[0.505s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.505s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/pkgconfig/moveit_resources_pr2_description.pc'
[0.505s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/python3.10/site-packages'
[0.505s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.505s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.ps1'
[0.506s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv'
[0.506s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh'
[0.506s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.bash'
[0.506s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.zsh'
[0.507s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/colcon-core/packages/moveit_resources_pr2_description)
[0.507s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_pr2_description)
[0.507s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake module files
[0.508s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake config files
[0.508s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_pr2_description', 'cmake_prefix_path')
[0.508s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.ps1'
[0.509s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.dsv'
[0.509s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.sh'
[0.509s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.509s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/pkgconfig/moveit_resources_pr2_description.pc'
[0.510s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/python3.10/site-packages'
[0.510s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.510s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.ps1'
[0.510s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv'
[0.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh'
[0.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.bash'
[0.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.zsh'
[0.511s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/colcon-core/packages/moveit_resources_pr2_description)
[0.512s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support -- -j8 -l8
[0.514s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support
[0.516s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools' with build type 'ament_cmake'
[0.516s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools'
[0.516s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.516s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.522s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/rosparam_shortcuts' with build type 'ament_cmake'
[0.522s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/rosparam_shortcuts'
[0.523s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.523s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.525s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_description)
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake module files
[0.526s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake config files
[0.527s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_description
[0.527s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_description', 'cmake_prefix_path')
[0.527s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.ps1'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.dsv'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.sh'
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/pkgconfig/moveit_resources_panda_description.pc'
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/python3.10/site-packages'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.529s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.ps1'
[0.529s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv'
[0.529s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh'
[0.529s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.bash'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.zsh'
[0.530s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/colcon-core/packages/moveit_resources_panda_description)
[0.530s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_description)
[0.530s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake module files
[0.530s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake config files
[0.531s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_description', 'cmake_prefix_path')
[0.531s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.ps1'
[0.531s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.dsv'
[0.531s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.sh'
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/pkgconfig/moveit_resources_panda_description.pc'
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/python3.10/site-packages'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.532s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.ps1'
[0.532s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv'
[0.532s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh'
[0.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.bash'
[0.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.zsh'
[0.534s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/colcon-core/packages/moveit_resources_panda_description)
[0.534s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config' with build type 'ament_cmake'
[0.535s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config'
[0.535s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.535s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.536s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_description)
[0.536s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description
[0.536s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake module files
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake config files
[0.537s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_description', 'cmake_prefix_path')
[0.537s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.ps1'
[0.537s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.dsv'
[0.537s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.sh'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/pkgconfig/moveit_resources_fanuc_description.pc'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/python3.10/site-packages'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.538s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.ps1'
[0.538s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv'
[0.539s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh'
[0.539s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.bash'
[0.539s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.zsh'
[0.539s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/colcon-core/packages/moveit_resources_fanuc_description)
[0.539s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_description)
[0.539s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake module files
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake config files
[0.540s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_description', 'cmake_prefix_path')
[0.540s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.ps1'
[0.540s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.dsv'
[0.540s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.sh'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/pkgconfig/moveit_resources_fanuc_description.pc'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/python3.10/site-packages'
[0.541s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.541s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.ps1'
[0.541s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv'
[0.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh'
[0.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.bash'
[0.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.zsh'
[0.543s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/colcon-core/packages/moveit_resources_fanuc_description)
[0.543s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(srdfdom)
[0.543s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake module files
[0.544s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake config files
[0.544s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'cmake_prefix_path')
[0.544s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.ps1'
[0.544s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.dsv'
[0.543s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[0.545s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.sh'
[0.546s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib'
[0.546s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'ld_library_path_lib')
[0.546s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.ps1'
[0.546s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.dsv'
[0.547s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.sh'
[0.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/pkgconfig/srdfdom.pc'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/python3.10/site-packages'
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.549s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.ps1'
[0.551s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv'
[0.551s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh'
[0.552s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.bash'
[0.553s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.zsh'
[0.553s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/srdfdom/share/colcon-core/packages/srdfdom)
[0.554s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(srdfdom)
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake module files
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake config files
[0.554s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'cmake_prefix_path')
[0.555s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.ps1'
[0.555s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.dsv'
[0.555s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.sh'
[0.555s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib'
[0.555s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'ld_library_path_lib')
[0.555s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.ps1'
[0.556s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.dsv'
[0.556s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.sh'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/pkgconfig/srdfdom.pc'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/python3.10/site-packages'
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.ps1'
[0.557s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.bash'
[0.558s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.zsh'
[0.559s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/srdfdom/share/colcon-core/packages/srdfdom)
[0.559s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_prbt_support)
[0.559s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake module files
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake config files
[0.560s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_prbt_support', 'cmake_prefix_path')
[0.560s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.ps1'
[0.560s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.dsv'
[0.560s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.sh'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/pkgconfig/moveit_resources_prbt_support.pc'
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/python3.10/site-packages'
[0.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.ps1'
[0.562s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.bash'
[0.563s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.zsh'
[0.563s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/colcon-core/packages/moveit_resources_prbt_support)
[0.563s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_prbt_support)
[0.563s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake module files
[0.563s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake config files
[0.563s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_prbt_support', 'cmake_prefix_path')
[0.564s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.ps1'
[0.564s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.dsv'
[0.564s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.sh'
[0.564s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.564s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/pkgconfig/moveit_resources_prbt_support.pc'
[0.564s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/python3.10/site-packages'
[0.564s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.564s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.ps1'
[0.565s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv'
[0.565s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh'
[0.565s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.bash'
[0.565s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.zsh'
[0.565s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/colcon-core/packages/moveit_resources_prbt_support)
[0.566s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config' with build type 'ament_cmake'
[0.566s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config'
[0.566s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.566s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.571s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[0.575s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[0.584s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config -- -j8 -l8
[0.595s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config -- -j8 -l8
[0.608s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config -- -j8 -l8
[0.609s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config
[0.621s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config -- -j8 -l8
[0.622s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config
[0.625s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[0.626s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[0.629s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_moveit_config)
[0.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake module files
[0.632s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake config files
[0.632s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_moveit_config', 'cmake_prefix_path')
[0.632s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.ps1'
[0.633s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config
[0.634s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.dsv'
[0.636s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.sh'
[0.636s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[0.636s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/pkgconfig/moveit_resources_panda_moveit_config.pc'
[0.637s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/python3.10/site-packages'
[0.637s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[0.637s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.ps1'
[0.637s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv'
[0.637s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh'
[0.637s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.bash'
[0.638s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.zsh'
[0.638s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/colcon-core/packages/moveit_resources_panda_moveit_config)
[0.638s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_moveit_config)
[0.638s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake module files
[0.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake config files
[0.639s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_moveit_config', 'cmake_prefix_path')
[0.639s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.ps1'
[0.639s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.dsv'
[0.640s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.sh'
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/pkgconfig/moveit_resources_panda_moveit_config.pc'
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/python3.10/site-packages'
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[0.640s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.ps1'
[0.641s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv'
[0.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh'
[0.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.bash'
[0.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.zsh'
[0.641s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/colcon-core/packages/moveit_resources_panda_moveit_config)
[0.642s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_core' with build type 'ament_cmake'
[0.643s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_core'
[0.644s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.644s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.646s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_moveit_config)
[0.646s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake module files
[0.646s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake config files
[0.647s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config
[0.647s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_moveit_config', 'cmake_prefix_path')
[0.647s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.ps1'
[0.647s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.dsv'
[0.648s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.sh'
[0.648s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.648s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/pkgconfig/moveit_resources_fanuc_moveit_config.pc'
[0.648s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/python3.10/site-packages'
[0.648s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.649s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.ps1'
[0.649s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv'
[0.649s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh'
[0.649s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.bash'
[0.649s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.zsh'
[0.650s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/colcon-core/packages/moveit_resources_fanuc_moveit_config)
[0.650s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_moveit_config)
[0.650s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake module files
[0.650s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake config files
[0.650s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_moveit_config', 'cmake_prefix_path')
[0.650s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.ps1'
[0.651s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.dsv'
[0.651s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.sh'
[0.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/pkgconfig/moveit_resources_fanuc_moveit_config.pc'
[0.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/python3.10/site-packages'
[0.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.652s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.ps1'
[0.652s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv'
[0.652s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh'
[0.652s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.bash'
[0.653s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.zsh'
[0.653s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/colcon-core/packages/moveit_resources_fanuc_moveit_config)
[0.653s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rviz_marker_tools)
[0.653s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake module files
[0.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake config files
[0.654s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'cmake_prefix_path')
[0.654s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.ps1'
[0.654s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.dsv'
[0.654s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[0.654s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.sh'
[0.655s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib'
[0.655s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'ld_library_path_lib')
[0.655s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.ps1'
[0.655s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.dsv'
[0.655s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.sh'
[0.655s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[0.655s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/pkgconfig/rviz_marker_tools.pc'
[0.656s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/python3.10/site-packages'
[0.656s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[0.656s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.ps1'
[0.656s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv'
[0.656s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.sh'
[0.657s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.bash'
[0.657s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.zsh'
[0.657s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/colcon-core/packages/rviz_marker_tools)
[0.657s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rviz_marker_tools)
[0.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake module files
[0.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake config files
[0.657s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'cmake_prefix_path')
[0.658s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.ps1'
[0.658s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.dsv'
[0.658s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.sh'
[0.658s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib'
[0.658s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'ld_library_path_lib')
[0.658s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.ps1'
[0.659s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.dsv'
[0.659s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.sh'
[0.661s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[0.661s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/pkgconfig/rviz_marker_tools.pc'
[0.661s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/python3.10/site-packages'
[0.661s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[0.661s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.ps1'
[0.663s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv'
[0.665s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.sh'
[0.666s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.bash'
[0.666s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.zsh'
[0.667s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/colcon-core/packages/rviz_marker_tools)
[0.668s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources' with build type 'ament_cmake'
[0.668s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources'
[0.668s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.668s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.670s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[0.671s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_task_constructor_msgs)
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs' for CMake module files
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs' for CMake config files
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/bin'
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/lib/pkgconfig/moveit_task_constructor_msgs.pc'
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/lib/python3.10/site-packages'
[0.671s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/bin'
[0.671s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.ps1'
[0.673s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.dsv'
[0.673s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.sh'
[0.674s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.bash'
[0.674s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.zsh'
[0.674s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/colcon-core/packages/moveit_task_constructor_msgs)
[0.686s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.686s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.686s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[0.686s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.691s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.691s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.691s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.703s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.703s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.ps1'
[0.704s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_moveit2/install/_local_setup_util_ps1.py'
[0.705s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.ps1'
[0.707s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.sh'
[0.708s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_moveit2/install/_local_setup_util_sh.py'
[0.708s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.sh'
[0.708s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.bash'
[0.709s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.bash'
[0.709s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.zsh'
[0.709s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.zsh'
