[0.026s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_planners_chomp -- -j8 -l8
[0.052s] [ 60%] Built target moveit_chomp_interface
[0.068s] [100%] Built target moveit_chomp_planner_plugin
[0.071s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_planners_chomp -- -j8 -l8
[0.072s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_planners_chomp
[0.074s] -- Install configuration: "Release"
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_interface.so.2.5.9
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_interface.so
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_planner_plugin.so
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/library_path.sh
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/library_path.dsv
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/chomp_interface_plugin_description.xml
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/package_run_dependencies/moveit_planners_chomp
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/parent_prefix_path/moveit_planners_chomp
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/ament_prefix_path.sh
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/ament_prefix_path.dsv
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/path.sh
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/path.dsv
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.bash
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.sh
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.zsh
[0.074s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.dsv
[0.074s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.dsv
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/packages/moveit_planners_chomp
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_planners_chomp
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompTargetsExport.cmake
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompTargetsExport-release.cmake
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/ament_cmake_export_targets-extras.cmake
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/ament_cmake_export_dependencies-extras.cmake
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompConfig.cmake
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompConfig-version.cmake
[0.075s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.xml
[0.076s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_planners_chomp
