[0.033s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_plugins -- -j8 -l8
[0.055s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_plugins -- -j8 -l8
[0.057s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_plugins
[0.058s] -- Install configuration: "Release"
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/ament_index/resource_index/package_run_dependencies/moveit_plugins
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/ament_index/resource_index/parent_prefix_path/moveit_plugins
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/environment/ament_prefix_path.sh
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/environment/ament_prefix_path.dsv
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/environment/path.sh
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/environment/path.dsv
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/local_setup.bash
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/local_setup.sh
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/local_setup.zsh
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/local_setup.dsv
[0.058s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.dsv
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/ament_index/resource_index/packages/moveit_plugins
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/cmake/moveit_pluginsConfig.cmake
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/cmake/moveit_pluginsConfig-version.cmake
[0.058s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.xml
[0.059s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_plugins
