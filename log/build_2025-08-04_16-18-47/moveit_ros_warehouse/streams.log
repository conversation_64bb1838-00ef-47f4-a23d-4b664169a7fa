[0.016s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_warehouse -- -j8 -l8
[0.063s] [ 40%] Built target moveit_warehouse
[0.088s] [ 50%] Built target moveit_warehouse_services
[0.104s] [ 60%] Built target moveit_warehouse_save_as_text
[0.105s] [ 70%] Built target moveit_warehouse_broadcast
[0.106s] [ 80%] Built target moveit_warehouse_import_from_text
[0.108s] [ 90%] Built target moveit_init_demo_warehouse
[0.111s] [100%] Built target moveit_save_to_warehouse
[0.141s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_warehouse -- -j8 -l8
[0.145s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_warehouse
[0.151s] -- Install configuration: "Release"
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/moveit_ros_warehouse/moveit_save_to_warehouse
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/moveit_ros_warehouse/moveit_warehouse_broadcast
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/moveit_ros_warehouse/moveit_warehouse_import_from_text
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/moveit_ros_warehouse/moveit_warehouse_save_as_text
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/moveit_ros_warehouse/moveit_init_demo_warehouse
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/moveit_ros_warehouse/moveit_warehouse_services
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit
[0.151s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/constraints_storage.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/moveit_message_storage.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/planning_scene_storage.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/planning_scene_world_storage.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/state_storage.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/trajectory_constraints_storage.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit/warehouse/warehouse_connector.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/include/moveit_warehouse_export.h
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/libmoveit_warehouse.so.2.5.9
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib/libmoveit_warehouse.so
[0.152s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/environment/library_path.sh
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/environment/library_path.dsv
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/ament_index/resource_index/package_run_dependencies/moveit_ros_warehouse
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/ament_index/resource_index/parent_prefix_path/moveit_ros_warehouse
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/environment/ament_prefix_path.sh
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/environment/ament_prefix_path.dsv
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/environment/path.sh
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/environment/path.dsv
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/local_setup.bash
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/local_setup.sh
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/local_setup.zsh
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/local_setup.dsv
[0.153s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/package.dsv
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/ament_index/resource_index/packages/moveit_ros_warehouse
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/export_moveit_ros_warehouseExport.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/export_moveit_ros_warehouseExport-release.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ament_cmake_export_targets-extras.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ament_cmake_export_dependencies-extras.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig-version.cmake
[0.153s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/package.xml
[0.165s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_warehouse
