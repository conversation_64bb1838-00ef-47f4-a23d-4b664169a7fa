[0.026s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins -- -j8 -l8
[0.052s] [  4%] Built target gtest
[0.054s] [  8%] Built target gtest_main
[0.155s] [ 95%] Built target moveit_setup_srdf_plugins
[0.171s] [100%] Built target test_srdf
[0.177s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins -- -j8 -l8
[0.177s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins
[0.180s] -- Install configuration: "Release"
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib/libmoveit_setup_srdf_plugins.so
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/moveit_setup_framework_plugins.xml
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/collision_linear_model.hpp
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/collision_matrix_model.hpp
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/compute_default_collisions.hpp
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/default_collisions.hpp
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/default_collisions_widget.hpp
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/end_effectors.hpp
[0.181s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/end_effectors_widget.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/group_edit_widget.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/group_meta_config.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/kinematic_chain_widget.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/passive_joints.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/passive_joints_widget.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/planning_groups.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/planning_groups_widget.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/robot_poses.hpp
[0.182s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/robot_poses_widget.hpp
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/rotated_header_view.hpp
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/srdf_step.hpp
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/virtual_joints.hpp
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/include/moveit_setup_srdf_plugins/virtual_joints_widget.hpp
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/library_path.sh
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/library_path.dsv
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/moveit_setup_framework_plugins.xml
[0.183s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/package_run_dependencies/moveit_setup_srdf_plugins
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/parent_prefix_path/moveit_setup_srdf_plugins
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/ament_prefix_path.sh
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/ament_prefix_path.dsv
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/path.sh
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/environment/path.dsv
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.bash
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.sh
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.zsh
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/local_setup.dsv
[0.184s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/package.dsv
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/packages/moveit_setup_srdf_plugins
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/ament_index/resource_index/moveit_setup_framework__pluginlib__plugin/moveit_setup_srdf_plugins
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/export_moveit_setup_srdf_pluginsExport.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/export_moveit_setup_srdf_pluginsExport-release.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/ament_cmake_export_include_directories-extras.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/ament_cmake_export_libraries-extras.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/ament_cmake_export_targets-extras.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/moveit_setup_srdf_pluginsConfig.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/cmake/moveit_setup_srdf_pluginsConfig-version.cmake
[0.184s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/package.xml
[0.185s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins
