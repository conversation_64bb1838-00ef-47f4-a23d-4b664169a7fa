[0.018s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_benchmarks -- -j8 -l8
[0.048s] [ 42%] Built target moveit_ros_benchmarks
[0.067s] [ 71%] Built target moveit_combine_predefined_poses_benchmark
[0.068s] [100%] Built target moveit_run_benchmark
[0.078s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_benchmarks -- -j8 -l8
[0.078s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_benchmarks
[0.081s] -- Install configuration: "Release"
[0.081s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib/libmoveit_ros_benchmarks.so.2.5.9
[0.081s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib/libmoveit_ros_benchmarks.so
[0.081s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/environment/library_path.sh
[0.081s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/environment/library_path.dsv
[0.081s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib/moveit_ros_benchmarks/moveit_run_benchmark
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib/moveit_ros_benchmarks/moveit_combine_predefined_poses_benchmark
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/include
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/include/moveit
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/include/moveit/benchmarks
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/include/moveit/benchmarks/BenchmarkExecutor.h
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/include/moveit/benchmarks/BenchmarkOptions.h
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib/moveit_ros_benchmarks/moveit_benchmark_statistics.py
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo1.yaml
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo2.yaml
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_fanuc.launch
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_obstacles.yaml
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_panda.launch.py
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_panda_all_planners.launch
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_panda_all_planners.yaml
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_panda_all_planners_obstacles.launch
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_panda_predefined_poses.launch.py
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/demo_panda_predefined_poses.yaml
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/ament_index/resource_index/package_run_dependencies/moveit_ros_benchmarks
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/ament_index/resource_index/parent_prefix_path/moveit_ros_benchmarks
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/environment/ament_prefix_path.sh
[0.082s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/environment/ament_prefix_path.dsv
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/environment/path.sh
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/environment/path.dsv
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/local_setup.bash
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/local_setup.sh
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/local_setup.zsh
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/local_setup.dsv
[0.083s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/package.dsv
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/ament_index/resource_index/packages/moveit_ros_benchmarks
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/export_moveit_ros_benchmarksExport.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/export_moveit_ros_benchmarksExport-release.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/ConfigExtras.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/ament_cmake_export_targets-extras.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/ament_cmake_export_dependencies-extras.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/moveit_ros_benchmarksConfig.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/cmake/moveit_ros_benchmarksConfig-version.cmake
[0.083s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/package.xml
[0.085s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_benchmarks
