[0.029s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_app_plugins -- -j8 -l8
[0.045s] [  0%] Built target moveit_setup_app_plugins_autogen_timestamp_deps
[0.045s] [  0%] Built target gtest_main_autogen_timestamp_deps
[0.046s] [  0%] Built target gtest_autogen_timestamp_deps
[0.059s] [  3%] Built target gtest_main_autogen
[0.060s] [  7%] Built target gtest_autogen
[0.069s] [ 22%] Built target gtest_main
[0.070s] [ 25%] Built target moveit_setup_app_plugins_autogen
[0.072s] [ 40%] Built target gtest
[0.099s] [ 85%] Built target moveit_setup_app_plugins
[0.103s] [ 85%] Built target test_perception_autogen_timestamp_deps
[0.149s] [ 88%] Built target test_perception_autogen
[0.162s] [100%] Built target test_perception
[0.167s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_setup_app_plugins -- -j8 -l8
[0.167s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_app_plugins
[0.170s] -- Install configuration: "Release"
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/config
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/config/moveit.rviz
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/config/sensors_3d.yaml
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/launch
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/templates/launch/generic.launch.py.template
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib/libmoveit_setup_app_plugins.so
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/moveit_setup_framework_plugins.xml
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launch_bundle.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launches.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launches_config.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/launches_widget.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/perception.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/perception_config.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/include/moveit_setup_app_plugins/perception_widget.hpp
[0.170s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/library_path.sh
[0.171s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/library_path.dsv
[0.171s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/moveit_setup_framework_plugins.xml
[0.171s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/package_run_dependencies/moveit_setup_app_plugins
[0.171s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/parent_prefix_path/moveit_setup_app_plugins
[0.173s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/ament_prefix_path.sh
[0.173s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/ament_prefix_path.dsv
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/path.sh
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/environment/path.dsv
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.bash
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.sh
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.zsh
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/local_setup.dsv
[0.174s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/package.dsv
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/packages/moveit_setup_app_plugins
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/ament_index/resource_index/moveit_setup_framework__pluginlib__plugin/moveit_setup_app_plugins
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/export_moveit_setup_app_pluginsExport.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/export_moveit_setup_app_pluginsExport-release.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/ament_cmake_export_include_directories-extras.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/ament_cmake_export_libraries-extras.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/ament_cmake_export_targets-extras.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/moveit_setup_app_pluginsConfig.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/cmake/moveit_setup_app_pluginsConfig-version.cmake
[0.174s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/package.xml
[0.174s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_setup_app_plugins
