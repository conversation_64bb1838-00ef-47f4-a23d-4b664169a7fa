[0.015s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support -- -j8 -l8
[0.025s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support -- -j8 -l8
[0.025s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support
[0.028s] -- Install configuration: "Release"
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/ament_index/resource_index/package_run_dependencies/moveit_resources_prbt_pg70_support
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/ament_index/resource_index/parent_prefix_path/moveit_resources_prbt_pg70_support
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/environment/ament_prefix_path.sh
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/environment/ament_prefix_path.dsv
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/environment/path.sh
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/environment/path.dsv
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/local_setup.bash
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/local_setup.sh
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/local_setup.zsh
[0.028s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/local_setup.dsv
[0.029s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/package.dsv
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/ament_index/resource_index/packages/moveit_resources_prbt_pg70_support
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/cmake/moveit_resources_prbt_pg70_supportConfig.cmake
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/cmake/moveit_resources_prbt_pg70_supportConfig-version.cmake
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/package.xml
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/controllers_manipulator_gripper.yaml
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/fake_controllers.yaml
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/gripper_controller.yaml
[0.029s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/gripper_driver_canopen_motor_node.yaml
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/joint_limits.yaml
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/pg70.srdf.xacro
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/config/pg70_tcp_offset.xacro
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/meshes
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/meshes/pg70
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/meshes/pg70/pg70.dae
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/meshes/pg70/pg70.stl
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf/common.xacro
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf/pg70.urdf.xacro
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf/pg70
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf/pg70/pg70.gazebo.xacro
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf/pg70/pg70.transmission.xacro
[0.030s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/urdf/pg70/pg70.urdf.xacro
[0.030s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support
