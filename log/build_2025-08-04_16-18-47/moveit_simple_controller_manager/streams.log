[0.010s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager -- -j8 -l8
[0.050s] [100%] Built target moveit_simple_controller_manager
[0.061s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager -- -j8 -l8
[0.063s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager
[0.068s] -- Install configuration: "Release"
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib/libmoveit_simple_controller_manager.so.2.5.9
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib/libmoveit_simple_controller_manager.so
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/include
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/include/moveit_simple_controller_manager
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/include/moveit_simple_controller_manager/action_based_controller_handle.h
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/include/moveit_simple_controller_manager/empty_controller_handle.h
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/include/moveit_simple_controller_manager/follow_joint_trajectory_controller_handle.h
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/include/moveit_simple_controller_manager/gripper_controller_handle.h
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/environment/library_path.sh
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/environment/library_path.dsv
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/moveit_simple_controller_manager_plugin_description.xml
[0.068s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/ament_index/resource_index/package_run_dependencies/moveit_simple_controller_manager
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/ament_index/resource_index/parent_prefix_path/moveit_simple_controller_manager
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/environment/ament_prefix_path.sh
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/environment/ament_prefix_path.dsv
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/environment/path.sh
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/environment/path.dsv
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/local_setup.bash
[0.069s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/local_setup.sh
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/local_setup.zsh
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/local_setup.dsv
[0.070s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.dsv
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/ament_index/resource_index/packages/moveit_simple_controller_manager
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_simple_controller_manager
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/export_moveit_simple_controller_managerExport.cmake
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/export_moveit_simple_controller_managerExport-release.cmake
[0.070s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/ConfigExtras.cmake
[0.071s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/ament_cmake_export_targets-extras.cmake
[0.071s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/ament_cmake_export_dependencies-extras.cmake
[0.071s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/moveit_simple_controller_managerConfig.cmake
[0.071s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/moveit_simple_controller_managerConfig-version.cmake
[0.071s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.xml
[0.084s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager
