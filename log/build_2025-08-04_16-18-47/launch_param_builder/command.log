Invoking command in '/home/<USER>/ws_moveit2/src/launch_param_builder': PYTHONPATH=/home/<USER>/ws_moveit2/build/launch_param_builder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launch_param_builder build --build-base /home/<USER>/ws_moveit2/build/launch_param_builder/build install --record /home/<USER>/ws_moveit2/build/launch_param_builder/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ws_moveit2/src/launch_param_builder' returned '0': PYTHONPATH=/home/<USER>/ws_moveit2/build/launch_param_builder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launch_param_builder build --build-base /home/<USER>/ws_moveit2/build/launch_param_builder/build install --record /home/<USER>/ws_moveit2/build/launch_param_builder/install.log --single-version-externally-managed install_data
