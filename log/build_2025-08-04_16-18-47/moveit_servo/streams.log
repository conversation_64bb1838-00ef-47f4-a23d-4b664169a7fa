[0.021s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_servo': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_servo -- -j8 -l8
[0.051s] [  6%] Built target gtest_main
[0.058s] [ 12%] Built target fake_command_publisher
[0.065s] [ 18%] Built target servo_controller_input
[0.065s] [ 24%] Built target gtest
[0.085s] [ 33%] Built target moveit_servo_lib_parameters
[0.116s] [ 51%] Built target moveit_servo_lib
[0.122s] [ 57%] Built target test_servo_integration
[0.126s] [ 63%] Built target test_servo_collision
[0.148s] [ 69%] Built target pose_tracking
[0.176s] [ 75%] Built target servo_node
[0.177s] [ 81%] Built target servo_calcs_unit_tests
[0.210s] [ 87%] Built target servo_pose_tracking_demo
[0.210s] [ 93%] Built target test_servo_pose_tracking
[0.212s] [100%] Built target servo_node_main
[0.222s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_servo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_servo -- -j8 -l8
[0.223s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_servo': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_servo
[0.224s] -- Install configuration: "Release"
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libmoveit_servo_lib.so.2.5.9
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libmoveit_servo_lib.so
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libmoveit_servo_lib_parameters.so.2.5.9
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libmoveit_servo_lib_parameters.so
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libpose_tracking.so
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libservo_node.so
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/libservo_controller_input.so
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/moveit_servo/servo_node_main
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/moveit_servo/servo_pose_tracking_demo
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/lib/moveit_servo/fake_command_publisher
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/collision_check.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/enforce_limits.hpp
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/make_shared_from_pool.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/parameter_descriptor_builder.hpp
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/pose_tracking.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/servo.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/servo_calcs.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/servo_node.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/servo_parameters.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/servo_server.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/status_codes.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/include/moveit_servo/utilities.h
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/launch
[0.224s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/launch/pose_tracking_example.launch.py
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/launch/servo_example.launch.py
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/config
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/config/demo_rviz_config.rviz
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/config/demo_rviz_pose_tracking.rviz
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/config/panda_simulated_config.yaml
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/config/panda_simulated_config_pose_tracking.yaml
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/config/pose_tracking_settings.yaml
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/environment/library_path.sh
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/environment/library_path.dsv
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/ament_index/resource_index/package_run_dependencies/moveit_servo
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/ament_index/resource_index/parent_prefix_path/moveit_servo
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/environment/ament_prefix_path.sh
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/environment/ament_prefix_path.dsv
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/environment/path.sh
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/environment/path.dsv
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/local_setup.bash
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/local_setup.sh
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/local_setup.zsh
[0.225s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/local_setup.dsv
[0.225s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/package.dsv
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/ament_index/resource_index/packages/moveit_servo
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/ament_index/resource_index/rclcpp_components/moveit_servo
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/cmake/export_moveit_servoExport.cmake
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/cmake/export_moveit_servoExport-release.cmake
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/cmake/ament_cmake_export_targets-extras.cmake
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/cmake/ament_cmake_export_dependencies-extras.cmake
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/cmake/moveit_servoConfig.cmake
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/cmake/moveit_servoConfig-version.cmake
[0.226s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_servo/share/moveit_servo/package.xml
[0.227s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_servo' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_servo
