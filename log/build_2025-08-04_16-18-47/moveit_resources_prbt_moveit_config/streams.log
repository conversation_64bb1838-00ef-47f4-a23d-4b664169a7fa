[0.020s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config -- -j8 -l8
[0.029s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config -- -j8 -l8
[0.029s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config
[0.031s] -- Install configuration: "Release"
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/launch
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/launch/demo.launch.py
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/launch/moveit.rviz
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/controllers_manipulator.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/joint_limits.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/joint_names.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/kinematics.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/ompl_planning.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/pilz_cartesian_limits.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/prbt.srdf.xacro
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/prbt_controllers.yaml
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/prbt_manipulator.srdf.xacro
[0.031s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/config/prbt_ros_controllers.yaml
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/ament_index/resource_index/package_run_dependencies/moveit_resources_prbt_moveit_config
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/ament_index/resource_index/parent_prefix_path/moveit_resources_prbt_moveit_config
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/environment/ament_prefix_path.sh
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/environment/ament_prefix_path.dsv
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/environment/path.sh
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/environment/path.dsv
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/local_setup.bash
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/local_setup.sh
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/local_setup.zsh
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/local_setup.dsv
[0.032s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/package.dsv
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/ament_index/resource_index/packages/moveit_resources_prbt_moveit_config
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/cmake/moveit_resources_prbt_moveit_configConfig.cmake
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/cmake/moveit_resources_prbt_moveit_configConfig-version.cmake
[0.032s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/package.xml
[0.033s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config
