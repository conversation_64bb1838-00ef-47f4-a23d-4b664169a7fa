[0.044s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_move_group': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_move_group -- -j8 -l8
[0.073s] [ 10%] Built target list_move_group_capabilities
[0.076s] [ 26%] Built target moveit_move_group_capabilities_base
[0.094s] [ 36%] Built target move_group
[0.120s] [100%] Built target moveit_move_group_default_capabilities
[0.124s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_move_group' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_move_group -- -j8 -l8
[0.124s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_move_group': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_move_group
[0.125s] -- Install configuration: "Release"
[0.125s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/moveit_ros_move_group/move_group
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/moveit_ros_move_group/list_move_group_capabilities
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/libmoveit_move_group_default_capabilities.so.2.5.9
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/libmoveit_move_group_default_capabilities.so
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/libmoveit_move_group_capabilities_base.so.2.5.9
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/libmoveit_move_group_capabilities_base.so
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include/moveit
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include/moveit/move_group
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include/moveit/move_group/capability_names.h
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include/moveit/move_group/move_group_capability.h
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include/moveit/move_group/move_group_context.h
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/include/moveit/move_group/node_name.h
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/environment/library_path.sh
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/environment/library_path.dsv
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/moveit_ros_move_group/load_map
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib/moveit_ros_move_group/save_map
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/default_capabilities_plugin_description.xml
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/ament_index/resource_index/package_run_dependencies/moveit_ros_move_group
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/ament_index/resource_index/parent_prefix_path/moveit_ros_move_group
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/environment/ament_prefix_path.sh
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/environment/ament_prefix_path.dsv
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/environment/path.sh
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/environment/path.dsv
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/local_setup.bash
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/local_setup.sh
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/local_setup.zsh
[0.126s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/local_setup.dsv
[0.126s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/package.dsv
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/ament_index/resource_index/packages/moveit_ros_move_group
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/ament_index/resource_index/moveit_ros_move_group__pluginlib__plugin/moveit_ros_move_group
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/export_moveit_ros_move_groupExport.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/export_moveit_ros_move_groupExport-release.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ament_cmake_export_targets-extras.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ament_cmake_export_dependencies-extras.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig-version.cmake
[0.127s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/package.xml
[0.128s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_move_group' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_move_group
