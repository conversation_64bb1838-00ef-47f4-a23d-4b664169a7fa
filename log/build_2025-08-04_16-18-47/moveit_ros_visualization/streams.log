[0.021s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_visualization': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_visualization -- -j8 -l8
[0.045s] [  0%] Built target moveit_rviz_plugin_render_tools_autogen_timestamp_deps
[0.079s] [  1%] Built target moveit_rviz_plugin_render_tools_autogen
[0.131s] [ 18%] Built target moveit_rviz_plugin_render_tools
[0.136s] [ 18%] Built target moveit_planning_scene_rviz_plugin_core_autogen_timestamp_deps
[0.141s] [ 18%] Built target moveit_robot_state_rviz_plugin_core_autogen_timestamp_deps
[0.159s] [ 20%] Built target moveit_robot_state_rviz_plugin_core_autogen
[0.172s] [ 22%] Built target moveit_planning_scene_rviz_plugin_core_autogen
[0.194s] [ 27%] Built target moveit_robot_state_rviz_plugin_core
[0.205s] [ 27%] Built target moveit_robot_state_rviz_plugin_autogen_timestamp_deps
[0.215s] [ 34%] Built target moveit_planning_scene_rviz_plugin_core
[0.216s] [ 36%] Built target moveit_robot_state_rviz_plugin_autogen
[0.221s] [ 36%] Built target moveit_planning_scene_rviz_plugin_autogen_timestamp_deps
[0.221s] [ 36%] Built target moveit_trajectory_rviz_plugin_core_autogen_timestamp_deps
[0.226s] [ 37%] Built target moveit_planning_scene_rviz_plugin_autogen
[0.233s] [ 39%] Built target moveit_trajectory_rviz_plugin_core_autogen
[0.236s] [ 44%] Built target moveit_robot_state_rviz_plugin
[0.246s] [ 50%] Built target moveit_planning_scene_rviz_plugin
[0.251s] [ 55%] Built target moveit_trajectory_rviz_plugin_core
[0.252s] [ 55%] Built target moveit_motion_planning_rviz_plugin_core_autogen_timestamp_deps
[0.257s] [ 55%] Built target moveit_trajectory_rviz_plugin_autogen_timestamp_deps
[0.262s] [ 56%] Built target moveit_trajectory_rviz_plugin_autogen
[0.265s] [ 58%] Built target moveit_motion_planning_rviz_plugin_core_autogen
[0.279s] [ 63%] Built target moveit_trajectory_rviz_plugin
[0.322s] [ 93%] Built target moveit_motion_planning_rviz_plugin_core
[0.327s] [ 93%] Built target moveit_motion_planning_rviz_plugin_autogen_timestamp_deps
[0.332s] [ 94%] Built target moveit_motion_planning_rviz_plugin_autogen
[0.354s] [100%] Built target moveit_motion_planning_rviz_plugin
[0.359s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_visualization' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_visualization -- -j8 -l8
[0.359s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_visualization': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_visualization
[0.362s] -- Install configuration: "Release"
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/octomap_render.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/planning_link_updater.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/planning_scene_render.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/render_shapes.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/robot_state_visualization.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/trajectory_panel.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/rviz_plugin_render_tools/trajectory_visualization.h
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/ogre_helpers
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/ogre_helpers/mesh_shape.hpp
[0.362s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/robot_state_rviz_plugin
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/robot_state_rviz_plugin/robot_state_display.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/planning_scene_rviz_plugin
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/planning_scene_rviz_plugin/background_processing.hpp
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/planning_scene_rviz_plugin/planning_scene_display.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit_planning_scene_rviz_plugin_core_export.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/motion_planning_rviz_plugin
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/motion_planning_rviz_plugin/interactive_marker_display.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/motion_planning_rviz_plugin/motion_planning_display.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/motion_planning_rviz_plugin/motion_planning_frame.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/motion_planning_rviz_plugin/motion_planning_frame_joints_widget.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/motion_planning_rviz_plugin/motion_planning_param_widget.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/trajectory_rviz_plugin
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/include/moveit/trajectory_rviz_plugin/trajectory_display.h
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/icons
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/icons/classes
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/icons/classes/MotionPlanning.png
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/icons/classes/PlanningScene.png
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/icons/classes/RobotState.png
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/icons/classes/Trajectory.png
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/motion_planning_rviz_plugin_description.xml
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/trajectory_rviz_plugin_description.xml
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/planning_scene_rviz_plugin_description.xml
[0.363s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/robot_state_rviz_plugin_description.xml
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_motion_planning_rviz_plugin.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_motion_planning_rviz_plugin.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_motion_planning_rviz_plugin_core.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_motion_planning_rviz_plugin_core.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_planning_scene_rviz_plugin.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_planning_scene_rviz_plugin.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_planning_scene_rviz_plugin_core.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_planning_scene_rviz_plugin_core.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_robot_state_rviz_plugin.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_robot_state_rviz_plugin.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_robot_state_rviz_plugin_core.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_robot_state_rviz_plugin_core.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_rviz_plugin_render_tools.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_rviz_plugin_render_tools.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_trajectory_rviz_plugin.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_trajectory_rviz_plugin.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_trajectory_rviz_plugin_core.so.2.5.9
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib/libmoveit_trajectory_rviz_plugin_core.so
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/environment/library_path.sh
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/environment/library_path.dsv
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/ament_index/resource_index/package_run_dependencies/moveit_ros_visualization
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/ament_index/resource_index/parent_prefix_path/moveit_ros_visualization
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/environment/ament_prefix_path.sh
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/environment/ament_prefix_path.dsv
[0.364s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/environment/path.sh
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/environment/path.dsv
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/local_setup.bash
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/local_setup.sh
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/local_setup.zsh
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/local_setup.dsv
[0.365s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/package.dsv
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/ament_index/resource_index/packages/moveit_ros_visualization
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/ament_index/resource_index/rviz_common__pluginlib__plugin/moveit_ros_visualization
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/export_moveit_ros_visualizationExport.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/export_moveit_ros_visualizationExport-release.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ConfigExtras.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_targets-extras.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/ament_cmake_export_dependencies-extras.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/cmake/moveit_ros_visualizationConfig-version.cmake
[0.365s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/package.xml
[0.368s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_visualization' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_visualization
