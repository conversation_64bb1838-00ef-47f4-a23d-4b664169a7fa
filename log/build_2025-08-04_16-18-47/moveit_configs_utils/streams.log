[0.323s] Invoking command in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/build/moveit_configs_utils/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/moveit_configs_utils build --build-base /home/<USER>/ws_moveit2/build/moveit_configs_utils/build install --record /home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log --single-version-externally-managed install_data
[0.436s] running egg_info
[0.436s] writing ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/PKG-INFO
[0.436s] writing dependency_links to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/dependency_links.txt
[0.437s] writing entry points to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/entry_points.txt
[0.437s] writing requirements to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/requires.txt
[0.437s] writing top-level names to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/top_level.txt
[0.438s] reading manifest file '../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/SOURCES.txt'
[0.439s] writing manifest file '../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/SOURCES.txt'
[0.439s] running build
[0.439s] running build_py
[0.439s] running install
[0.439s] running install_lib
[0.440s] running install_data
[0.440s] running install_egg_info
[0.441s] removing '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages/moveit_configs_utils-2.5.9-py3.10.egg-info' (and everything under it)
[0.441s] Copying ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info to /home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages/moveit_configs_utils-2.5.9-py3.10.egg-info
[0.441s] running install_scripts
[0.452s] writing list of installed files to '/home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log'
[0.464s] Invoked command in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/build/moveit_configs_utils/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/moveit_configs_utils build --build-base /home/<USER>/ws_moveit2/build/moveit_configs_utils/build install --record /home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log --single-version-externally-managed install_data
