Invoking command in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/build/moveit_configs_utils/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/moveit_configs_utils build --build-base /home/<USER>/ws_moveit2/build/moveit_configs_utils/build install --record /home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/build/moveit_configs_utils/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/moveit_configs_utils build --build-base /home/<USER>/ws_moveit2/build/moveit_configs_utils/build install --record /home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log --single-version-externally-managed install_data
