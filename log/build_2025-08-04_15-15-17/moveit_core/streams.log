[0.023s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_core -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_core
[0.078s] -- The CXX compiler identification is GNU 11.4.0
[0.083s] -- Detecting CXX compiler ABI info
[0.121s] -- Detecting CXX compiler ABI info - done
[0.131s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.131s] -- Detecting CXX compile features
[0.131s] -- Detecting CXX compile features - done
[0.135s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.139s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.205s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.295s] -- Found angles: 1.15.0 (/opt/ros/humble/share/angles/cmake)
[0.315s] -- Found Bullet: /usr/lib/aarch64-linux-gnu/libBulletDynamics.so (Required is at least version "2.87")
[0.317s] -- Found common_interfaces: 4.9.0 (/opt/ros/humble/share/common_interfaces/cmake)
[0.319s] -- Found eigen_stl_containers: 1.1.0 (/opt/ros/humble/share/eigen_stl_containers/cmake)
[0.355s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.358s] -- Found Eigen3: TRUE (found version "3.4.0")
[0.362s] -- Found generate_parameter_library: 0.5.0 (/opt/ros/humble/share/generate_parameter_library/cmake)
[0.366s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.401s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.406s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.414s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.425s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.439s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.477s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.480s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.535s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
[0.557s] -- Found FastRTPS: /opt/ros/humble/include
[0.593s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.602s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.656s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.656s] -- Found Threads: TRUE
[0.706s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[0.725s] -- Found geometric_shapes: 2.3.2 (/opt/ros/humble/share/geometric_shapes/cmake)
[0.725s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[0.725s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.725s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.725s]   set the policy and suppress this warning.
[0.725s] 
[0.725s] Call Stack (most recent call first):
[0.725s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[0.725s]   CMakeLists.txt:17 (find_package)
[0.725s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.725s] [0m
[0.734s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[0.795s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[0.842s] -- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
[0.848s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.852s] -- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)
[0.949s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.968s] -- Found srdfdom: 2.0.8 (/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake)
[0.978s] -- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
[1.037s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.042s] -- Found tf2_kdl: 0.25.15 (/opt/ros/humble/share/tf2_kdl/cmake)
[1.046s] [33mCMake Warning (dev) at ConfigExtras.cmake:3 (find_package):
[1.046s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.046s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.046s]   set the policy and suppress this warning.
[1.046s] 
[1.046s] Call Stack (most recent call first):
[1.046s]   CMakeLists.txt:43 (include)
[1.046s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.046s] [0m
[1.072s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.073s] --  *** Building MoveIt 2.5.9-Alpha ***
[1.076s] -- Performing Test COMPILER_HAS_HIDDEN_VISIBILITY
[1.125s] -- Performing Test COMPILER_HAS_HIDDEN_VISIBILITY - Success
[1.126s] -- Performing Test COMPILER_HAS_HIDDEN_INLINE_VISIBILITY
[1.174s] -- Performing Test COMPILER_HAS_HIDDEN_INLINE_VISIBILITY - Success
[1.174s] -- Performing Test COMPILER_HAS_DEPRECATED_ATTR
[1.223s] -- Performing Test COMPILER_HAS_DEPRECATED_ATTR - Success
[1.226s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.228s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.256s] -- The C compiler identification is GNU 11.4.0
[1.260s] -- Detecting C compiler ABI info
[1.303s] -- Detecting C compiler ABI info - done
[1.312s] -- Check for working C compiler: /usr/bin/cc - skipped
[1.313s] -- Detecting C compile features
[1.313s] -- Detecting C compile features - done
[1.316s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.316s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.316s]   CMake.
[1.316s] 
[1.316s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.316s]   to tell CMake that the project requires at least <min> but has been updated
[1.316s]   to work with policies introduced by <max> or earlier.
[1.316s] 
[1.316s] [0m
[1.324s] -- Found ament_cmake_gmock: 1.3.12 (/opt/ros/humble/share/ament_cmake_gmock/cmake)
[1.328s] -- Found gmock sources under '/opt/ros/humble/src/gmock_vendor': C++ tests using 'Google Mock' will be built
[1.331s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gmock_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.331s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.331s]   CMake.
[1.331s] 
[1.331s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.331s]   to tell CMake that the project requires at least <min> but has been updated
[1.331s]   to work with policies introduced by <max> or earlier.
[1.331s] 
[1.331s] [0m
[1.331s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.332s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.339s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.339s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.354s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.355s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.366s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.367s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.367s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.379s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.379s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.387s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.387s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.394s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.394s] -- Found gmock sources under '/opt/ros/humble/src/gmock_vendor': C++ tests using 'Google Mock' will be built
[1.394s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.403s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.411s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.420s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.420s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.436s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.446s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.455s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.460s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.480s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.481s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_core/exceptions/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection_fcl/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection_bullet/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/constraint_samplers/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/controller_manager/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/distance_field/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_distance_field/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/dynamics_solver/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematics_base/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematics_metrics/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_model/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/transforms/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_state/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_trajectory/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematic_constraints/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/macros/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/online_signal_smoothing/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_interface/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_request_adapter/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_scene/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/sensor_manager/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/trajectory_processing/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/utils/include
[1.481s] -- Configured cppcheck exclude dirs and/or files: 
[1.482s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.483s] -- Added test 'xmllint' to check XML markup files
[1.486s] -- Configuring done (1.5s)
[1.917s] -- Generating done (0.4s)
[2.025s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_core
