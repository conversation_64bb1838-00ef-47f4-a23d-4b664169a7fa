-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found message_filters: 4.3.8 (/opt/ros/humble/share/message_filters/cmake)
-- Found srdfdom: 2.0.8 (/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake)
-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
-- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Found moveit_ros_occupancy_map_monitor: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
-- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found ament_cmake_gmock: 1.3.12 (/opt/ros/humble/share/ament_cmake_gmock/cmake)
-- Found ros_testing: 0.4.0 (/opt/ros/humble/share/ros_testing/cmake)
-- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
-- Found gmock sources under '/opt/ros/humble/src/gmock_vendor': C++ tests using 'Google Mock' will be built
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/rdf_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/kinematics_plugin_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/robot_model_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/constraint_sampler_manager_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/planning_pipeline/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/planning_scene_monitor/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/trajectory_execution_manager/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/plan_execution/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/collision_plugin_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/moveit_cpp/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done (1.6s)
-- Generating done (0.5s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_ros_planning
[  2%] Built target moveit_collision_plugin_loader
[  7%] Built target moveit_rdf_loader
[ 11%] Built target gmock
[ 14%] Built target gtest_main
[ 17%] Built target gmock_main
[ 20%] Built target moveit_list_request_adapter_plugins
[ 23%] Built target gtest
[ 26%] Built target moveit_planning_pipeline
[ 29%] Built target moveit_kinematics_plugin_loader
[ 32%] Built target moveit_constraint_sampler_manager_loader
[ 35%] Built target moveit_robot_model_loader
[ 51%] Built target moveit_default_planning_request_adapter_plugins
[ 54%] Built target moveit_print_planning_model_info
[ 63%] Built target moveit_planning_scene_monitor
[ 66%] Built target demo_scene
[ 69%] Built target moveit_visualize_robot_collision_volume
[ 72%] Built target current_state_monitor_tests
[ 75%] Built target moveit_publish_scene_from_text
[ 77%] Built target trajectory_monitor_tests
[ 80%] Built target moveit_print_planning_scene_info
[ 83%] Built target moveit_evaluate_collision_checking_speed
[ 86%] Built target moveit_trajectory_execution_manager
[ 89%] Built target planning_scene_monitor_test
[ 92%] Built target moveit_display_random_state
[ 97%] Built target moveit_cpp
[100%] Built target moveit_plan_execution
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/rdf_loader
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/rdf_loader/rdf_loader.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/rdf_loader/synchronized_string_parameter.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/gonzo.srdf
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/gonzo.urdf
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/kermit.srdf
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/kermit.urdf
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/robin.srdf.xacro
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/launch
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/launch/test_rdf_integration.test.py
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/collision_plugin_loader
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/collision_plugin_loader/collision_plugin_loader.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/kinematics_plugin_loader
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/kinematics_plugin_loader/kinematics_plugin_loader.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/robot_model_loader
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/robot_model_loader/robot_model_loader.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/constraint_sampler_manager_loader
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/constraint_sampler_manager_loader/constraint_sampler_manager_loader.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_pipeline
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_pipeline/planning_pipeline.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit_planning_pipeline_export.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_list_request_adapter_plugins
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/demo_scene
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/current_state_monitor.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/current_state_monitor_middleware_handle.hpp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/planning_scene_monitor.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/trajectory_monitor.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/trajectory_monitor_middleware_handle.hpp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit_planning_scene_monitor_export.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_print_planning_model_info
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_print_planning_scene_info
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_display_random_state
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_visualize_robot_collision_volume
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_evaluate_collision_checking_speed
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_publish_scene_from_text
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/trajectory_execution_manager
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/trajectory_execution_manager/trajectory_execution_manager.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit_trajectory_execution_manager_export.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/plan_execution
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/plan_execution/plan_execution.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/plan_execution/plan_representation.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/moveit_cpp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/moveit_cpp/moveit_cpp.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/moveit_cpp/planning_component.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_rdf_loader.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_rdf_loader.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_kinematics_plugin_loader.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_kinematics_plugin_loader.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_robot_model_loader.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_robot_model_loader.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_constraint_sampler_manager_loader.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_constraint_sampler_manager_loader.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_pipeline.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_pipeline.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_trajectory_execution_manager.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_trajectory_execution_manager.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_plan_execution.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_plan_execution.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_scene_monitor.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_scene_monitor.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_collision_plugin_loader.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_collision_plugin_loader.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_default_planning_request_adapter_plugins.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_default_planning_request_adapter_plugins.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_cpp.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_cpp.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/planning_request_adapters_plugin_description.xml
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/package_run_dependencies/moveit_ros_planning
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/parent_prefix_path/moveit_ros_planning
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.bash
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.sh
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/packages/moveit_ros_planning
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_ros_planning
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/export_moveit_ros_planningExport.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/export_moveit_ros_planningExport-release.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig-version.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.xml
