[0.070s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_planning
[0.645s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.645s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.645s] -- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)
[0.645s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.645s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.651s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.657s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.657s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.876s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.919s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.993s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.993s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.031s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.053s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.080s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.103s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[1.166s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.227s] -- Found message_filters: 4.3.8 (/opt/ros/humble/share/message_filters/cmake)
[1.230s] -- Found srdfdom: 2.0.8 (/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake)
[1.235s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.242s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.252s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.253s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.319s] -- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
[1.322s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[1.322s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.322s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.322s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.322s]   set the policy and suppress this warning.
[1.322s] 
[1.322s] Call Stack (most recent call first):
[1.323s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.323s]   CMakeLists.txt:22 (find_package)
[1.323s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.323s] [0m
[1.336s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.367s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.374s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.386s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.386s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.386s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.386s]   set the policy and suppress this warning.
[1.386s] 
[1.386s] Call Stack (most recent call first):
[1.386s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.386s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.386s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.386s]   CMakeLists.txt:22 (find_package)
[1.386s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.386s] [0m
[1.391s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.421s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.468s] -- Found moveit_ros_occupancy_map_monitor: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/cmake)
[1.475s] [33mCMake Warning (dev) at ConfigExtras.cmake:3 (find_package):
[1.475s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.475s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.475s]   set the policy and suppress this warning.
[1.475s] 
[1.475s] Call Stack (most recent call first):
[1.475s]   CMakeLists.txt:27 (include)
[1.475s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.475s] [0m
[1.480s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[1.533s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.533s] -- Found ament_cmake_gmock: 1.3.12 (/opt/ros/humble/share/ament_cmake_gmock/cmake)
[1.534s] -- Found ros_testing: 0.4.0 (/opt/ros/humble/share/ros_testing/cmake)
[1.534s] -- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
[1.534s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.535s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[1.535s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.535s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.535s]   the cmake_policy command to set the policy and suppress this warning.
[1.535s] 
[1.535s] Call Stack (most recent call first):
[1.535s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.535s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.535s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.535s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.535s]   planning_scene_monitor/CMakeLists.txt:49 (find_package)
[1.535s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.535s] [0m
[1.549s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[1.549s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.549s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.549s]   the cmake_policy command to set the policy and suppress this warning.
[1.549s] 
[1.549s] Call Stack (most recent call first):
[1.549s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.549s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.549s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.549s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.549s]   planning_scene_monitor/CMakeLists.txt:49 (find_package)
[1.549s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.549s] [0m
[1.550s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.550s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.550s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[1.553s] -- Found gmock sources under '/opt/ros/humble/src/gmock_vendor': C++ tests using 'Google Mock' will be built
[1.553s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gmock_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.553s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.554s]   CMake.
[1.554s] 
[1.554s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.554s]   to tell CMake that the project requires at least <min> but has been updated
[1.554s]   to work with policies introduced by <max> or earlier.
[1.554s] 
[1.554s] [0m
[1.554s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.555s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.555s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.555s]   CMake.
[1.555s] 
[1.555s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.555s]   to tell CMake that the project requires at least <min> but has been updated
[1.555s]   to work with policies introduced by <max> or earlier.
[1.555s] 
[1.555s] [0m
[1.597s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.611s] -- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
[1.613s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.617s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[1.617s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.617s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.617s]   the cmake_policy command to set the policy and suppress this warning.
[1.617s] 
[1.617s] Call Stack (most recent call first):
[1.617s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.617s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.617s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.617s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.617s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:36 (find_package)
[1.617s]   CMakeLists.txt:115 (ament_lint_auto_find_test_dependencies)
[1.617s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.617s] [0m
[1.633s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[1.633s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.633s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.633s]   the cmake_policy command to set the policy and suppress this warning.
[1.633s] 
[1.633s] Call Stack (most recent call first):
[1.633s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.633s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.633s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.634s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.634s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:36 (find_package)
[1.634s]   CMakeLists.txt:115 (ament_lint_auto_find_test_dependencies)
[1.635s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.635s] [0m
[1.635s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.635s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.636s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[1.642s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.643s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/rdf_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/kinematics_plugin_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/robot_model_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/constraint_sampler_manager_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/planning_pipeline/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/planning_scene_monitor/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/trajectory_execution_manager/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/plan_execution/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/collision_plugin_loader/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning/moveit_cpp/include
[1.643s] -- Configured cppcheck exclude dirs and/or files: 
[1.645s] -- Added test 'lint_cmake' to check CMake code style
[1.646s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.646s] -- Added test 'xmllint' to check XML markup files
[1.648s] -- Configuring done (1.6s)
[2.140s] -- Generating done (0.5s)
[2.232s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_ros_planning
[2.250s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_planning
[2.250s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_planning -- -j8 -l8
[2.286s] [  2%] Built target moveit_collision_plugin_loader
[2.300s] [  7%] Built target moveit_rdf_loader
[2.302s] [ 11%] Built target gmock
[2.304s] [ 14%] Built target gtest_main
[2.308s] [ 17%] Built target gmock_main
[2.312s] [ 20%] Built target moveit_list_request_adapter_plugins
[2.312s] [ 23%] Built target gtest
[2.313s] [ 26%] Built target moveit_planning_pipeline
[2.334s] [ 29%] Built target moveit_kinematics_plugin_loader
[2.337s] [ 32%] Built target moveit_constraint_sampler_manager_loader
[2.352s] [ 35%] Built target moveit_robot_model_loader
[2.360s] [ 51%] Built target moveit_default_planning_request_adapter_plugins
[2.371s] [ 54%] Built target moveit_print_planning_model_info
[2.420s] [ 63%] Built target moveit_planning_scene_monitor
[2.463s] [ 66%] Built target demo_scene
[2.470s] [ 69%] Built target moveit_visualize_robot_collision_volume
[2.475s] [ 72%] Built target current_state_monitor_tests
[2.521s] [ 75%] Built target moveit_publish_scene_from_text
[2.521s] [ 77%] Built target trajectory_monitor_tests
[2.522s] [ 80%] Built target moveit_print_planning_scene_info
[2.527s] [ 83%] Built target moveit_evaluate_collision_checking_speed
[2.528s] [ 86%] Built target moveit_trajectory_execution_manager
[2.530s] [ 89%] Built target planning_scene_monitor_test
[2.532s] [ 92%] Built target moveit_display_random_state
[2.564s] [ 97%] Built target moveit_cpp
[2.574s] [100%] Built target moveit_plan_execution
[2.581s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_planning -- -j8 -l8
[2.582s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_planning
[2.585s] -- Install configuration: "Release"
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/rdf_loader
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/rdf_loader/rdf_loader.h
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/rdf_loader/synchronized_string_parameter.h
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/gonzo.srdf
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/gonzo.urdf
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/kermit.srdf
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/kermit.urdf
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/data/robin.srdf.xacro
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/launch
[2.585s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/rdf_loader/test/launch/test_rdf_integration.test.py
[2.586s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.586s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/collision_plugin_loader
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/collision_plugin_loader/collision_plugin_loader.h
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/kinematics_plugin_loader
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/kinematics_plugin_loader/kinematics_plugin_loader.h
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/robot_model_loader
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/robot_model_loader/robot_model_loader.h
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/constraint_sampler_manager_loader
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/constraint_sampler_manager_loader/constraint_sampler_manager_loader.h
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_pipeline
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_pipeline/planning_pipeline.h
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit_planning_pipeline_export.h
[2.588s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_list_request_adapter_plugins
[2.589s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/demo_scene
[2.589s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.589s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.589s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor
[2.589s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/current_state_monitor.h
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/current_state_monitor_middleware_handle.hpp
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/planning_scene_monitor.h
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/trajectory_monitor.h
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/planning_scene_monitor/trajectory_monitor_middleware_handle.hpp
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit_planning_scene_monitor_export.h
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_print_planning_model_info
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_print_planning_scene_info
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_display_random_state
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_visualize_robot_collision_volume
[2.590s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_evaluate_collision_checking_speed
[2.591s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/moveit_ros_planning/moveit_publish_scene_from_text
[2.591s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.591s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.591s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/trajectory_execution_manager
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/trajectory_execution_manager/trajectory_execution_manager.h
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit_trajectory_execution_manager_export.h
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/plan_execution
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/plan_execution/plan_execution.h
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/plan_execution/plan_representation.h
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/moveit_cpp
[2.592s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/moveit_cpp/moveit_cpp.h
[2.593s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/include/moveit/moveit_cpp/planning_component.h
[2.593s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_rdf_loader.so.2.5.9
[2.593s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_rdf_loader.so
[2.593s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_kinematics_plugin_loader.so.2.5.9
[2.593s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_kinematics_plugin_loader.so
[2.593s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_robot_model_loader.so.2.5.9
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_robot_model_loader.so
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_constraint_sampler_manager_loader.so.2.5.9
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_constraint_sampler_manager_loader.so
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_pipeline.so.2.5.9
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_pipeline.so
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_trajectory_execution_manager.so.2.5.9
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_trajectory_execution_manager.so
[2.594s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_plan_execution.so.2.5.9
[2.595s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_plan_execution.so
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_scene_monitor.so.2.5.9
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_scene_monitor.so
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_collision_plugin_loader.so.2.5.9
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_collision_plugin_loader.so
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_default_planning_request_adapter_plugins.so.2.5.9
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_default_planning_request_adapter_plugins.so
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_cpp.so.2.5.9
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_cpp.so
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/library_path.sh
[2.596s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/library_path.dsv
[2.596s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/planning_request_adapters_plugin_description.xml
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/package_run_dependencies/moveit_ros_planning
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/parent_prefix_path/moveit_ros_planning
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/ament_prefix_path.sh
[2.597s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/ament_prefix_path.dsv
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/path.sh
[2.597s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/environment/path.dsv
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.bash
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.sh
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.zsh
[2.597s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/local_setup.dsv
[2.597s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.dsv
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/packages/moveit_ros_planning
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_ros_planning
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/export_moveit_ros_planningExport.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/export_moveit_ros_planningExport-release.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_targets-extras.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_dependencies-extras.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig-version.cmake
[2.597s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.xml
[2.599s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_planning
