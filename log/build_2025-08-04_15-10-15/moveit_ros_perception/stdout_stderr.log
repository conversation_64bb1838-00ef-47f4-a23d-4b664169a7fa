-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[31mCMake Error at /usr/local/cmake/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake:227 (message):
  Could NOT find GLUT (missing: GLUT_glut_LIBRARY GLUT_INCLUDE_DIR)
Call Stack (most recent call first):
  /usr/local/cmake/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake:591 (_FPHSA_FAILURE_MESSAGE)
  /usr/local/cmake/share/cmake-4.0/Modules/FindGLUT.cmake:168 (find_package_handle_standard_args)
  CMakeLists.txt:23 (find_package)

[0m
-- Configuring incomplete, errors occurred!
