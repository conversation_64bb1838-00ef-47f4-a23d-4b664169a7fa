[0.042s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_common -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_common
[0.193s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.193s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.193s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.193s] -- Added test 'lint_cmake' to check CMake code style
[0.193s] -- Added test 'xmllint' to check XML markup files
[0.193s] -- Configuring done (0.1s)
[0.193s] -- Generating done (0.0s)
[0.193s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_common
[0.203s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_common -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_common
[0.203s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.248s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.248s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.250s] -- Install configuration: "Release"
[0.250s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/ament_index/resource_index/package_run_dependencies/moveit_common
[0.252s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/ament_index/resource_index/parent_prefix_path/moveit_common
[0.252s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/ament_prefix_path.sh
[0.252s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/ament_prefix_path.dsv
[0.253s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/path.sh
[0.253s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/path.dsv
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.bash
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.sh
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.zsh
[0.254s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.dsv
[0.254s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/ament_index/resource_index/packages/moveit_common
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_common-extras.cmake
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_commonConfig.cmake
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_commonConfig-version.cmake
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.xml
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake
[0.254s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_package.cmake
[0.254s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
