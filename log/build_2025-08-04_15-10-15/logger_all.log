[0.147s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--mixin', 'release']
[0.147s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=['release'], verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffff91a75450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff91a74f40>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff91a74f40>>, mixin_verb=('build',))
[0.286s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.286s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.286s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.286s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.286s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.286s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.286s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ws_moveit2'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.286s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['ignore', 'ignore_ament_install']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ignore'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ignore_ament_install'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['colcon_pkg']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'colcon_pkg'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['colcon_meta']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'colcon_meta'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['ros']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ros'
[0.296s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launch_param_builder' with type 'ros.ament_python' and name 'launch_param_builder'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ignore_ament_install'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['colcon_pkg']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'colcon_pkg'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['colcon_meta']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'colcon_meta'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['ros']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ros'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['cmake', 'python']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'cmake'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'python'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['python_setup_py']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'python_setup_py'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ignore_ament_install'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['colcon_pkg']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'colcon_pkg'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['colcon_meta']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'colcon_meta'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['ros']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ros'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['cmake', 'python']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'cmake'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'python'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['python_setup_py']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'python_setup_py'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['ignore', 'ignore_ament_install']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ignore'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ignore_ament_install'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['colcon_pkg']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'colcon_pkg'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['colcon_meta']
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'colcon_meta'
[0.296s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['ros']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ros'
[0.297s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit' with type 'ros.ament_cmake' and name 'moveit'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) by extensions ['ignore', 'ignore_ament_install']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) by extension 'ignore'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) ignored
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['ignore', 'ignore_ament_install']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ignore'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ignore_ament_install'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['colcon_pkg']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'colcon_pkg'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['colcon_meta']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'colcon_meta'
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['ros']
[0.297s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ros'
[0.298s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_common' with type 'ros.ament_cmake' and name 'moveit_common'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['ignore', 'ignore_ament_install']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ignore'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ignore_ament_install'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['colcon_pkg']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'colcon_pkg'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['colcon_meta']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'colcon_meta'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['ros']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ros'
[0.298s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_configs_utils' with type 'ros.ament_python' and name 'moveit_configs_utils'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['ignore', 'ignore_ament_install']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ignore'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ignore_ament_install'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['colcon_pkg']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'colcon_pkg'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['colcon_meta']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'colcon_meta'
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['ros']
[0.298s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ros'
[0.300s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_core' with type 'ros.ament_cmake' and name 'moveit_core'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) by extensions ['ignore', 'ignore_ament_install']
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) by extension 'ignore'
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) ignored
[0.300s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['ignore', 'ignore_ament_install']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ignore'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ignore_ament_install'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['colcon_pkg']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'colcon_pkg'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['colcon_meta']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'colcon_meta'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['ros']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ros'
[0.301s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_kinematics' with type 'ros.ament_cmake' and name 'moveit_kinematics'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['ignore', 'ignore_ament_install']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ignore'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ignore_ament_install'
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['colcon_pkg']
[0.301s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['ros']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ros'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['cmake', 'python']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'cmake'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'python'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['python_setup_py']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'python_setup_py'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['ignore', 'ignore_ament_install']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ignore'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ignore_ament_install'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['colcon_pkg']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['ros']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ros'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['cmake', 'python']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'cmake'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'python'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['python_setup_py']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'python_setup_py'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['ignore', 'ignore_ament_install']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ignore'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ignore_ament_install'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['colcon_pkg']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'colcon_pkg'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['colcon_meta']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'colcon_meta'
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['ros']
[0.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ros'
[0.303s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_interface' with type 'ros.ament_cmake' and name 'moveit_planners_chomp'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['ignore', 'ignore_ament_install']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ignore'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ignore_ament_install'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['colcon_pkg']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'colcon_pkg'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['colcon_meta']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'colcon_meta'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['ros']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ros'
[0.303s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_motion_planner' with type 'ros.ament_cmake' and name 'chomp_motion_planner'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['ignore', 'ignore_ament_install']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ignore'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ignore_ament_install'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['colcon_pkg']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'colcon_pkg'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['colcon_meta']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'colcon_meta'
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['ros']
[0.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ros'
[0.304s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter' with type 'ros.ament_cmake' and name 'moveit_chomp_optimizer_adapter'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['ignore', 'ignore_ament_install']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ignore'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ignore_ament_install'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['colcon_pkg']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'colcon_pkg'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['colcon_meta']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'colcon_meta'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['ros']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ros'
[0.304s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/moveit_planners' with type 'ros.ament_cmake' and name 'moveit_planners'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['ignore', 'ignore_ament_install']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ignore'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ignore_ament_install'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['colcon_pkg']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'colcon_pkg'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['colcon_meta']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'colcon_meta'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['ros']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ros'
[0.305s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/ompl' with type 'ros.ament_cmake' and name 'moveit_planners_ompl'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['ignore', 'ignore_ament_install']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ignore'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ignore_ament_install'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['colcon_pkg']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'colcon_pkg'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['colcon_meta']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'colcon_meta'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['ros']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ros'
[0.306s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/pilz_industrial_motion_planner' with type 'ros.ament_cmake' and name 'pilz_industrial_motion_planner'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['ignore', 'ignore_ament_install']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ignore'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ignore_ament_install'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['colcon_pkg']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'colcon_pkg'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['colcon_meta']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'colcon_meta'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['ros']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ros'
[0.307s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils' with type 'ros.ament_cmake' and name 'pilz_industrial_motion_planner_testutils'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['ignore', 'ignore_ament_install']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ignore'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ignore_ament_install'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['colcon_pkg']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'colcon_pkg'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['colcon_meta']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'colcon_meta'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['ros']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ros'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['cmake', 'python']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'cmake'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'python'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['python_setup_py']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'python_setup_py'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ignore'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ignore_ament_install'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['colcon_pkg']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'colcon_pkg'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['colcon_meta']
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'colcon_meta'
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['ros']
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ros'
[0.308s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_ikfast_manipulator_plugin'
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ignore'
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ignore_ament_install'
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['colcon_pkg']
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'colcon_pkg'
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['colcon_meta']
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'colcon_meta'
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['ros']
[0.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ros'
[0.308s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_moveit_config'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['ignore', 'ignore_ament_install']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ignore'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ignore_ament_install'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['colcon_pkg']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'colcon_pkg'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['colcon_meta']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'colcon_meta'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['ros']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ros'
[0.309s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_pg70_support' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_pg70_support'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['ignore', 'ignore_ament_install']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ignore'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ignore_ament_install'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['colcon_pkg']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'colcon_pkg'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['colcon_meta']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'colcon_meta'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['ros']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ros'
[0.310s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_support' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_support'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) by extensions ['ignore', 'ignore_ament_install']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) by extension 'ignore'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) ignored
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ignore'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ignore_ament_install'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['colcon_pkg']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'colcon_pkg'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['colcon_meta']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'colcon_meta'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['ros']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ros'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['cmake', 'python']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'cmake'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'python'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['python_setup_py']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'python_setup_py'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ignore'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ignore_ament_install'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['colcon_pkg']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'colcon_pkg'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['colcon_meta']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'colcon_meta'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['ros']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ros'
[0.311s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_plugins' with type 'ros.ament_cmake' and name 'moveit_plugins'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['ignore', 'ignore_ament_install']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ignore'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ignore_ament_install'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['colcon_pkg']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'colcon_pkg'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['colcon_meta']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'colcon_meta'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['ros']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ros'
[0.311s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_ros_control_interface' with type 'ros.ament_cmake' and name 'moveit_ros_control_interface'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['ignore', 'ignore_ament_install']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ignore'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ignore_ament_install'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['colcon_pkg']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'colcon_pkg'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['colcon_meta']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'colcon_meta'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['ros']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ros'
[0.312s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_simple_controller_manager' with type 'ros.ament_cmake' and name 'moveit_simple_controller_manager'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['ignore', 'ignore_ament_install']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ignore'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ignore_ament_install'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['colcon_pkg']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'colcon_pkg'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['colcon_meta']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'colcon_meta'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['ros']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ros'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['cmake', 'python']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'cmake'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'python'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['python_setup_py']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'python_setup_py'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['ignore', 'ignore_ament_install']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ignore'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ignore_ament_install'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['colcon_pkg']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'colcon_pkg'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['colcon_meta']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'colcon_meta'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['ros']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ros'
[0.318s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/benchmarks' with type 'ros.ament_cmake' and name 'moveit_ros_benchmarks'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ignore_ament_install'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['colcon_pkg']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'colcon_pkg'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['colcon_meta']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'colcon_meta'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['ros']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ros'
[0.319s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/hybrid_planning' with type 'ros.ament_cmake' and name 'moveit_hybrid_planning'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['ignore', 'ignore_ament_install']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ignore'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ignore_ament_install'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['colcon_pkg']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'colcon_pkg'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['colcon_meta']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'colcon_meta'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['ros']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ros'
[0.320s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/move_group' with type 'ros.ament_cmake' and name 'moveit_ros_move_group'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['ignore', 'ignore_ament_install']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ignore'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ignore_ament_install'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['colcon_pkg']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'colcon_pkg'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['colcon_meta']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'colcon_meta'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['ros']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ros'
[0.321s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/moveit_ros' with type 'ros.ament_cmake' and name 'moveit_ros'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['ignore', 'ignore_ament_install']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ignore'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ros'
[0.322s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/moveit_servo' with type 'ros.ament_cmake' and name 'moveit_servo'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ignore_ament_install'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['colcon_pkg']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'colcon_pkg'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['colcon_meta']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'colcon_meta'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['ros']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ros'
[0.323s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/occupancy_map_monitor' with type 'ros.ament_cmake' and name 'moveit_ros_occupancy_map_monitor'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['ignore', 'ignore_ament_install']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ignore'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ignore_ament_install'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['colcon_pkg']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'colcon_pkg'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['colcon_meta']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'colcon_meta'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['ros']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ros'
[0.324s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/perception' with type 'ros.ament_cmake' and name 'moveit_ros_perception'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['ignore', 'ignore_ament_install']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ignore'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ignore_ament_install'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['colcon_pkg']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'colcon_pkg'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['colcon_meta']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'colcon_meta'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['ros']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ros'
[0.325s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/planning' with type 'ros.ament_cmake' and name 'moveit_ros_planning'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['ignore', 'ignore_ament_install']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ignore'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ignore_ament_install'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['colcon_pkg']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'colcon_pkg'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['colcon_meta']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'colcon_meta'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['ros']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ros'
[0.327s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/planning_interface' with type 'ros.ament_cmake' and name 'moveit_ros_planning_interface'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ros'
[0.327s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/robot_interaction' with type 'ros.ament_cmake' and name 'moveit_ros_robot_interaction'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ignore'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ignore_ament_install'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['colcon_pkg']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'colcon_pkg'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['colcon_meta']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'colcon_meta'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['ros']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ros'
[0.328s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/visualization' with type 'ros.ament_cmake' and name 'moveit_ros_visualization'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['ignore', 'ignore_ament_install']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ignore'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ignore_ament_install'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['colcon_pkg']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'colcon_pkg'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['colcon_meta']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'colcon_meta'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['ros']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ros'
[0.329s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/warehouse' with type 'ros.ament_cmake' and name 'moveit_ros_warehouse'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['ignore', 'ignore_ament_install']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ignore'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ignore_ament_install'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['colcon_pkg']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'colcon_pkg'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['colcon_meta']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'colcon_meta'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['ros']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ros'
[0.330s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_runtime' with type 'ros.ament_cmake' and name 'moveit_runtime'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['ignore', 'ignore_ament_install']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ignore'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ignore_ament_install'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['colcon_pkg']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'colcon_pkg'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['colcon_meta']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'colcon_meta'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['ros']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ros'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['cmake', 'python']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'cmake'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'python'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['python_setup_py']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'python_setup_py'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['ignore', 'ignore_ament_install']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ignore'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ignore_ament_install'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['colcon_pkg']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'colcon_pkg'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['colcon_meta']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'colcon_meta'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['ros']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ros'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['cmake', 'python']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'cmake'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'python'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['python_setup_py']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'python_setup_py'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['ignore', 'ignore_ament_install']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ignore'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ignore_ament_install'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['colcon_pkg']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'colcon_pkg'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['colcon_meta']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'colcon_meta'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['ros']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ros'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['cmake', 'python']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'cmake'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'python'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['python_setup_py']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'python_setup_py'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ignore'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ignore_ament_install'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['colcon_pkg']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'colcon_pkg'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['colcon_meta']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'colcon_meta'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['ros']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ros'
[0.331s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_app_plugins'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['ignore', 'ignore_ament_install']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ignore'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ignore_ament_install'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['colcon_pkg']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'colcon_pkg'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['colcon_meta']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'colcon_meta'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['ros']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ros'
[0.332s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_assistant' with type 'ros.ament_cmake' and name 'moveit_setup_assistant'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['ignore', 'ignore_ament_install']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ignore'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ignore_ament_install'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['colcon_pkg']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'colcon_pkg'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['colcon_meta']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'colcon_meta'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['ros']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ros'
[0.333s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_controllers' with type 'ros.ament_cmake' and name 'moveit_setup_controllers'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ignore'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ignore_ament_install'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['colcon_pkg']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'colcon_pkg'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['colcon_meta']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'colcon_meta'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['ros']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ros'
[0.333s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_core_plugins'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['ignore', 'ignore_ament_install']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ignore'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ignore_ament_install'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['colcon_pkg']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'colcon_pkg'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['colcon_meta']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'colcon_meta'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['ros']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ros'
[0.334s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_framework' with type 'ros.ament_cmake' and name 'moveit_setup_framework'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) by extension 'ignore'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) ignored
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ignore'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ignore_ament_install'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['colcon_pkg']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'colcon_pkg'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['colcon_meta']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'colcon_meta'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['ros']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ros'
[0.335s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_srdf_plugins'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['ignore', 'ignore_ament_install']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ignore'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ignore_ament_install'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['colcon_pkg']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'colcon_pkg'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['colcon_meta']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'colcon_meta'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['ros']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ros'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['cmake', 'python']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'cmake'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'python'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['python_setup_py']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'python_setup_py'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['ignore', 'ignore_ament_install']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ignore'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ignore_ament_install'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['colcon_pkg']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'colcon_pkg'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['colcon_meta']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'colcon_meta'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['ros']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ros'
[0.336s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2_tutorials' with type 'ros.ament_cmake' and name 'moveit2_tutorials'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['ignore', 'ignore_ament_install']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ignore'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ignore_ament_install'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['colcon_pkg']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'colcon_pkg'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['colcon_meta']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'colcon_meta'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['ros']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ros'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['cmake', 'python']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'cmake'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'python'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['python_setup_py']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'python_setup_py'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['ignore', 'ignore_ament_install']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ignore'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ignore_ament_install'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['colcon_pkg']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'colcon_pkg'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['colcon_meta']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'colcon_meta'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['ros']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ros'
[0.337s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/fanuc_description' with type 'ros.ament_cmake' and name 'moveit_resources_fanuc_description'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ignore'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ignore_ament_install'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['colcon_pkg']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'colcon_pkg'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['colcon_meta']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'colcon_meta'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['ros']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ros'
[0.338s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/fanuc_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_fanuc_moveit_config'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ignore_ament_install'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['colcon_pkg']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'colcon_pkg'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['colcon_meta']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'colcon_meta'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['ros']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ros'
[0.338s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/moveit_resources' with type 'ros.ament_cmake' and name 'moveit_resources'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ignore_ament_install'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['colcon_pkg']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'colcon_pkg'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['colcon_meta']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'colcon_meta'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['ros']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ros'
[0.339s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/panda_description' with type 'ros.ament_cmake' and name 'moveit_resources_panda_description'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ignore'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ignore_ament_install'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['colcon_pkg']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'colcon_pkg'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['colcon_meta']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'colcon_meta'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['ros']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ros'
[0.339s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/panda_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_panda_moveit_config'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['ignore', 'ignore_ament_install']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ignore'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ignore_ament_install'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['colcon_pkg']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'colcon_pkg'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['colcon_meta']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'colcon_meta'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['ros']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ros'
[0.340s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/pr2_description' with type 'ros.ament_cmake' and name 'moveit_resources_pr2_description'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ros'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['cmake', 'python']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'cmake'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'python'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['python_setup_py']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'python_setup_py'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ros'
[0.341s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/capabilities' with type 'ros.ament_cmake' and name 'moveit_task_constructor_capabilities'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['ignore', 'ignore_ament_install']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ros'
[0.341s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/core' with type 'ros.ament_cmake' and name 'moveit_task_constructor_core'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ros'
[0.342s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/demo' with type 'ros.ament_cmake' and name 'moveit_task_constructor_demo'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ros'
[0.343s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/msgs' with type 'ros.ament_cmake' and name 'moveit_task_constructor_msgs'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'colcon_meta'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['ros']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ros'
[0.343s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/rviz_marker_tools' with type 'ros.ament_cmake' and name 'rviz_marker_tools'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'colcon_meta'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['ros']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ros'
[0.344s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/visualization' with type 'ros.ament_cmake' and name 'moveit_task_constructor_visualization'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['ignore', 'ignore_ament_install']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ignore'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ignore_ament_install'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['colcon_pkg']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'colcon_pkg'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['colcon_meta']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'colcon_meta'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['ros']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ros'
[0.345s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_visual_tools' with type 'ros.ament_cmake' and name 'moveit_visual_tools'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['ignore', 'ignore_ament_install']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ignore'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ignore_ament_install'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['colcon_pkg']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'colcon_pkg'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['colcon_meta']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'colcon_meta'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['ros']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ros'
[0.346s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosparam_shortcuts' with type 'ros.ament_cmake' and name 'rosparam_shortcuts'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['ignore', 'ignore_ament_install']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ignore'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ignore_ament_install'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['colcon_pkg']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'colcon_pkg'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['colcon_meta']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'colcon_meta'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['ros']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ros'
[0.346s] DEBUG:colcon.colcon_core.package_identification:Package 'src/srdfdom' with type 'ros.ament_cmake' and name 'srdfdom'
[0.346s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.346s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.346s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.346s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.346s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.393s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.393s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.394s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 330 installed packages in /opt/ros/humble
[0.395s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_target' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_clean_cache' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_clean_first' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_force_configure' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'ament_cmake_args' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'catkin_cmake_args' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.422s] DEBUG:colcon.colcon_core.verb:Building package 'launch_param_builder' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/launch_param_builder', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/launch_param_builder', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/launch_param_builder', 'symlink_install': False, 'test_result_base': None}
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_target' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_clean_cache' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_clean_first' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_force_configure' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'ament_cmake_args' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'catkin_cmake_args' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.422s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_common' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_common', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_common', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_common', 'symlink_install': False, 'test_result_base': None}
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_target' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_clean_first' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_force_configure' from command line to 'False'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'ament_cmake_args' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.422s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.422s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_fanuc_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description', 'symlink_install': False, 'test_result_base': None}
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_target' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_clean_first' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_force_configure' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'ament_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.423s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_panda_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description', 'symlink_install': False, 'test_result_base': None}
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_target' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_clean_first' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_force_configure' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'ament_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.423s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_pr2_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description', 'symlink_install': False, 'test_result_base': None}
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_target' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_clean_cache' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_clean_first' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_force_configure' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'ament_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'catkin_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.423s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_support' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support', 'symlink_install': False, 'test_result_base': None}
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_target' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.423s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.423s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', 'symlink_install': False, 'test_result_base': None}
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_target' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_clean_cache' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_clean_first' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_force_configure' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'ament_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'catkin_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.424s] DEBUG:colcon.colcon_core.verb:Building package 'rosparam_shortcuts' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/rosparam_shortcuts', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/rosparam_shortcuts', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/rosparam_shortcuts', 'symlink_install': False, 'test_result_base': None}
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_target' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_clean_cache' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_clean_first' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_force_configure' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'ament_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'catkin_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.424s] DEBUG:colcon.colcon_core.verb:Building package 'srdfdom' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/srdfdom', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/srdfdom', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/srdfdom', 'symlink_install': False, 'test_result_base': None}
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_target' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_clean_cache' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_clean_first' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_force_configure' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'ament_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'catkin_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.424s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_configs_utils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_configs_utils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_configs_utils', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils', 'symlink_install': False, 'test_result_base': None}
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.424s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.424s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_fanuc_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.425s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_panda_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_target' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.425s] DEBUG:colcon.colcon_core.verb:Building package 'rviz_marker_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/rviz_marker_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/rviz_marker_tools', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools', 'symlink_install': False, 'test_result_base': None}
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_target' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_clean_cache' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_clean_first' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_force_configure' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'ament_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'catkin_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.425s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_core' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_core', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_core', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_core', 'symlink_install': False, 'test_result_base': None}
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_target' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_clean_cache' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_clean_first' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_force_configure' from command line to 'False'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'ament_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'catkin_cmake_args' from command line to 'None'
[0.425s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.425s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources', 'symlink_install': False, 'test_result_base': None}
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_target' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.426s] DEBUG:colcon.colcon_core.verb:Building package 'chomp_motion_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/chomp_motion_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/chomp_motion_planner', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner', 'symlink_install': False, 'test_result_base': None}
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_target' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.426s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_ikfast_manipulator_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin', 'symlink_install': False, 'test_result_base': None}
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_target' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_clean_cache' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_clean_first' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_force_configure' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'ament_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'catkin_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.426s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_occupancy_map_monitor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor', 'symlink_install': False, 'test_result_base': None}
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_target' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_clean_cache' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_clean_first' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_force_configure' from command line to 'False'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'ament_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'catkin_cmake_args' from command line to 'None'
[0.426s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.426s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_simple_controller_manager' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager', 'symlink_install': False, 'test_result_base': None}
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_target' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_clean_cache' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_clean_first' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_force_configure' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'ament_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'catkin_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.427s] DEBUG:colcon.colcon_core.verb:Building package 'pilz_industrial_motion_planner_testutils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils', 'symlink_install': False, 'test_result_base': None}
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_target' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_clean_cache' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_clean_first' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_force_configure' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'ament_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'catkin_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.427s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_chomp_optimizer_adapter' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter', 'symlink_install': False, 'test_result_base': None}
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_target' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_clean_cache' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_clean_first' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_force_configure' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'ament_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'catkin_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.427s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners_chomp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners_chomp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners_chomp', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface', 'symlink_install': False, 'test_result_base': None}
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_target' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.427s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.427s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins', 'symlink_install': False, 'test_result_base': None}
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_target' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.428s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_control_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface', 'symlink_install': False, 'test_result_base': None}
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_target' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_clean_cache' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_clean_first' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_force_configure' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'ament_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'catkin_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.428s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_planning' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_planning', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_planning', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning', 'symlink_install': False, 'test_result_base': None}
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_target' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_clean_cache' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_clean_first' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_force_configure' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'ament_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'catkin_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.428s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_kinematics' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_kinematics', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_kinematics', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics', 'symlink_install': False, 'test_result_base': None}
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_target' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_clean_cache' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_clean_first' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_force_configure' from command line to 'False'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'ament_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'catkin_cmake_args' from command line to 'None'
[0.428s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.429s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners_ompl' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners_ompl', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners_ompl', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl', 'symlink_install': False, 'test_result_base': None}
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_target' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_clean_cache' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_clean_first' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_force_configure' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'ament_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'catkin_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.429s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_perception' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_perception', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_perception', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception', 'symlink_install': False, 'test_result_base': None}
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_target' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_clean_cache' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_clean_first' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_force_configure' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'ament_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'catkin_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.429s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_robot_interaction' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_robot_interaction', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction', 'symlink_install': False, 'test_result_base': None}
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_target' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_clean_cache' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_clean_first' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_force_configure' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'ament_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'catkin_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.429s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_warehouse' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse', 'symlink_install': False, 'test_result_base': None}
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_target' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.429s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.429s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_visual_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_visual_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_visual_tools', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_visual_tools', 'symlink_install': False, 'test_result_base': None}
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_target' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_clean_cache' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_clean_first' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_force_configure' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'ament_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'catkin_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.430s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_benchmarks' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/benchmarks', 'symlink_install': False, 'test_result_base': None}
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_target' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_clean_cache' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_clean_first' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_force_configure' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'ament_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'catkin_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.430s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_move_group' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_move_group', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_move_group', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/move_group', 'symlink_install': False, 'test_result_base': None}
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.430s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_target' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.430s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.430s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_planning_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_planning_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning_interface', 'symlink_install': False, 'test_result_base': None}
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_target' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_clean_cache' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_clean_first' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_force_configure' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'ament_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'catkin_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.431s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_hybrid_planning' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_hybrid_planning', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_hybrid_planning', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/hybrid_planning', 'symlink_install': False, 'test_result_base': None}
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_target' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_clean_cache' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_clean_first' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_force_configure' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'ament_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'catkin_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.431s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_pg70_support' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_pg70_support', 'symlink_install': False, 'test_result_base': None}
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_target' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_clean_cache' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_clean_first' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_force_configure' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'ament_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'catkin_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.431s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_visualization' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_visualization', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_visualization', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization', 'symlink_install': False, 'test_result_base': None}
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_target' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_clean_cache' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_clean_first' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_force_configure' from command line to 'False'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'ament_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'catkin_cmake_args' from command line to 'None'
[0.431s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.431s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_servo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_servo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_servo', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_servo', 'symlink_install': False, 'test_result_base': None}
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_target' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_clean_cache' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_clean_first' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_force_configure' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'ament_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'catkin_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.432s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_ros', 'symlink_install': False, 'test_result_base': None}
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_target' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_clean_cache' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_clean_first' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_force_configure' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'ament_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'catkin_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.432s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_framework' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_framework', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_framework', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_framework', 'symlink_install': False, 'test_result_base': None}
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_target' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.432s] DEBUG:colcon.colcon_core.verb:Building package 'pilz_industrial_motion_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner', 'symlink_install': False, 'test_result_base': None}
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_target' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_clean_cache' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_clean_first' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_force_configure' from command line to 'False'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'ament_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'catkin_cmake_args' from command line to 'None'
[0.432s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.432s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/moveit_planners', 'symlink_install': False, 'test_result_base': None}
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_target' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.433s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_app_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins', 'symlink_install': False, 'test_result_base': None}
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_target' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_clean_cache' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_clean_first' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_force_configure' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'ament_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'catkin_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.433s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_controllers' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_controllers', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_controllers', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_controllers', 'symlink_install': False, 'test_result_base': None}
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_target' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.433s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_core_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_core_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins', 'symlink_install': False, 'test_result_base': None}
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_target' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.433s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.433s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_srdf_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins', 'symlink_install': False, 'test_result_base': None}
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_target' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_clean_cache' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_clean_first' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_force_configure' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'ament_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'catkin_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.434s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_runtime' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_runtime', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_runtime', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_runtime', 'symlink_install': False, 'test_result_base': None}
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_target' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_clean_cache' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_clean_first' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_force_configure' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'ament_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'catkin_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.434s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_assistant' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_assistant', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_assistant', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_assistant', 'symlink_install': False, 'test_result_base': None}
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_target' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_clean_cache' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_clean_first' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_force_configure' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'ament_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'catkin_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.434s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_core' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_core', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/core', 'symlink_install': False, 'test_result_base': None}
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_target' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_clean_cache' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_clean_first' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_force_configure' from command line to 'False'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'ament_cmake_args' from command line to 'None'
[0.434s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'catkin_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.435s] DEBUG:colcon.colcon_core.verb:Building package 'moveit' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit', 'symlink_install': False, 'test_result_base': None}
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_target' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_clean_cache' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_clean_first' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_force_configure' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'ament_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'catkin_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.435s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_capabilities' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_capabilities', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_capabilities', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/capabilities', 'symlink_install': False, 'test_result_base': None}
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_target' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_clean_cache' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_clean_first' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_force_configure' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'ament_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'catkin_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.435s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_visualization' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_visualization', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_visualization', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/visualization', 'symlink_install': False, 'test_result_base': None}
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_target' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_clean_cache' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_clean_first' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_force_configure' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'ament_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'catkin_cmake_args' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.435s] DEBUG:colcon.colcon_core.verb:Building package 'moveit2_tutorials' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit2_tutorials', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit2_tutorials', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2_tutorials', 'symlink_install': False, 'test_result_base': None}
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_target' from command line to 'None'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_clean_cache' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_clean_first' from command line to 'False'
[0.435s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_force_configure' from command line to 'False'
[0.436s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'ament_cmake_args' from command line to 'None'
[0.436s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'catkin_cmake_args' from command line to 'None'
[0.436s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.436s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_demo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_demo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_demo', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/demo', 'symlink_install': False, 'test_result_base': None}
[0.436s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.436s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.437s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description' with build type 'ament_cmake'
[0.437s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description'
[0.440s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.440s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.440s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.442s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_common' with build type 'ament_cmake'
[0.442s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_common'
[0.442s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.442s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.443s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description' with build type 'ament_cmake'
[0.443s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description'
[0.444s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.444s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.445s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/srdfdom' with build type 'ament_cmake'
[0.445s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/srdfdom'
[0.445s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.445s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.446s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_moveit2/src/launch_param_builder' with build type 'ament_python'
[0.446s] Level 1:colcon.colcon_core.shell:create_environment_hook('launch_param_builder', 'ament_prefix_path')
[0.446s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.ps1'
[0.447s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.dsv'
[0.447s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.sh'
[0.448s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.448s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.449s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description' with build type 'ament_cmake'
[0.449s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description'
[0.449s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.449s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.451s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support' with build type 'ament_cmake'
[0.451s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support'
[0.451s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.451s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.453s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs' with build type 'ament_cmake'
[0.453s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs'
[0.453s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.453s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.457s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/panda_description -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description
[0.483s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_common -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_common
[0.485s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/pr2_description -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description
[0.492s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/srdfdom -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/srdfdom
[0.623s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_moveit2/src/launch_param_builder'
[0.623s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.623s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.627s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description
[0.631s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support
[0.640s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs
[0.645s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_common -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_common
[0.645s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.683s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/pr2_description -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description
[0.690s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description -- -j8 -l8
[0.690s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.690s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.691s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/panda_description -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description
[0.695s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_common)
[0.696s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_description -- -j8 -l8
[0.696s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.700s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake module files
[0.701s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake config files
[0.701s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_common', 'cmake_prefix_path')
[0.702s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.ps1'
[0.702s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.dsv'
[0.702s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.sh'
[0.703s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.703s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/pkgconfig/moveit_common.pc'
[0.704s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/python3.10/site-packages'
[0.704s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.704s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.ps1'
[0.704s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv'
[0.705s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh'
[0.706s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.bash'
[0.706s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.zsh'
[0.707s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_common/share/colcon-core/packages/moveit_common)
[0.707s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_common)
[0.707s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake module files
[0.707s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake config files
[0.708s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_common', 'cmake_prefix_path')
[0.708s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.ps1'
[0.708s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.dsv'
[0.708s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.sh'
[0.709s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.709s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/pkgconfig/moveit_common.pc'
[0.709s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/python3.10/site-packages'
[0.709s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.709s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.ps1'
[0.709s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv'
[0.710s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh'
[0.710s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.bash'
[0.710s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.zsh'
[0.711s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_common/share/colcon-core/packages/moveit_common)
[0.711s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools' with build type 'ament_cmake'
[0.711s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools'
[0.711s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.711s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.714s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description -- -j8 -l8
[0.715s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description
[0.716s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_description -- -j8 -l8
[0.717s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_description
[0.728s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rviz_marker_tools
[0.736s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_description)
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake module files
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake config files
[0.737s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_description', 'cmake_prefix_path')
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.ps1'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.dsv'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.sh'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/pkgconfig/moveit_resources_panda_description.pc'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/python3.10/site-packages'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.ps1'
[0.747s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv'
[0.749s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh'
[0.749s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.bash'
[0.753s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.zsh'
[0.753s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/colcon-core/packages/moveit_resources_panda_description)
[0.753s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_description)
[0.753s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake module files
[0.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake config files
[0.754s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_description', 'cmake_prefix_path')
[0.754s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.ps1'
[0.754s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.dsv'
[0.754s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.sh'
[0.755s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.755s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/pkgconfig/moveit_resources_panda_description.pc'
[0.755s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/python3.10/site-packages'
[0.752s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_description
[0.757s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.758s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.ps1'
[0.759s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv'
[0.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh'
[0.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.bash'
[0.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.zsh'
[0.760s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/colcon-core/packages/moveit_resources_panda_description)
[0.760s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config' with build type 'ament_cmake'
[0.760s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config'
[0.760s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.760s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.764s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_pr2_description)
[0.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake module files
[0.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake config files
[0.767s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_pr2_description', 'cmake_prefix_path')
[0.768s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.ps1'
[0.766s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description
[0.769s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.dsv'
[0.769s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.sh'
[0.770s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/pkgconfig/moveit_resources_pr2_description.pc'
[0.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/python3.10/site-packages'
[0.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.771s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.ps1'
[0.772s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv'
[0.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh'
[0.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.bash'
[0.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.zsh'
[0.772s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/colcon-core/packages/moveit_resources_pr2_description)
[0.773s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_pr2_description)
[0.773s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake module files
[0.773s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake config files
[0.773s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_pr2_description', 'cmake_prefix_path')
[0.773s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.ps1'
[0.775s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.dsv'
[0.775s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.sh'
[0.776s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.776s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/pkgconfig/moveit_resources_pr2_description.pc'
[0.777s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/python3.10/site-packages'
[0.777s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.778s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.ps1'
[0.779s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv'
[0.780s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh'
[0.780s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.bash'
[0.780s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.zsh'
[0.781s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/colcon-core/packages/moveit_resources_pr2_description)
[0.785s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/rosparam_shortcuts' with build type 'ament_cmake'
[0.785s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/rosparam_shortcuts'
[0.785s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.785s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.795s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config
[0.797s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/rosparam_shortcuts -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rosparam_shortcuts
[0.814s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description
[0.815s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description -- -j8 -l8
[0.875s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support
[0.875s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support -- -j8 -l8
[0.878s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description -- -j8 -l8
[0.879s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description
[0.892s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_description)
[0.892s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake module files
[0.893s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake config files
[0.893s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_description', 'cmake_prefix_path')
[0.893s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.ps1'
[0.894s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.dsv'
[0.894s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.sh'
[0.894s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description
[0.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/pkgconfig/moveit_resources_fanuc_description.pc'
[0.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/python3.10/site-packages'
[0.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.895s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.ps1'
[0.896s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv'
[0.897s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh'
[0.897s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.bash'
[0.897s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.zsh'
[0.897s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/colcon-core/packages/moveit_resources_fanuc_description)
[0.899s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_description)
[0.899s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake module files
[0.899s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake config files
[0.899s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_description', 'cmake_prefix_path')
[0.900s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.ps1'
[0.900s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.dsv'
[0.900s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.sh'
[0.901s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.901s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/pkgconfig/moveit_resources_fanuc_description.pc'
[0.901s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/python3.10/site-packages'
[0.901s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.902s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.ps1'
[0.902s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv'
[0.903s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh'
[0.903s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.bash'
[0.903s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.zsh'
[0.904s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/colcon-core/packages/moveit_resources_fanuc_description)
[0.905s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config' with build type 'ament_cmake'
[0.905s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config'
[0.905s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.905s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.913s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support -- -j8 -l8
[0.921s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support
[0.930s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config
[0.930s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_prbt_support)
[0.930s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake module files
[0.930s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake config files
[0.930s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_prbt_support', 'cmake_prefix_path')
[0.930s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.ps1'
[0.931s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.dsv'
[0.931s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.sh'
[0.931s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.931s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/pkgconfig/moveit_resources_prbt_support.pc'
[0.931s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/python3.10/site-packages'
[0.931s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.931s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.ps1'
[0.932s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv'
[0.932s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh'
[0.932s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.bash'
[0.932s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.zsh'
[0.932s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/colcon-core/packages/moveit_resources_prbt_support)
[0.933s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support
[0.934s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_prbt_support)
[0.934s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake module files
[0.934s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake config files
[0.934s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_prbt_support', 'cmake_prefix_path')
[0.935s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.ps1'
[0.935s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.dsv'
[0.935s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.sh'
[0.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/pkgconfig/moveit_resources_prbt_support.pc'
[0.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/python3.10/site-packages'
[0.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.944s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.ps1'
[0.944s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv'
[0.944s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh'
[0.944s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.bash'
[0.945s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.zsh'
[0.945s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/colcon-core/packages/moveit_resources_prbt_support)
[1.005s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/src/launch_param_builder': PYTHONPATH=/home/<USER>/ws_moveit2/build/launch_param_builder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launch_param_builder build --build-base /home/<USER>/ws_moveit2/build/launch_param_builder/build install --record /home/<USER>/ws_moveit2/build/launch_param_builder/install.log --single-version-externally-managed install_data
[1.045s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config
[1.045s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config -- -j8 -l8
[1.157s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config
[1.157s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config -- -j8 -l8
[1.160s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config -- -j8 -l8
[1.160s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config
[1.170s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_moveit_config)
[1.170s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake module files
[1.171s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config -- -j8 -l8
[1.172s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config
[1.172s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config
[1.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake config files
[1.174s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_moveit_config', 'cmake_prefix_path')
[1.174s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.ps1'
[1.175s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.dsv'
[1.175s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.sh'
[1.175s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.175s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/pkgconfig/moveit_resources_panda_moveit_config.pc'
[1.176s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/python3.10/site-packages'
[1.176s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.ps1'
[1.176s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv'
[1.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh'
[1.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.bash'
[1.177s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.zsh'
[1.177s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/colcon-core/packages/moveit_resources_panda_moveit_config)
[1.177s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_moveit_config)
[1.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake module files
[1.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake config files
[1.178s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_moveit_config', 'cmake_prefix_path')
[1.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.ps1'
[1.178s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.dsv'
[1.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.sh'
[1.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/pkgconfig/moveit_resources_panda_moveit_config.pc'
[1.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/python3.10/site-packages'
[1.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.ps1'
[1.179s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv'
[1.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh'
[1.180s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.bash'
[1.180s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.zsh'
[1.180s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/colcon-core/packages/moveit_resources_panda_moveit_config)
[1.182s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/srdfdom -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/srdfdom
[1.182s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[1.182s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_moveit_config)
[1.182s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake module files
[1.183s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake config files
[1.183s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_moveit_config', 'cmake_prefix_path')
[1.183s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.ps1'
[1.182s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config
[1.183s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.dsv'
[1.183s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.sh'
[1.184s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[1.184s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/pkgconfig/moveit_resources_fanuc_moveit_config.pc'
[1.184s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/python3.10/site-packages'
[1.184s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[1.184s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.ps1'
[1.184s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv'
[1.185s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh'
[1.185s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.bash'
[1.185s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.zsh'
[1.185s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/colcon-core/packages/moveit_resources_fanuc_moveit_config)
[1.186s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_moveit_config)
[1.186s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake module files
[1.186s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake config files
[1.186s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_moveit_config', 'cmake_prefix_path')
[1.186s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.ps1'
[1.187s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.dsv'
[1.187s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.sh'
[1.187s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[1.187s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/pkgconfig/moveit_resources_fanuc_moveit_config.pc'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/python3.10/site-packages'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[1.188s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.ps1'
[1.188s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv'
[1.188s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh'
[1.189s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.bash'
[1.189s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.zsh'
[1.189s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/colcon-core/packages/moveit_resources_fanuc_moveit_config)
[1.190s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources' with build type 'ament_cmake'
[1.191s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources'
[1.191s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.191s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.197s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder' for CMake module files
[1.198s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder' for CMake config files
[1.198s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder/lib'
[1.198s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder/bin'
[1.198s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder/lib/pkgconfig/launch_param_builder.pc'
[1.198s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages'
[1.198s] Level 1:colcon.colcon_core.shell:create_environment_hook('launch_param_builder', 'pythonpath')
[1.199s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/pythonpath.ps1'
[1.198s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/src/launch_param_builder' returned '0': PYTHONPATH=/home/<USER>/ws_moveit2/build/launch_param_builder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launch_param_builder build --build-base /home/<USER>/ws_moveit2/build/launch_param_builder/build install --record /home/<USER>/ws_moveit2/build/launch_param_builder/install.log --single-version-externally-managed install_data
[1.199s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/pythonpath.dsv'
[1.199s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/pythonpath.sh'
[1.200s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/launch_param_builder/bin'
[1.200s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(launch_param_builder)
[1.200s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/package.ps1'
[1.200s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/package.dsv'
[1.200s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/package.sh'
[1.200s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/package.bash'
[1.201s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/package.zsh'
[1.201s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/launch_param_builder/share/colcon-core/packages/launch_param_builder)
[1.210s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources
[1.369s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources
[1.369s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources -- -j8 -l8
[1.372s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[1.372s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[1.392s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources -- -j8 -l8
[1.392s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources
[1.399s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(srdfdom)
[1.399s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake module files
[1.400s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake config files
[1.400s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'cmake_prefix_path')
[1.400s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.ps1'
[1.400s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[1.400s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.dsv'
[1.400s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.sh'
[1.401s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib'
[1.401s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'ld_library_path_lib')
[1.401s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.ps1'
[1.401s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.dsv'
[1.401s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.sh'
[1.401s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[1.401s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/pkgconfig/srdfdom.pc'
[1.401s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/python3.10/site-packages'
[1.402s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[1.402s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.ps1'
[1.402s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv'
[1.402s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh'
[1.402s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.bash'
[1.402s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.zsh'
[1.403s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/srdfdom/share/colcon-core/packages/srdfdom)
[1.403s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(srdfdom)
[1.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake module files
[1.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake config files
[1.403s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'cmake_prefix_path')
[1.403s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.ps1'
[1.404s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.dsv'
[1.404s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.sh'
[1.404s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib'
[1.404s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'ld_library_path_lib')
[1.404s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.ps1'
[1.405s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.dsv'
[1.405s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.sh'
[1.405s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[1.405s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/pkgconfig/srdfdom.pc'
[1.405s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/python3.10/site-packages'
[1.405s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[1.405s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.ps1'
[1.405s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv'
[1.406s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh'
[1.406s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.bash'
[1.406s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.zsh'
[1.406s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/srdfdom/share/colcon-core/packages/srdfdom)
[1.407s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_core' with build type 'ament_cmake'
[1.407s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_core'
[1.407s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.407s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.409s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils' with build type 'ament_python'
[1.410s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_configs_utils', 'ament_prefix_path')
[1.410s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/hook/ament_prefix_path.ps1'
[1.410s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/hook/ament_prefix_path.dsv'
[1.410s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/hook/ament_prefix_path.sh'
[1.411s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.411s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.413s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources)
[1.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources' for CMake module files
[1.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources' for CMake config files
[1.414s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources', 'cmake_prefix_path')
[1.414s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/hook/cmake_prefix_path.ps1'
[1.414s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources
[1.415s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/hook/cmake_prefix_path.dsv'
[1.415s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/hook/cmake_prefix_path.sh'
[1.415s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/bin'
[1.416s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/lib/pkgconfig/moveit_resources.pc'
[1.416s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/lib/python3.10/site-packages'
[1.416s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/bin'
[1.416s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.ps1'
[1.416s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.dsv'
[1.417s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.sh'
[1.417s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.bash'
[1.417s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.zsh'
[1.417s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources/share/colcon-core/packages/moveit_resources)
[1.417s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources)
[1.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources' for CMake module files
[1.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources' for CMake config files
[1.418s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources', 'cmake_prefix_path')
[1.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/hook/cmake_prefix_path.ps1'
[1.418s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/hook/cmake_prefix_path.dsv'
[1.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/hook/cmake_prefix_path.sh'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/bin'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/lib/pkgconfig/moveit_resources.pc'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/lib/python3.10/site-packages'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources/bin'
[1.419s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.ps1'
[1.419s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.dsv'
[1.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.sh'
[1.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.bash'
[1.421s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.zsh'
[1.421s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources/share/colcon-core/packages/moveit_resources)
[1.423s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rviz_marker_tools
[1.423s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[1.425s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_core -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_core
[1.555s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils'
[1.555s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.555s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.560s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[1.560s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[1.560s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/rosparam_shortcuts -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rosparam_shortcuts
[1.560s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[1.566s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rviz_marker_tools)
[1.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake module files
[1.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake config files
[1.569s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[1.569s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'cmake_prefix_path')
[1.569s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.ps1'
[1.570s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.dsv'
[1.570s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.sh'
[1.570s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib'
[1.570s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'ld_library_path_lib')
[1.570s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.ps1'
[1.571s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.dsv'
[1.571s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.sh'
[1.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/pkgconfig/rviz_marker_tools.pc'
[1.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/python3.10/site-packages'
[1.571s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.571s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.ps1'
[1.571s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv'
[1.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.sh'
[1.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.bash'
[1.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.zsh'
[1.572s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/colcon-core/packages/rviz_marker_tools)
[1.572s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rviz_marker_tools)
[1.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake module files
[1.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake config files
[1.573s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'cmake_prefix_path')
[1.573s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.ps1'
[1.573s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.dsv'
[1.573s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.sh'
[1.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib'
[1.574s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'ld_library_path_lib')
[1.574s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.ps1'
[1.574s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.dsv'
[1.574s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.sh'
[1.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/pkgconfig/rviz_marker_tools.pc'
[1.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/python3.10/site-packages'
[1.575s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.575s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.ps1'
[1.575s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv'
[1.575s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.sh'
[1.575s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.bash'
[1.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.zsh'
[1.576s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/colcon-core/packages/rviz_marker_tools)
[1.616s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[1.616s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[1.619s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rosparam_shortcuts)
[1.620s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts' for CMake module files
[1.620s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts' for CMake config files
[1.620s] Level 1:colcon.colcon_core.shell:create_environment_hook('rosparam_shortcuts', 'cmake_prefix_path')
[1.620s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/cmake_prefix_path.ps1'
[1.620s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/cmake_prefix_path.dsv'
[1.620s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/cmake_prefix_path.sh'
[1.621s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib'
[1.621s] Level 1:colcon.colcon_core.shell:create_environment_hook('rosparam_shortcuts', 'ld_library_path_lib')
[1.621s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/ld_library_path_lib.ps1'
[1.621s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/ld_library_path_lib.dsv'
[1.621s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/ld_library_path_lib.sh'
[1.621s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/bin'
[1.621s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[1.621s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/pkgconfig/rosparam_shortcuts.pc'
[1.622s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/python3.10/site-packages'
[1.622s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/bin'
[1.622s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.ps1'
[1.622s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.dsv'
[1.622s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.sh'
[1.622s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.bash'
[1.623s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.zsh'
[1.623s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/colcon-core/packages/rosparam_shortcuts)
[1.623s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rosparam_shortcuts)
[1.623s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts' for CMake module files
[1.624s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts' for CMake config files
[1.624s] Level 1:colcon.colcon_core.shell:create_environment_hook('rosparam_shortcuts', 'cmake_prefix_path')
[1.624s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/cmake_prefix_path.ps1'
[1.624s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/cmake_prefix_path.dsv'
[1.624s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/cmake_prefix_path.sh'
[1.624s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib'
[1.624s] Level 1:colcon.colcon_core.shell:create_environment_hook('rosparam_shortcuts', 'ld_library_path_lib')
[1.624s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/ld_library_path_lib.ps1'
[1.625s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/ld_library_path_lib.dsv'
[1.625s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/hook/ld_library_path_lib.sh'
[1.625s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/bin'
[1.625s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/pkgconfig/rosparam_shortcuts.pc'
[1.625s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/python3.10/site-packages'
[1.625s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/bin'
[1.626s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.ps1'
[1.626s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.dsv'
[1.626s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.sh'
[1.626s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.bash'
[1.626s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.zsh'
[1.627s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/colcon-core/packages/rosparam_shortcuts)
[1.773s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/build/moveit_configs_utils/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/moveit_configs_utils build --build-base /home/<USER>/ws_moveit2/build/moveit_configs_utils/build install --record /home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log --single-version-externally-managed install_data
[1.907s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils' for CMake module files
[1.907s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils' for CMake config files
[1.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib'
[1.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils/bin'
[1.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/pkgconfig/moveit_configs_utils.pc'
[1.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages'
[1.908s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_configs_utils', 'pythonpath')
[1.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/hook/pythonpath.ps1'
[1.908s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/hook/pythonpath.dsv'
[1.908s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/hook/pythonpath.sh'
[1.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_configs_utils/bin'
[1.909s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_configs_utils)
[1.909s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/package.ps1'
[1.909s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/srdfdom LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/build/moveit_configs_utils/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/moveit_configs_utils build --build-base /home/<USER>/ws_moveit2/build/moveit_configs_utils/build install --record /home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log --single-version-externally-managed install_data
[1.909s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/package.dsv'
[1.909s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/package.sh'
[1.910s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/package.bash'
[1.910s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/package.zsh'
[1.910s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/colcon-core/packages/moveit_configs_utils)
[2.638s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs
[2.638s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[2.951s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_core' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_core -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_core
[2.952s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_core -- -j8 -l8
[3.780s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_core' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_core -- -j8 -l8
[3.781s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_core
[3.841s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_core)
[3.842s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core' for CMake module files
[3.842s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_core' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_core
[3.842s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core' for CMake config files
[3.843s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'cmake_prefix_path')
[3.843s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/cmake_prefix_path.ps1'
[3.843s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/cmake_prefix_path.dsv'
[3.843s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/cmake_prefix_path.sh'
[3.846s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/lib'
[3.846s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'ld_library_path_lib')
[3.846s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/ld_library_path_lib.ps1'
[3.847s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/ld_library_path_lib.dsv'
[3.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/ld_library_path_lib.sh'
[3.847s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/bin'
[3.847s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'path')
[3.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/path.ps1'
[3.847s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/path.dsv'
[3.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/path.sh'
[3.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/lib/pkgconfig/moveit_core.pc'
[3.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/lib/python3.10/site-packages'
[3.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/bin'
[3.848s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'pythonscriptspath')
[3.848s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/pythonscriptspath.ps1'
[3.851s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/pythonscriptspath.dsv'
[3.851s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/pythonscriptspath.sh'
[3.852s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.ps1'
[3.852s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.dsv'
[3.853s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.sh'
[3.853s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.bash'
[3.853s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.zsh'
[3.853s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_core/share/colcon-core/packages/moveit_core)
[3.854s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_core)
[3.854s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core' for CMake module files
[3.854s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core' for CMake config files
[3.854s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'cmake_prefix_path')
[3.855s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/cmake_prefix_path.ps1'
[3.858s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/cmake_prefix_path.dsv'
[3.858s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/cmake_prefix_path.sh'
[3.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/lib'
[3.859s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'ld_library_path_lib')
[3.859s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/ld_library_path_lib.ps1'
[3.859s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/ld_library_path_lib.dsv'
[3.859s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/ld_library_path_lib.sh'
[3.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/bin'
[3.859s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'path')
[3.859s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/path.ps1'
[3.860s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/path.dsv'
[3.860s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/path.sh'
[3.860s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/lib/pkgconfig/moveit_core.pc'
[3.860s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/lib/python3.10/site-packages'
[3.860s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_core/bin'
[3.860s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_core', 'pythonscriptspath')
[3.860s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/pythonscriptspath.ps1'
[3.860s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/pythonscriptspath.dsv'
[3.860s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/hook/pythonscriptspath.sh'
[3.861s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.ps1'
[3.861s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.dsv'
[3.865s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.sh'
[3.865s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.bash'
[3.866s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.zsh'
[3.866s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_core/share/colcon-core/packages/moveit_core)
[3.867s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor' with build type 'ament_cmake'
[3.867s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor'
[3.867s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.867s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.873s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager' with build type 'ament_cmake'
[3.873s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager'
[3.873s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.873s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.877s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin' with build type 'ament_cmake'
[3.877s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin'
[3.877s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.877s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.882s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner' with build type 'ament_cmake'
[3.882s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner'
[3.882s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.883s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.897s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils' with build type 'ament_cmake'
[3.897s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils'
[3.897s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.897s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.910s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor
[3.929s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin
[3.938s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager
[3.950s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/chomp_motion_planner
[3.959s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils
[5.592s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin
[5.593s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin -- -j8 -l8
[5.846s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/chomp_motion_planner
[5.846s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/chomp_motion_planner -- -j8 -l8
[5.920s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor
[5.924s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor -- -j8 -l8
[5.961s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/chomp_motion_planner -- -j8 -l8
[5.964s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/chomp_motion_planner
[5.990s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(chomp_motion_planner)
[5.993s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner' for CMake module files
[5.995s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner' for CMake config files
[5.995s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager
[5.995s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager -- -j8 -l8
[5.995s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/chomp_motion_planner
[5.999s] Level 1:colcon.colcon_core.shell:create_environment_hook('chomp_motion_planner', 'cmake_prefix_path')
[5.999s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/cmake_prefix_path.ps1'
[6.000s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/cmake_prefix_path.dsv'
[6.000s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/cmake_prefix_path.sh'
[6.000s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib'
[6.000s] Level 1:colcon.colcon_core.shell:create_environment_hook('chomp_motion_planner', 'ld_library_path_lib')
[6.000s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/ld_library_path_lib.ps1'
[6.000s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/ld_library_path_lib.dsv'
[6.000s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/ld_library_path_lib.sh'
[6.001s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/bin'
[6.001s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/pkgconfig/chomp_motion_planner.pc'
[6.001s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/python3.10/site-packages'
[6.001s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/bin'
[6.001s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.ps1'
[6.001s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.dsv'
[6.001s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.sh'
[6.002s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.bash'
[6.002s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.zsh'
[6.002s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/colcon-core/packages/chomp_motion_planner)
[6.005s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(chomp_motion_planner)
[6.005s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner' for CMake module files
[6.006s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner' for CMake config files
[6.009s] Level 1:colcon.colcon_core.shell:create_environment_hook('chomp_motion_planner', 'cmake_prefix_path')
[6.009s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/cmake_prefix_path.ps1'
[6.016s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/cmake_prefix_path.dsv'
[6.017s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/cmake_prefix_path.sh'
[6.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib'
[6.021s] Level 1:colcon.colcon_core.shell:create_environment_hook('chomp_motion_planner', 'ld_library_path_lib')
[6.021s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/ld_library_path_lib.ps1'
[6.022s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/ld_library_path_lib.dsv'
[6.022s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/hook/ld_library_path_lib.sh'
[6.023s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/bin'
[6.023s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/pkgconfig/chomp_motion_planner.pc'
[6.023s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/python3.10/site-packages'
[6.023s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/chomp_motion_planner/bin'
[6.023s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.ps1'
[6.023s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.dsv'
[6.023s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.sh'
[6.024s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.bash'
[6.024s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.zsh'
[6.024s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/colcon-core/packages/chomp_motion_planner)
[6.027s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface' with build type 'ament_cmake'
[6.028s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface'
[6.028s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[6.028s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[6.031s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter' with build type 'ament_cmake'
[6.032s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter'
[6.032s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[6.032s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[6.040s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils
[6.041s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils -- -j8 -l8
[6.065s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_planners_chomp
[6.091s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter
[6.128s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager -- -j8 -l8
[6.128s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager
[6.136s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor -- -j8 -l8
[6.136s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor
[6.153s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_simple_controller_manager)
[6.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager' for CMake module files
[6.154s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_simple_controller_manager
[6.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager' for CMake config files
[6.157s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_simple_controller_manager', 'cmake_prefix_path')
[6.158s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/cmake_prefix_path.ps1'
[6.158s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/cmake_prefix_path.dsv'
[6.158s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/cmake_prefix_path.sh'
[6.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib'
[6.158s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_simple_controller_manager', 'ld_library_path_lib')
[6.159s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/ld_library_path_lib.ps1'
[6.159s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/ld_library_path_lib.dsv'
[6.159s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/ld_library_path_lib.sh'
[6.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/bin'
[6.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib/pkgconfig/moveit_simple_controller_manager.pc'
[6.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib/python3.10/site-packages'
[6.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/bin'
[6.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.ps1'
[6.160s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.dsv'
[6.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.sh'
[6.164s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.bash'
[6.164s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.zsh'
[6.164s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/colcon-core/packages/moveit_simple_controller_manager)
[6.164s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_simple_controller_manager)
[6.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager' for CMake module files
[6.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager' for CMake config files
[6.165s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_simple_controller_manager', 'cmake_prefix_path')
[6.165s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/cmake_prefix_path.ps1'
[6.165s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/cmake_prefix_path.dsv'
[6.165s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/cmake_prefix_path.sh'
[6.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib'
[6.165s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_simple_controller_manager', 'ld_library_path_lib')
[6.166s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/ld_library_path_lib.ps1'
[6.174s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/ld_library_path_lib.dsv'
[6.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/hook/ld_library_path_lib.sh'
[6.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/bin'
[6.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib/pkgconfig/moveit_simple_controller_manager.pc'
[6.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib/python3.10/site-packages'
[6.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/bin'
[6.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.ps1'
[6.179s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.dsv'
[6.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.sh'
[6.180s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.bash'
[6.180s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.zsh'
[6.180s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/colcon-core/packages/moveit_simple_controller_manager)
[6.180s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins' with build type 'ament_cmake'
[6.180s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins'
[6.180s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[6.181s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[6.189s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface' with build type 'ament_cmake'
[6.189s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface'
[6.189s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[6.189s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[6.206s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_occupancy_map_monitor)
[6.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor' for CMake module files
[6.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor' for CMake config files
[6.207s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_occupancy_map_monitor', 'cmake_prefix_path')
[6.207s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/cmake_prefix_path.ps1'
[6.207s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/cmake_prefix_path.dsv'
[6.208s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/cmake_prefix_path.sh'
[6.208s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib'
[6.208s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor
[6.212s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_occupancy_map_monitor', 'ld_library_path_lib')
[6.214s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/ld_library_path_lib.ps1'
[6.216s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/ld_library_path_lib.dsv'
[6.222s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/ld_library_path_lib.sh'
[6.222s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/bin'
[6.222s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib/pkgconfig/moveit_ros_occupancy_map_monitor.pc'
[6.223s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib/python3.10/site-packages'
[6.223s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/bin'
[6.223s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.ps1'
[6.227s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.dsv'
[6.227s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.sh'
[6.228s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.bash'
[6.230s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.zsh'
[6.230s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/colcon-core/packages/moveit_ros_occupancy_map_monitor)
[6.234s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_occupancy_map_monitor)
[6.235s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor' for CMake module files
[6.235s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor' for CMake config files
[6.235s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_occupancy_map_monitor', 'cmake_prefix_path')
[6.235s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/cmake_prefix_path.ps1'
[6.236s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/cmake_prefix_path.dsv'
[6.236s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/cmake_prefix_path.sh'
[6.236s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib'
[6.236s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_occupancy_map_monitor', 'ld_library_path_lib')
[6.237s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/ld_library_path_lib.ps1'
[6.240s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/ld_library_path_lib.dsv'
[6.240s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/hook/ld_library_path_lib.sh'
[6.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/bin'
[6.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib/pkgconfig/moveit_ros_occupancy_map_monitor.pc'
[6.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib/python3.10/site-packages'
[6.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/bin'
[6.241s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.ps1'
[6.241s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.dsv'
[6.242s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.sh'
[6.242s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.bash'
[6.242s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.zsh'
[6.242s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/colcon-core/packages/moveit_ros_occupancy_map_monitor)
[6.243s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning' with build type 'ament_cmake'
[6.243s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning'
[6.243s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[6.243s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[6.251s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_plugins
[6.259s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_control_interface
[6.264s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils -- -j8 -l8
[6.270s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils
[6.313s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_planning
[6.883s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pilz_industrial_motion_planner_testutils)
[6.883s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils' for CMake module files
[6.883s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils' for CMake config files
[6.883s] Level 1:colcon.colcon_core.shell:create_environment_hook('pilz_industrial_motion_planner_testutils', 'cmake_prefix_path')
[6.884s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/cmake_prefix_path.ps1'
[6.896s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/cmake_prefix_path.dsv'
[6.900s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils
[6.904s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/cmake_prefix_path.sh'
[6.905s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib'
[6.905s] Level 1:colcon.colcon_core.shell:create_environment_hook('pilz_industrial_motion_planner_testutils', 'ld_library_path_lib')
[6.905s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/ld_library_path_lib.ps1'
[6.905s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/ld_library_path_lib.dsv'
[6.905s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/ld_library_path_lib.sh'
[6.905s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/bin'
[6.905s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib/pkgconfig/pilz_industrial_motion_planner_testutils.pc'
[6.905s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib/python3.10/site-packages'
[6.906s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/bin'
[6.906s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.ps1'
[6.906s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.dsv'
[6.906s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.sh'
[6.913s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.bash'
[6.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.zsh'
[6.914s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/colcon-core/packages/pilz_industrial_motion_planner_testutils)
[6.914s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pilz_industrial_motion_planner_testutils)
[6.914s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils' for CMake module files
[6.915s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils' for CMake config files
[6.915s] Level 1:colcon.colcon_core.shell:create_environment_hook('pilz_industrial_motion_planner_testutils', 'cmake_prefix_path')
[6.915s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/cmake_prefix_path.ps1'
[6.915s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/cmake_prefix_path.dsv'
[6.919s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/cmake_prefix_path.sh'
[6.923s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib'
[6.923s] Level 1:colcon.colcon_core.shell:create_environment_hook('pilz_industrial_motion_planner_testutils', 'ld_library_path_lib')
[6.923s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/ld_library_path_lib.ps1'
[6.924s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/ld_library_path_lib.dsv'
[6.924s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/hook/ld_library_path_lib.sh'
[6.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/bin'
[6.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib/pkgconfig/pilz_industrial_motion_planner_testutils.pc'
[6.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib/python3.10/site-packages'
[6.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/bin'
[6.925s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.ps1'
[6.925s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.dsv'
[6.925s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.sh'
[6.925s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.bash'
[6.926s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.zsh'
[6.926s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/colcon-core/packages/pilz_industrial_motion_planner_testutils)
[6.933s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_plugins
[6.937s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_plugins -- -j8 -l8
[7.832s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter
[7.832s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter -- -j8 -l8
[7.833s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_plugins -- -j8 -l8
[7.840s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_plugins': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_plugins
[7.857s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_plugins)
[7.857s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins' for CMake module files
[7.857s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins' for CMake config files
[7.857s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_plugins', 'cmake_prefix_path')
[7.858s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/hook/cmake_prefix_path.ps1'
[7.859s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/hook/cmake_prefix_path.dsv'
[7.859s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/hook/cmake_prefix_path.sh'
[7.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/bin'
[7.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/lib/pkgconfig/moveit_plugins.pc'
[7.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/lib/python3.10/site-packages'
[7.859s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/bin'
[7.859s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.ps1'
[7.861s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_plugins' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_plugins
[7.861s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.dsv'
[7.861s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.sh'
[7.861s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.bash'
[7.861s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.zsh'
[7.861s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_plugins/share/colcon-core/packages/moveit_plugins)
[7.862s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_plugins)
[7.862s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins' for CMake module files
[7.862s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins' for CMake config files
[7.862s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_plugins', 'cmake_prefix_path')
[7.862s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/hook/cmake_prefix_path.ps1'
[7.862s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/hook/cmake_prefix_path.dsv'
[7.862s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/hook/cmake_prefix_path.sh'
[7.863s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/bin'
[7.863s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/lib/pkgconfig/moveit_plugins.pc'
[7.863s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/lib/python3.10/site-packages'
[7.863s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_plugins/bin'
[7.863s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.ps1'
[7.863s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.dsv'
[7.863s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.sh'
[7.864s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.bash'
[7.864s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.zsh'
[7.864s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_plugins/share/colcon-core/packages/moveit_plugins)
[7.867s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_planners_chomp
[7.867s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_planners_chomp -- -j8 -l8
[7.914s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter -- -j8 -l8
[7.919s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter
[7.933s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_chomp_optimizer_adapter)
[7.934s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter' for CMake module files
[7.934s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter' for CMake config files
[7.934s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_chomp_optimizer_adapter', 'cmake_prefix_path')
[7.934s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/cmake_prefix_path.ps1'
[7.938s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter
[7.939s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/cmake_prefix_path.dsv'
[7.941s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/cmake_prefix_path.sh'
[7.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib'
[7.941s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_chomp_optimizer_adapter', 'ld_library_path_lib')
[7.941s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/ld_library_path_lib.ps1'
[7.942s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/ld_library_path_lib.dsv'
[7.942s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/ld_library_path_lib.sh'
[7.942s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/bin'
[7.942s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib/pkgconfig/moveit_chomp_optimizer_adapter.pc'
[7.942s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib/python3.10/site-packages'
[7.942s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/bin'
[7.942s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.ps1'
[7.943s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.dsv'
[7.943s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.sh'
[7.943s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.bash'
[7.943s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.zsh'
[7.943s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/colcon-core/packages/moveit_chomp_optimizer_adapter)
[7.944s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_chomp_optimizer_adapter)
[7.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter' for CMake module files
[7.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter' for CMake config files
[7.944s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_chomp_optimizer_adapter', 'cmake_prefix_path')
[7.944s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/cmake_prefix_path.ps1'
[7.944s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/cmake_prefix_path.dsv'
[7.944s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/cmake_prefix_path.sh'
[7.945s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib'
[7.945s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_chomp_optimizer_adapter', 'ld_library_path_lib')
[7.945s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/ld_library_path_lib.ps1'
[7.945s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/ld_library_path_lib.dsv'
[7.945s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/hook/ld_library_path_lib.sh'
[7.945s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/bin'
[7.945s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib/pkgconfig/moveit_chomp_optimizer_adapter.pc'
[7.945s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib/python3.10/site-packages'
[7.945s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/bin'
[7.945s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.ps1'
[7.947s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.dsv'
[7.948s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.sh'
[7.948s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.bash'
[7.948s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.zsh'
[7.949s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/colcon-core/packages/moveit_chomp_optimizer_adapter)
[7.956s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_planners_chomp -- -j8 -l8
[7.957s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_planners_chomp
[7.965s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_planners_chomp)
[7.966s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp' for CMake module files
[7.967s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_planners_chomp
[7.969s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp' for CMake config files
[7.969s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_planners_chomp', 'cmake_prefix_path')
[7.970s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/cmake_prefix_path.ps1'
[7.971s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/cmake_prefix_path.dsv'
[7.971s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/cmake_prefix_path.sh'
[7.973s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib'
[7.973s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_planners_chomp', 'ld_library_path_lib')
[7.973s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/ld_library_path_lib.ps1'
[7.973s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/ld_library_path_lib.dsv'
[7.973s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/ld_library_path_lib.sh'
[7.974s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/bin'
[7.974s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/pkgconfig/moveit_planners_chomp.pc'
[7.974s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/python3.10/site-packages'
[7.974s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/bin'
[7.974s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.ps1'
[7.974s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.dsv'
[7.975s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.sh'
[7.975s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.bash'
[7.976s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.zsh'
[7.976s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/colcon-core/packages/moveit_planners_chomp)
[7.977s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_planners_chomp)
[7.977s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp' for CMake module files
[7.977s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp' for CMake config files
[7.977s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_planners_chomp', 'cmake_prefix_path')
[7.977s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/cmake_prefix_path.ps1'
[7.978s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/cmake_prefix_path.dsv'
[7.978s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/cmake_prefix_path.sh'
[7.979s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib'
[7.979s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_planners_chomp', 'ld_library_path_lib')
[7.979s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/ld_library_path_lib.ps1'
[7.980s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/ld_library_path_lib.dsv'
[7.980s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/hook/ld_library_path_lib.sh'
[7.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/bin'
[7.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/pkgconfig/moveit_planners_chomp.pc'
[7.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/python3.10/site-packages'
[7.980s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/bin'
[7.981s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.ps1'
[7.981s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.dsv'
[7.982s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.sh'
[7.982s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.bash'
[7.982s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.zsh'
[7.982s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/colcon-core/packages/moveit_planners_chomp)
[8.166s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_control_interface
[8.166s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_control_interface -- -j8 -l8
[8.319s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_control_interface -- -j8 -l8
[8.319s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_control_interface
[8.332s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_control_interface)
[8.332s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface' for CMake module files
[8.332s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface' for CMake config files
[8.332s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_control_interface', 'cmake_prefix_path')
[8.332s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/cmake_prefix_path.ps1'
[8.333s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_control_interface
[8.333s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/cmake_prefix_path.dsv'
[8.333s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/cmake_prefix_path.sh'
[8.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib'
[8.334s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_control_interface', 'ld_library_path_lib')
[8.334s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/ld_library_path_lib.ps1'
[8.335s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/ld_library_path_lib.dsv'
[8.338s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/ld_library_path_lib.sh'
[8.338s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/bin'
[8.338s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/pkgconfig/moveit_ros_control_interface.pc'
[8.339s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/python3.10/site-packages'
[8.339s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/bin'
[8.339s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.ps1'
[8.339s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.dsv'
[8.340s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.sh'
[8.349s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.bash'
[8.350s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.zsh'
[8.353s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/colcon-core/packages/moveit_ros_control_interface)
[8.353s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_control_interface)
[8.353s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface' for CMake module files
[8.354s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface' for CMake config files
[8.354s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_control_interface', 'cmake_prefix_path')
[8.354s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/cmake_prefix_path.ps1'
[8.363s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/cmake_prefix_path.dsv'
[8.364s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/cmake_prefix_path.sh'
[8.365s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib'
[8.365s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_control_interface', 'ld_library_path_lib')
[8.365s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/ld_library_path_lib.ps1'
[8.365s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/ld_library_path_lib.dsv'
[8.366s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/hook/ld_library_path_lib.sh'
[8.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/bin'
[8.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/pkgconfig/moveit_ros_control_interface.pc'
[8.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/python3.10/site-packages'
[8.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/bin'
[8.366s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.ps1'
[8.367s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.dsv'
[8.367s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.sh'
[8.377s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.bash'
[8.379s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.zsh'
[8.381s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/colcon-core/packages/moveit_ros_control_interface)
[8.492s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_planning
[8.493s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_planning -- -j8 -l8
[8.824s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_planning -- -j8 -l8
[8.825s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_planning
[8.840s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_planning)
[8.841s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning' for CMake module files
[8.841s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning' for CMake config files
[8.841s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_planning', 'cmake_prefix_path')
[8.841s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/cmake_prefix_path.ps1'
[8.841s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/cmake_prefix_path.dsv'
[8.842s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_planning' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_planning
[8.842s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/cmake_prefix_path.sh'
[8.842s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib'
[8.842s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_planning', 'ld_library_path_lib')
[8.842s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/ld_library_path_lib.ps1'
[8.843s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/ld_library_path_lib.dsv'
[8.843s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/ld_library_path_lib.sh'
[8.843s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/bin'
[8.843s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/pkgconfig/moveit_ros_planning.pc'
[8.843s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/python3.10/site-packages'
[8.843s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/bin'
[8.844s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.ps1'
[8.844s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.dsv'
[8.844s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.sh'
[8.844s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.bash'
[8.845s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.zsh'
[8.845s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/colcon-core/packages/moveit_ros_planning)
[8.845s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_planning)
[8.846s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning' for CMake module files
[8.846s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning' for CMake config files
[8.846s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_planning', 'cmake_prefix_path')
[8.846s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/cmake_prefix_path.ps1'
[8.846s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/cmake_prefix_path.dsv'
[8.846s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/cmake_prefix_path.sh'
[8.847s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib'
[8.847s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_ros_planning', 'ld_library_path_lib')
[8.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/ld_library_path_lib.ps1'
[8.847s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/ld_library_path_lib.dsv'
[8.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/hook/ld_library_path_lib.sh'
[8.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/bin'
[8.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/pkgconfig/moveit_ros_planning.pc'
[8.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/python3.10/site-packages'
[8.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_planning/bin'
[8.848s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.ps1'
[8.849s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.dsv'
[8.849s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.sh'
[8.849s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.bash'
[8.849s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.zsh'
[8.849s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/colcon-core/packages/moveit_ros_planning)
[8.850s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics' with build type 'ament_cmake'
[8.850s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics'
[8.850s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[8.850s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[8.852s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse' with build type 'ament_cmake'
[8.852s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse'
[8.853s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[8.853s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[8.857s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl' with build type 'ament_cmake'
[8.857s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl'
[8.858s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[8.858s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[8.862s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction' with build type 'ament_cmake'
[8.863s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction'
[8.863s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[8.863s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[8.866s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception' with build type 'ament_cmake'
[8.866s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception'
[8.866s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[8.866s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[8.869s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_visual_tools' with build type 'ament_cmake'
[8.870s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_visual_tools'
[8.870s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[8.870s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[8.876s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_kinematics': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_kinematics
[8.879s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse
[8.889s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_ompl': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_planners_ompl
[8.896s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_robot_interaction': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction
[8.906s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_perception': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_perception
[8.913s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_visual_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_visual_tools -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_visual_tools
[8.929s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[8.929s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs
[9.508s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_perception' returned '1': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_perception
[9.525s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_visual_tools' returned '1': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_visual_tools -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_visual_tools
[9.526s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_ros_perception)
[9.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_perception' for CMake module files
[9.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_perception' for CMake config files
[9.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_perception/bin'
[9.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib/pkgconfig/moveit_ros_perception.pc'
[9.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib/python3.10/site-packages'
[9.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_ros_perception/bin'
[9.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_perception/share/moveit_ros_perception/package.ps1'
[9.540s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_ros_perception/share/moveit_ros_perception/package.dsv'
[9.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_perception/share/moveit_ros_perception/package.sh'
[9.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_perception/share/moveit_ros_perception/package.bash'
[9.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_ros_perception/share/moveit_ros_perception/package.zsh'
[9.541s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_ros_perception/share/colcon-core/packages/moveit_ros_perception)
[20.438s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[20.438s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[20.438s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '1'
[20.438s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[20.449s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[20.449s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[20.449s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[20.475s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[20.475s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.ps1'
[20.476s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_moveit2/install/_local_setup_util_ps1.py'
[20.477s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.ps1'
[20.478s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.sh'
[20.479s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_moveit2/install/_local_setup_util_sh.py'
[20.479s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.sh'
[20.480s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.bash'
[20.480s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.bash'
[20.481s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.zsh'
[20.481s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.zsh'
