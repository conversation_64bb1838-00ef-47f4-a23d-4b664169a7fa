-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Configuring done (0.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_resources
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/ament_index/resource_index/package_run_dependencies/moveit_resources
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/ament_index/resource_index/parent_prefix_path/moveit_resources
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.bash
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.sh
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/ament_index/resource_index/packages/moveit_resources
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/cmake/moveit_resourcesConfig.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/cmake/moveit_resourcesConfig-version.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.xml
