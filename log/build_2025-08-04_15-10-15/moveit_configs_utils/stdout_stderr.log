running egg_info
writing ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/PKG-INFO
writing dependency_links to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/dependency_links.txt
writing entry points to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/entry_points.txt
writing requirements to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/requires.txt
writing top-level names to ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/top_level.txt
reading manifest file '../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/SOURCES.txt'
writing manifest file '../../../build/moveit_configs_utils/moveit_configs_utils.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages/moveit_configs_utils-2.5.9-py3.10.egg-info' (and everything under it)
Copying ../../../build/moveit_configs_utils/moveit_configs_utils.egg-info to /home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages/moveit_configs_utils-2.5.9-py3.10.egg-info
running install_scripts
writing list of installed files to '/home/<USER>/ws_moveit2/build/moveit_configs_utils/install.log'
