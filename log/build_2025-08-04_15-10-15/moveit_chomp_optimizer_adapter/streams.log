[0.059s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter
[0.096s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.096s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.474s] -- Found chomp_motion_planner: 2.5.9 (/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake)
[0.475s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.475s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.475s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.475s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.475s]   set the policy and suppress this warning.
[0.475s] 
[0.475s] Call Stack (most recent call first):
[0.475s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.475s]   CMakeLists.txt:11 (find_package)
[0.475s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.475s] [0m
[0.507s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.549s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.589s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.590s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.595s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.607s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.618s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.677s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.677s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.094s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.236s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.273s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.273s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.273s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.273s]   set the policy and suppress this warning.
[1.273s] 
[1.273s] Call Stack (most recent call first):
[1.273s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.273s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.273s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.274s]   CMakeLists.txt:11 (find_package)
[1.274s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.274s] [0m
[1.280s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.420s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.513s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.709s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.728s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.728s] -- Configured cppcheck include dirs: 
[1.728s] -- Configured cppcheck exclude dirs and/or files: 
[1.728s] -- Added test 'xmllint' to check XML markup files
[1.729s] -- Configuring done (1.7s)
[1.781s] -- Generating done (0.1s)
[1.786s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter
[1.800s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter
[1.800s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter -- -j8 -l8
[1.879s] [100%] Built target moveit_chomp_optimizer_adapter
[1.882s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter -- -j8 -l8
[1.887s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter
[1.891s] -- Install configuration: "Release"
[1.891s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib/libmoveit_chomp_optimizer_adapter.so.2.5.9
[1.892s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib/libmoveit_chomp_optimizer_adapter.so
[1.892s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/environment/library_path.sh
[1.892s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/environment/library_path.dsv
[1.893s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/chomp_optimizer_adapter_plugin_description.xml
[1.894s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/ament_index/resource_index/package_run_dependencies/moveit_chomp_optimizer_adapter
[1.894s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/ament_index/resource_index/parent_prefix_path/moveit_chomp_optimizer_adapter
[1.894s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/environment/ament_prefix_path.sh
[1.894s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/environment/ament_prefix_path.dsv
[1.898s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/environment/path.sh
[1.900s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/environment/path.dsv
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/local_setup.bash
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/local_setup.sh
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/local_setup.zsh
[1.900s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/local_setup.dsv
[1.900s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.dsv
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/ament_index/resource_index/packages/moveit_chomp_optimizer_adapter
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_chomp_optimizer_adapter
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/cmake/moveit_chomp_optimizer_adapterTargetsExport.cmake
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/cmake/moveit_chomp_optimizer_adapterTargetsExport-release.cmake
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/cmake/ament_cmake_export_targets-extras.cmake
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/cmake/moveit_chomp_optimizer_adapterConfig.cmake
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/cmake/moveit_chomp_optimizer_adapterConfig-version.cmake
[1.900s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/share/moveit_chomp_optimizer_adapter/package.xml
[1.907s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter
