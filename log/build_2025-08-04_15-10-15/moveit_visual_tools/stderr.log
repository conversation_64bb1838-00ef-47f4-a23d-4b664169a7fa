[0mCMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
[33mCMake Warning (dev) at CMakeLists.txt:8 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[31mCMake Error at CMakeLists.txt:11 (find_package):
  By not providing "Findgraph_msgs.cmake" in CMAKE_MODULE_PATH this project
  has asked CMake to find a package configuration file provided by
  "graph_msgs", but CMake did not find one.

  Could not find a package configuration file provided by "graph_msgs" with
  any of the following names:

    graph_msgsConfig.cmake
    graph_msgs-config.cmake

  Add the installation prefix of "graph_msgs" to CMAKE_PREFIX_PATH or set
  "graph_msgs_DIR" to a directory containing one of the above files.  If
  "graph_msgs" provides a separate development package or SDK, be sure it has
  been installed.

[0m
