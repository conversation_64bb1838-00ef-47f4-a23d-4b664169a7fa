[0.015s] Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rviz_marker_tools
[0.045s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.238s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.275s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.276s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.296s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.296s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.301s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.307s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.324s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.355s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.383s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.384s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.521s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.561s] -- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
[0.636s] -- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[0.673s] -- Configuring done (0.7s)
[0.688s] -- Generating done (0.0s)
[0.690s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/rviz_marker_tools
[0.709s] Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rviz_marker_tools
[0.710s] Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[0.743s] [100%] Built target rviz_marker_tools
[0.847s] Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[0.847s] Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[0.849s] -- Install configuration: "Release"
[0.850s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/include
[0.850s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/include/rviz_marker_tools
[0.851s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/include/rviz_marker_tools/marker_creation.h
[0.851s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/librviz_marker_tools.so
[0.851s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/library_path.sh
[0.851s] -- Installing: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/library_path.dsv
[0.851s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/ament_index/resource_index/package_run_dependencies/rviz_marker_tools
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/ament_index/resource_index/parent_prefix_path/rviz_marker_tools
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/ament_prefix_path.sh
[0.852s] -- Installing: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/ament_prefix_path.dsv
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/path.sh
[0.852s] -- Installing: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/path.dsv
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.bash
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.sh
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.zsh
[0.852s] -- Installing: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.dsv
[0.852s] -- Installing: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/ament_index/resource_index/packages/rviz_marker_tools
[0.852s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsTargetsExport.cmake
[0.854s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsTargetsExport-release.cmake
[0.855s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/ament_cmake_export_targets-extras.cmake
[0.855s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/ament_cmake_export_dependencies-extras.cmake
[0.855s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsConfig.cmake
[0.856s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsConfig-version.cmake
[0.856s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.xml
[0.856s] Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
