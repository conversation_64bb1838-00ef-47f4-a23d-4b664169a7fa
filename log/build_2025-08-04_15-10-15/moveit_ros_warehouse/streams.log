[0.027s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_warehouse
[0.038s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.038s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.249s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.270s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.273s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.277s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.285s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.296s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.329s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.330s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.568s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.625s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.625s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.625s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.625s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.626s]   set the policy and suppress this warning.
[0.626s] 
[0.626s] Call Stack (most recent call first):
[0.626s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.626s]   CMakeLists.txt:10 (find_package)
[0.626s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.626s] [0m
[0.637s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.698s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.698s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[0.699s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[0.699s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.699s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.699s]   set the policy and suppress this warning.
[0.699s] 
[0.699s] Call Stack (most recent call first):
[0.699s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[0.699s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.699s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.699s]   CMakeLists.txt:10 (find_package)
[0.700s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.700s] [0m
[0.700s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[0.745s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[0.793s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.955s] -- Found warehouse_ros: 2.0.5 (/opt/ros/humble/share/warehouse_ros/cmake)
[0.959s] -- Found moveit_ros_planning: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake)
[0.959s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[0.963s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.964s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.964s]   set the policy and suppress this warning.
[0.964s] 
[0.964s] Call Stack (most recent call first):
[0.964s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[0.964s]   CMakeLists.txt:12 (find_package)
[0.964s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.964s] [0m
[0.966s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[0.984s] [33mCMake Warning (dev) at ConfigExtras.cmake:3 (find_package):
[0.984s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.985s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.985s]   set the policy and suppress this warning.
[0.985s] 
[0.987s] Call Stack (most recent call first):
[0.988s]   CMakeLists.txt:17 (include)
[0.988s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.988s] [0m
[0.989s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[1.023s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.029s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.030s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse/include
[1.030s] -- Configured cppcheck exclude dirs and/or files: 
[1.030s] -- Added test 'lint_cmake' to check CMake code style
[1.030s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.031s] -- Added test 'xmllint' to check XML markup files
[1.031s] -- Configuring done (1.0s)
[1.123s] -- Generating done (0.1s)
[1.141s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_ros_warehouse
