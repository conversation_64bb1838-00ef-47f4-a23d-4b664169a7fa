-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (1.8s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/chomp_motion_planner
[100%] Built target chomp_motion_planner
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/libchomp_motion_planner.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/libchomp_motion_planner.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_cost.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_optimizer.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_parameters.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_planner.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_trajectory.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_utils.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/multivariate_gaussian.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/ament_index/resource_index/package_run_dependencies/chomp_motion_planner
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/ament_index/resource_index/parent_prefix_path/chomp_motion_planner
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.bash
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.sh
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/ament_index/resource_index/packages/chomp_motion_planner
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerTargetsExport.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerTargetsExport-release.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerConfig.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerConfig-version.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.xml
