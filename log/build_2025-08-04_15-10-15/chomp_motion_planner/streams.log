[0.067s] Invoking command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/chomp_motion_planner
[0.093s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.093s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.450s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.451s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.451s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.452s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.456s]   set the policy and suppress this warning.
[0.456s] 
[0.456s] Call Stack (most recent call first):
[0.456s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.456s]   CMakeLists.txt:10 (find_package)
[0.456s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.456s] [0m
[0.482s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.542s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.578s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.586s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.591s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.613s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.632s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.704s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.704s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.045s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.159s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.199s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.199s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.199s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.199s]   set the policy and suppress this warning.
[1.199s] 
[1.199s] Call Stack (most recent call first):
[1.199s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.199s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.199s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.199s]   CMakeLists.txt:10 (find_package)
[1.199s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.199s] [0m
[1.206s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.270s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.394s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.828s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.871s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.871s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner/include
[1.871s] -- Configured cppcheck exclude dirs and/or files: 
[1.871s] -- Added test 'xmllint' to check XML markup files
[1.871s] -- Configuring done (1.8s)
[1.922s] -- Generating done (0.1s)
[1.935s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/chomp_motion_planner
[1.963s] Invoked command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/chomp_motion_planner
[1.964s] Invoking command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/chomp_motion_planner -- -j8 -l8
[2.066s] [100%] Built target chomp_motion_planner
[2.079s] Invoked command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/chomp_motion_planner -- -j8 -l8
[2.082s] Invoking command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/chomp_motion_planner
[2.090s] -- Install configuration: "Release"
[2.090s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/libchomp_motion_planner.so.2.5.9
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/lib/libchomp_motion_planner.so
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_cost.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_optimizer.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_parameters.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_planner.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_trajectory.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/chomp_utils.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/include/chomp_motion_planner/multivariate_gaussian.h
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/library_path.sh
[2.091s] -- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/library_path.dsv
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/ament_index/resource_index/package_run_dependencies/chomp_motion_planner
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/ament_index/resource_index/parent_prefix_path/chomp_motion_planner
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/ament_prefix_path.sh
[2.091s] -- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/ament_prefix_path.dsv
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/path.sh
[2.091s] -- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/environment/path.dsv
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.bash
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.sh
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.zsh
[2.091s] -- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/local_setup.dsv
[2.091s] -- Installing: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.dsv
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/ament_index/resource_index/packages/chomp_motion_planner
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerTargetsExport.cmake
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerTargetsExport-release.cmake
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/ament_cmake_export_targets-extras.cmake
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerConfig.cmake
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake/chomp_motion_plannerConfig-version.cmake
[2.091s] -- Up-to-date: /home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.xml
[2.113s] Invoked command in '/home/<USER>/ws_moveit2/build/chomp_motion_planner' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/chomp_motion_planner
