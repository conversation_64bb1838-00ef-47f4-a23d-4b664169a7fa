[0.032s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_robot_interaction': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction
[0.050s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.050s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.262s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.262s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.262s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.262s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.262s]   set the policy and suppress this warning.
[0.262s] 
[0.262s] Call Stack (most recent call first):
[0.262s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.262s]   CMakeLists.txt:8 (find_package)
[0.262s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.262s] [0m
[0.277s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.318s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.339s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.342s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.347s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.367s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.381s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.429s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.430s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.630s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.687s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[0.690s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[0.692s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.692s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.692s]   set the policy and suppress this warning.
[0.692s] 
[0.692s] Call Stack (most recent call first):
[0.692s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[0.692s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.692s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.692s]   CMakeLists.txt:8 (find_package)
[0.692s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.692s] [0m
[0.693s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[0.740s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[0.789s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.959s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[0.959s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.959s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.959s]   set the policy and suppress this warning.
[0.959s] 
[0.959s] Call Stack (most recent call first):
[0.960s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[0.960s]   CMakeLists.txt:9 (find_package)
[0.961s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.962s] [0m
[0.962s] -- Found moveit_ros_planning: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake)
[0.962s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[0.982s] -- Found interactive_markers: 2.3.2 (/opt/ros/humble/share/interactive_markers/cmake)
[0.995s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.996s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.004s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.004s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.004s]   CMake.
[1.004s] 
[1.005s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.005s]   to tell CMake that the project requires at least <min> but has been updated
[1.005s]   to work with policies introduced by <max> or earlier.
[1.005s] 
[1.005s] [0m
[1.005s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.005s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.005s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction/include
[1.005s] -- Configured cppcheck exclude dirs and/or files: 
[1.005s] -- Added test 'lint_cmake' to check CMake code style
[1.005s] -- Added test 'xmllint' to check XML markup files
[1.005s] -- Configuring done (1.0s)
[1.043s] -- Generating done (0.0s)
[1.049s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_ros_robot_interaction
