[0.037s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_planners_chomp
[0.060s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.060s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.394s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.394s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.394s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.395s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.395s]   set the policy and suppress this warning.
[0.395s] 
[0.395s] Call Stack (most recent call first):
[0.395s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.395s]   CMakeLists.txt:10 (find_package)
[0.395s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.395s] [0m
[0.431s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.537s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.659s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.664s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.673s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.703s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.724s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.802s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.810s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.121s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.208s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.226s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.226s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.226s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.226s]   set the policy and suppress this warning.
[1.226s] 
[1.226s] Call Stack (most recent call first):
[1.226s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.226s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.226s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.226s]   CMakeLists.txt:10 (find_package)
[1.226s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.226s] [0m
[1.227s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.357s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.500s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.763s] -- Found chomp_motion_planner: 2.5.9 (/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake)
[1.771s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.778s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.778s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface/include
[1.778s] -- Configured cppcheck exclude dirs and/or files: 
[1.779s] -- Added test 'xmllint' to check XML markup files
[1.784s] -- Configuring done (1.7s)
[1.821s] -- Generating done (0.0s)
[1.821s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_planners_chomp
[1.839s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_planners_chomp
[1.839s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_planners_chomp -- -j8 -l8
[1.896s] [ 60%] Built target moveit_chomp_interface
[1.923s] [100%] Built target moveit_chomp_planner_plugin
[1.928s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_planners_chomp -- -j8 -l8
[1.929s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_planners_chomp
[1.938s] -- Install configuration: "Release"
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_interface.so.2.5.9
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_interface.so
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_planner_plugin.so
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/library_path.sh
[1.938s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/library_path.dsv
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/chomp_interface_plugin_description.xml
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/package_run_dependencies/moveit_planners_chomp
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/parent_prefix_path/moveit_planners_chomp
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/ament_prefix_path.sh
[1.938s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/ament_prefix_path.dsv
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/path.sh
[1.938s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/path.dsv
[1.938s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.bash
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.sh
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.zsh
[1.939s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.dsv
[1.939s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.dsv
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/packages/moveit_planners_chomp
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_planners_chomp
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompTargetsExport.cmake
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompTargetsExport-release.cmake
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/ament_cmake_export_targets-extras.cmake
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/ament_cmake_export_dependencies-extras.cmake
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompConfig.cmake
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompConfig-version.cmake
[1.939s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.xml
[1.939s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_planners_chomp' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_planners_chomp
