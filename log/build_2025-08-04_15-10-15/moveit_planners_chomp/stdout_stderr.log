-- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  CMakeLists.txt:10 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  CMakeLists.txt:10 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found chomp_motion_planner: 2.5.9 (/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (1.7s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_planners_chomp
[ 60%] Built target moveit_chomp_interface
[100%] Built target moveit_chomp_planner_plugin
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_interface.so.2.5.9
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_interface.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib/libmoveit_chomp_planner_plugin.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/chomp_interface_plugin_description.xml
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/package_run_dependencies/moveit_planners_chomp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/parent_prefix_path/moveit_planners_chomp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/environment/path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.bash
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.sh
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/packages/moveit_planners_chomp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_planners_chomp
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompTargetsExport.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompTargetsExport-release.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompConfig.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/cmake/moveit_planners_chompConfig-version.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.xml
