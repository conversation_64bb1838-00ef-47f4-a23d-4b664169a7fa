-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found urdf: 2.6.1 (/opt/ros/humble/share/urdf/cmake)
[33mCMake Warning (dev) at CMakeLists.txt:8 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found ament_cmake_pytest: 1.3.12 (/opt/ros/humble/share/ament_cmake_pytest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.

[0m
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Configuring done (0.6s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/srdfdom
[ 18%] Built target gtest_main
[ 45%] Built target srdfdom
[ 45%] Built target ament_cmake_python_copy_srdfdom
[ 63%] Built target gtest
[ 81%] Built target test_cpp_C
[100%] Built target test_cpp_nl_NL.UTF-8
running egg_info
writing srdfdom.egg-info/PKG-INFO
writing dependency_links to srdfdom.egg-info/dependency_links.txt
writing top-level names to srdfdom.egg-info/top_level.txt
reading manifest file 'srdfdom.egg-info/SOURCES.txt'
writing manifest file 'srdfdom.egg-info/SOURCES.txt'
[100%] Built target ament_cmake_python_build_srdfdom_egg
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so.2.0.8
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/model.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/srdf_writer.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/visibility_control.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/srdfdom/display_srdf
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/pythonpath.sh
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/pythonpath.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/PKG-INFO
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/dependency_links.txt
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/top_level.txt
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/SOURCES.txt
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom/__init__.py
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom/srdf.py
Listing '/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom'...
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/package_run_dependencies/srdfdom
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/parent_prefix_path/srdfdom
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.bash
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.sh
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/packages/srdfdom
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport-release.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig-version.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.xml
