[0.046s] Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/srdfdom -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/srdfdom
[0.191s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.191s] -- Found urdf: 2.6.1 (/opt/ros/humble/share/urdf/cmake)
[0.191s] [33mCMake Warning (dev) at CMakeLists.txt:8 (find_package):
[0.191s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.191s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.191s]   set the policy and suppress this warning.
[0.191s] 
[0.191s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.191s] [0m
[0.313s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.342s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.343s] -- Found ament_cmake_pytest: 1.3.12 (/opt/ros/humble/share/ament_cmake_pytest/cmake)
[0.675s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.678s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[0.678s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.678s]   CMake.
[0.678s] 
[0.678s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.678s]   to tell CMake that the project requires at least <min> but has been updated
[0.678s]   to work with policies introduced by <max> or earlier.
[0.679s] 
[0.679s] [0m
[0.682s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.702s] -- Configuring done (0.6s)
[0.718s] -- Generating done (0.0s)
[0.723s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/srdfdom
[0.736s] Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/srdfdom -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/srdfdom
[0.736s] Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.765s] [ 18%] Built target gtest_main
[0.767s] [ 45%] Built target srdfdom
[0.768s] [ 45%] Built target ament_cmake_python_copy_srdfdom
[0.770s] [ 63%] Built target gtest
[0.787s] [ 81%] Built target test_cpp_C
[0.787s] [100%] Built target test_cpp_nl_NL.UTF-8
[0.898s] running egg_info
[0.898s] writing srdfdom.egg-info/PKG-INFO
[0.898s] writing dependency_links to srdfdom.egg-info/dependency_links.txt
[0.899s] writing top-level names to srdfdom.egg-info/top_level.txt
[0.902s] reading manifest file 'srdfdom.egg-info/SOURCES.txt'
[0.902s] writing manifest file 'srdfdom.egg-info/SOURCES.txt'
[0.915s] [100%] Built target ament_cmake_python_build_srdfdom_egg
[0.927s] Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.927s] Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[0.928s] -- Install configuration: "Release"
[0.928s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so.2.0.8
[0.928s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so
[0.929s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom
[0.929s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom
[0.929s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/model.h
[0.930s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/srdf_writer.h
[0.930s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/visibility_control.h
[0.930s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/srdfdom/display_srdf
[0.930s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/pythonpath.sh
[0.930s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/pythonpath.dsv
[0.931s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info
[0.931s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/PKG-INFO
[0.931s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/dependency_links.txt
[0.931s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/top_level.txt
[0.931s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/SOURCES.txt
[0.931s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom
[0.931s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom/__init__.py
[0.931s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom/srdf.py
[0.947s] Listing '/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom'...
[0.950s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/library_path.sh
[0.950s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/library_path.dsv
[0.950s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/package_run_dependencies/srdfdom
[0.953s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/parent_prefix_path/srdfdom
[0.953s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/ament_prefix_path.sh
[0.953s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/ament_prefix_path.dsv
[0.953s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/path.sh
[0.953s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/path.dsv
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.bash
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.sh
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.zsh
[0.954s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.dsv
[0.954s] -- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/packages/srdfdom
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport.cmake
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport-release.cmake
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_targets-extras.cmake
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_dependencies-extras.cmake
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig.cmake
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig-version.cmake
[0.954s] -- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.xml
[0.955s] Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
