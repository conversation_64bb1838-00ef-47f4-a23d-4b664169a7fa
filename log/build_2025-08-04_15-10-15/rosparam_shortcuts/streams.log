[0.012s] Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/rosparam_shortcuts -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rosparam_shortcuts
[0.047s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.187s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.192s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.218s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.220s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.225s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.231s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.241s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.274s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.305s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.311s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.445s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.488s] -- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
[0.568s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.568s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.569s] -- Found ros_testing: 0.4.0 (/opt/ros/humble/share/ros_testing/cmake)
[0.569s] -- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
[0.570s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[0.570s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[0.570s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[0.570s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[0.570s]   the cmake_policy command to set the policy and suppress this warning.
[0.571s] 
[0.571s] Call Stack (most recent call first):
[0.571s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[0.571s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[0.571s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[0.571s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[0.571s]   CMakeLists.txt:97 (find_package)
[0.571s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.571s] [0m
[0.590s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[0.590s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[0.591s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[0.591s]   the cmake_policy command to set the policy and suppress this warning.
[0.591s] 
[0.591s] Call Stack (most recent call first):
[0.591s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[0.591s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[0.591s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[0.591s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[0.591s]   CMakeLists.txt:97 (find_package)
[0.591s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.591s] [0m
[0.595s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[0.595s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[0.595s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[0.630s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.630s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[0.630s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.630s]   CMake.
[0.630s] 
[0.630s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.631s]   to tell CMake that the project requires at least <min> but has been updated
[0.631s]   to work with policies introduced by <max> or earlier.
[0.631s] 
[0.631s] [0m
[0.634s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.635s] -- Added test 'lint_cmake' to check CMake code style
[0.635s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.636s] -- Added test 'xmllint' to check XML markup files
[0.637s] -- Configuring done (0.6s)
[0.673s] -- Generating done (0.0s)
[0.684s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[0.775s] Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/rosparam_shortcuts -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rosparam_shortcuts
[0.776s] Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[0.800s] [ 15%] Built target gtest_main
[0.801s] [ 30%] Built target gtest
[0.804s] [ 53%] Built target rosparam_shortcuts
[0.819s] [ 69%] Built target rosparam_shortcuts_example
[0.825s] [ 84%] Built target test_node_parameters
[0.826s] [100%] Built target rosparam_shortcuts_node_parameters_example
[0.831s] Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[0.831s] Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[0.833s] -- Install configuration: "Release"
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/librosparam_shortcuts.so
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/rosparam_shortcuts/example
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/rosparam_shortcuts/node_parameters_example
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts/node_parameters.h
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts/rosparam_shortcuts.h
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/launch
[0.833s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/launch/example.launch.py
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/config
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/config/example.yaml
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/library_path.sh
[0.834s] -- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/library_path.dsv
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/package_run_dependencies/rosparam_shortcuts
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/parent_prefix_path/rosparam_shortcuts
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/ament_prefix_path.sh
[0.834s] -- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/ament_prefix_path.dsv
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/path.sh
[0.834s] -- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/path.dsv
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.bash
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.sh
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.zsh
[0.834s] -- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.dsv
[0.834s] -- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.dsv
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/packages/rosparam_shortcuts
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/export_rosparam_shortcutsExport.cmake
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/export_rosparam_shortcutsExport-release.cmake
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/ament_cmake_export_targets-extras.cmake
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/ament_cmake_export_dependencies-extras.cmake
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/rosparam_shortcutsConfig.cmake
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/rosparam_shortcutsConfig-version.cmake
[0.834s] -- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.xml
[0.836s] Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rosparam_shortcuts
