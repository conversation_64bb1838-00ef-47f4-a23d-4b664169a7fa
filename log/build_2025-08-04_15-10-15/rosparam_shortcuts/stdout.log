-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found ros_testing: 0.4.0 (/opt/ros/humble/share/ros_testing/cmake)
-- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.6s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[ 15%] Built target gtest_main
[ 30%] Built target gtest
[ 53%] Built target rosparam_shortcuts
[ 69%] Built target rosparam_shortcuts_example
[ 84%] Built target test_node_parameters
[100%] Built target rosparam_shortcuts_node_parameters_example
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/librosparam_shortcuts.so
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/rosparam_shortcuts/example
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/rosparam_shortcuts/node_parameters_example
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts/node_parameters.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts/rosparam_shortcuts.h
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/launch
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/launch/example.launch.py
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/config
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/config/example.yaml
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/library_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/library_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/package_run_dependencies/rosparam_shortcuts
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/parent_prefix_path/rosparam_shortcuts
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/path.sh
-- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/path.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.bash
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.sh
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.zsh
-- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.dsv
-- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.dsv
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/packages/rosparam_shortcuts
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/export_rosparam_shortcutsExport.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/export_rosparam_shortcutsExport-release.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/rosparam_shortcutsConfig.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/rosparam_shortcutsConfig-version.cmake
-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.xml
