[0.069s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_control_interface
[0.095s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.471s] -- Found rclcpp_action: 16.0.14 (/opt/ros/humble/share/rclcpp_action/cmake)
[0.515s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.517s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.541s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.558s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.580s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.761s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.773s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.092s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.228s] -- Found controller_manager_msgs: 2.51.0 (/opt/ros/humble/share/controller_manager_msgs/cmake)
[1.307s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[1.308s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.308s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.308s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.308s]   set the policy and suppress this warning.
[1.308s] 
[1.308s] Call Stack (most recent call first):
[1.308s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.308s]   CMakeLists.txt:16 (find_package)
[1.308s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.308s] [0m
[1.332s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.375s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.377s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.384s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.384s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.384s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.384s]   set the policy and suppress this warning.
[1.384s] 
[1.384s] Call Stack (most recent call first):
[1.384s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.384s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.384s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.384s]   CMakeLists.txt:16 (find_package)
[1.384s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.384s] [0m
[1.387s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.442s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.489s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.696s] -- Found moveit_simple_controller_manager: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake)
[1.696s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/ConfigExtras.cmake:3 (find_package):
[1.696s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.696s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.696s]   set the policy and suppress this warning.
[1.696s] 
[1.696s] Call Stack (most recent call first):
[1.696s]   /home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/cmake/moveit_simple_controller_managerConfig.cmake:41 (include)
[1.696s]   CMakeLists.txt:16 (find_package)
[1.696s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.696s] [0m
[1.704s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread
[1.738s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[1.794s] [33mCMake Warning (dev) at ConfigExtras.cmake:3 (find_package):
[1.794s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.794s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.794s]   set the policy and suppress this warning.
[1.794s] 
[1.794s] Call Stack (most recent call first):
[1.794s]   CMakeLists.txt:24 (include)
[1.794s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.794s] [0m
[1.798s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system thread
[1.820s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.821s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.822s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.822s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.822s]   CMake.
[1.822s] 
[1.822s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.822s]   to tell CMake that the project requires at least <min> but has been updated
[1.822s]   to work with policies introduced by <max> or earlier.
[1.822s] 
[1.822s] [0m
[1.835s] -- Configuring done (1.8s)
[1.930s] -- Generating done (0.1s)
[1.959s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_ros_control_interface
[1.975s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_ros_control_interface
[1.976s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_control_interface -- -j8 -l8
[2.052s] [ 14%] Built target gtest
[2.060s] [ 28%] Built target moveit_ros_control_interface_gripper_plugin
[2.070s] [ 42%] Built target moveit_ros_control_interface_empty_plugin
[2.071s] [ 57%] Built target gtest_main
[2.075s] [ 71%] Built target moveit_ros_control_interface_trajectory_plugin
[2.080s] [ 85%] Built target moveit_ros_control_interface_plugin
[2.126s] [100%] Built target test_controller_manager_plugin
[2.128s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_ros_control_interface -- -j8 -l8
[2.129s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_control_interface
[2.135s] -- Install configuration: "Release"
[2.135s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_plugin.so.2.5.9
[2.135s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_plugin.so
[2.135s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_trajectory_plugin.so.2.5.9
[2.135s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_trajectory_plugin.so
[2.135s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_gripper_plugin.so.2.5.9
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_gripper_plugin.so
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_empty_plugin.so.2.5.9
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib/libmoveit_ros_control_interface_empty_plugin.so
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/include
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/include/moveit_ros_control_interface
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/include/moveit_ros_control_interface/ControllerHandle.h
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/moveit_core_plugins.xml
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/moveit_ros_control_interface_plugins.xml
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/environment/library_path.sh
[2.136s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/environment/library_path.dsv
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/ament_index/resource_index/package_run_dependencies/moveit_ros_control_interface
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/ament_index/resource_index/parent_prefix_path/moveit_ros_control_interface
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/environment/ament_prefix_path.sh
[2.136s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/environment/ament_prefix_path.dsv
[2.136s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/environment/path.sh
[2.137s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/environment/path.dsv
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/local_setup.bash
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/local_setup.sh
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/local_setup.zsh
[2.137s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/local_setup.dsv
[2.137s] -- Installing: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.dsv
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/ament_index/resource_index/packages/moveit_ros_control_interface
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/ament_index/resource_index/moveit_core__pluginlib__plugin/moveit_ros_control_interface
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/ament_index/resource_index/moveit_ros_control_interface__pluginlib__plugin/moveit_ros_control_interface
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/export_moveit_ros_control_interfaceExport.cmake
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/export_moveit_ros_control_interfaceExport-release.cmake
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/ConfigExtras.cmake
[2.137s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/ament_cmake_export_targets-extras.cmake
[2.141s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/ament_cmake_export_dependencies-extras.cmake
[2.141s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/moveit_ros_control_interfaceConfig.cmake
[2.141s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/cmake/moveit_ros_control_interfaceConfig-version.cmake
[2.141s] -- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_ros_control_interface/share/moveit_ros_control_interface/package.xml
[2.142s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_ros_control_interface
