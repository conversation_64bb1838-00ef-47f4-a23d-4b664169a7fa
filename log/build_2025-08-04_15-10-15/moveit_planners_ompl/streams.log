[0.027s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_planners_ompl': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_planners_ompl
[0.028s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.028s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.206s] [33mCMake Warning (dev) at CMakeLists.txt:8 (find_package):
[0.206s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.206s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.206s]   set the policy and suppress this warning.
[0.206s] 
[0.206s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.206s] [0m
[0.215s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time thread serialization
[0.217s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.218s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.218s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.218s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.218s]   set the policy and suppress this warning.
[0.218s] 
[0.218s] Call Stack (most recent call first):
[0.218s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.218s]   CMakeLists.txt:9 (find_package)
[0.218s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.218s] [0m
[0.226s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.252s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.271s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.273s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.279s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.287s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.299s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.340s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.341s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.561s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.615s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[0.629s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[0.629s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.629s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.629s]   set the policy and suppress this warning.
[0.629s] 
[0.629s] Call Stack (most recent call first):
[0.629s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[0.629s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.629s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.629s]   CMakeLists.txt:9 (find_package)
[0.629s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.629s] [0m
[0.632s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[0.693s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[0.749s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.923s] -- Found moveit_ros_planning: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake)
[0.924s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[0.924s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.924s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.924s]   set the policy and suppress this warning.
[0.924s] 
[0.924s] Call Stack (most recent call first):
[0.924s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[0.924s]   CMakeLists.txt:11 (find_package)
[0.925s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.925s] [0m
[0.927s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[0.955s] [33mCMake Warning (dev) at /usr/local/cmake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package):
[0.955s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.955s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.955s]   set the policy and suppress this warning.
[0.955s] 
[0.955s] Call Stack (most recent call first):
[0.955s]   /opt/ros/humble/share/ompl/cmake/omplConfig.cmake:30 (find_dependency)
[0.956s]   CMakeLists.txt:16 (find_package)
[0.956s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.956s] [0m
[0.956s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: serialization filesystem system
[0.970s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.974s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.978s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[0.978s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.978s]   CMake.
[0.979s] 
[0.979s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.979s]   to tell CMake that the project requires at least <min> but has been updated
[0.979s]   to work with policies introduced by <max> or earlier.
[0.979s] 
[0.979s] [0m
[1.006s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.025s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.025s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl/ompl_interface/include
[1.025s] -- Configured cppcheck exclude dirs and/or files: 
[1.026s] -- Added test 'lint_cmake' to check CMake code style
[1.028s] -- Added test 'xmllint' to check XML markup files
[1.028s] -- Configuring done (1.0s)
[1.142s] -- Generating done (0.1s)
[1.164s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_planners_ompl
