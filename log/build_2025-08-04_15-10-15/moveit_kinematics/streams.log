[0.026s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_kinematics': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:${PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_kinematics
[0.046s] -- Found moveit_common: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake)
[0.046s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.241s] -- Found tf2_kdl: 0.25.15 (/opt/ros/humble/share/tf2_kdl/cmake)
[0.299s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.305s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.323s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.340s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.388s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.433s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.433s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.480s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.481s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.701s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.770s] -- Found kdl_parser: 2.6.4 (/opt/ros/humble/share/kdl_parser/cmake)
[0.772s] -- Found random_numbers: 2.0.1 (/opt/ros/humble/share/random_numbers/cmake)
[0.772s] [33mCMake Warning (dev) at /opt/ros/humble/share/random_numbers/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package):
[0.772s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.773s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.773s]   set the policy and suppress this warning.
[0.773s] 
[0.773s] Call Stack (most recent call first):
[0.773s]   /opt/ros/humble/share/random_numbers/cmake/random_numbersConfig.cmake:41 (include)
[0.773s]   CMakeLists.txt:14 (find_package)
[0.773s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.773s] [0m
[0.773s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[0.794s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.794s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.794s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.794s]   set the policy and suppress this warning.
[0.795s] 
[0.795s] Call Stack (most recent call first):
[0.795s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.795s]   CMakeLists.txt:17 (find_package)
[0.795s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.795s] [0m
[0.795s] -- Found moveit_core: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake)
[0.803s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[0.823s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[0.833s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[0.842s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[0.842s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.842s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.842s]   set the policy and suppress this warning.
[0.843s] 
[0.843s] Call Stack (most recent call first):
[0.843s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[0.843s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.843s]   /home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.843s]   CMakeLists.txt:17 (find_package)
[0.843s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.843s] [0m
[0.844s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[0.877s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.009s] -- Found moveit_ros_planning: 2.5.9 (/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake)
[1.009s] [33mCMake Warning (dev) at /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[1.009s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.009s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.010s]   set the policy and suppress this warning.
[1.010s] 
[1.010s] Call Stack (most recent call first):
[1.010s]   /home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[1.011s]   CMakeLists.txt:18 (find_package)
[1.011s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.011s] [0m
[1.011s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[1.025s] [33mCMake Warning (dev) at ConfigExtras.cmake:3 (find_package):
[1.025s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.025s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.025s]   set the policy and suppress this warning.
[1.025s] 
[1.025s] Call Stack (most recent call first):
[1.025s]   CMakeLists.txt:22 (include)
[1.025s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.025s] [0m
[1.028s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: program_options system
[1.083s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.083s] -- Found ros_testing: 0.4.0 (/opt/ros/humble/share/ros_testing/cmake)
[1.083s] -- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
[1.083s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.084s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[1.084s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.084s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.084s]   the cmake_policy command to set the policy and suppress this warning.
[1.084s] 
[1.084s] Call Stack (most recent call first):
[1.084s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.084s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.084s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.084s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.084s]   test/CMakeLists.txt:3 (find_package)
[1.084s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.084s] [0m
[1.103s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[1.103s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.103s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.103s]   the cmake_policy command to set the policy and suppress this warning.
[1.103s] 
[1.103s] Call Stack (most recent call first):
[1.103s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.103s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.103s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.103s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.103s]   test/CMakeLists.txt:3 (find_package)
[1.103s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.103s] [0m
[1.106s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.106s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.106s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[1.108s] -- Found moveit_resources_fanuc_description: 2.0.7 (/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/cmake)
[1.108s] -- Found moveit_resources_fanuc_moveit_config: 2.0.7 (/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/cmake)
[1.108s] -- Found moveit_resources_panda_description: 2.0.7 (/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/cmake)
[1.109s] -- Found moveit_resources_panda_moveit_config: 2.0.7 (/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/cmake)
[1.109s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.111s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.111s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.112s]   CMake.
[1.112s] 
[1.112s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.112s]   to tell CMake that the project requires at least <min> but has been updated
[1.112s]   to work with policies introduced by <max> or earlier.
[1.112s] 
[1.112s] [0m
[1.123s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.124s] -- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
[1.124s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.124s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[1.125s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.125s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.125s]   the cmake_policy command to set the policy and suppress this warning.
[1.125s] 
[1.125s] Call Stack (most recent call first):
[1.125s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.125s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.125s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.125s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.125s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:36 (find_package)
[1.125s]   CMakeLists.txt:84 (ament_lint_auto_find_test_dependencies)
[1.125s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.125s] [0m
[1.136s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[1.136s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.136s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.136s]   the cmake_policy command to set the policy and suppress this warning.
[1.136s] 
[1.136s] Call Stack (most recent call first):
[1.136s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.136s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.136s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.136s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.136s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:36 (find_package)
[1.136s]   CMakeLists.txt:84 (ament_lint_auto_find_test_dependencies)
[1.136s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.136s] [0m
[1.137s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.137s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.137s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[1.171s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.171s] -- Configured cppcheck include dirs: /home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics/kdl_kinematics_plugin/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics/lma_kinematics_plugin/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics/srv_kinematics_plugin/include;/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics/cached_ik_kinematics_plugin/include
[1.171s] -- Configured cppcheck exclude dirs and/or files: 
[1.171s] -- Added test 'lint_cmake' to check CMake code style
[1.173s] -- Configuring done (1.1s)
[1.264s] -- Generating done (0.1s)
[1.280s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_kinematics
