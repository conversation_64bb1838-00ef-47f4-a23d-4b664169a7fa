running egg_info
writing ../../build/launch_param_builder/launch_param_builder.egg-info/PKG-INFO
writing dependency_links to ../../build/launch_param_builder/launch_param_builder.egg-info/dependency_links.txt
writing entry points to ../../build/launch_param_builder/launch_param_builder.egg-info/entry_points.txt
writing requirements to ../../build/launch_param_builder/launch_param_builder.egg-info/requires.txt
writing top-level names to ../../build/launch_param_builder/launch_param_builder.egg-info/top_level.txt
reading manifest file '../../build/launch_param_builder/launch_param_builder.egg-info/SOURCES.txt'
adding license file 'LICENSE'
writing manifest file '../../build/launch_param_builder/launch_param_builder.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages/launch_param_builder-0.1.1-py3.10.egg-info' (and everything under it)
Copying ../../build/launch_param_builder/launch_param_builder.egg-info to /home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages/launch_param_builder-0.1.1-py3.10.egg-info
running install_scripts
writing list of installed files to '/home/<USER>/ws_moveit2/build/launch_param_builder/install.log'
