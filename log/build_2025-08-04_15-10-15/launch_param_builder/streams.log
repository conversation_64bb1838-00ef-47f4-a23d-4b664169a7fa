[0.559s] Invoking command in '/home/<USER>/ws_moveit2/src/launch_param_builder': PYTHONPATH=/home/<USER>/ws_moveit2/build/launch_param_builder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launch_param_builder build --build-base /home/<USER>/ws_moveit2/build/launch_param_builder/build install --record /home/<USER>/ws_moveit2/build/launch_param_builder/install.log --single-version-externally-managed install_data
[0.690s] running egg_info
[0.690s] writing ../../build/launch_param_builder/launch_param_builder.egg-info/PKG-INFO
[0.691s] writing dependency_links to ../../build/launch_param_builder/launch_param_builder.egg-info/dependency_links.txt
[0.691s] writing entry points to ../../build/launch_param_builder/launch_param_builder.egg-info/entry_points.txt
[0.691s] writing requirements to ../../build/launch_param_builder/launch_param_builder.egg-info/requires.txt
[0.691s] writing top-level names to ../../build/launch_param_builder/launch_param_builder.egg-info/top_level.txt
[0.695s] reading manifest file '../../build/launch_param_builder/launch_param_builder.egg-info/SOURCES.txt'
[0.695s] adding license file 'LICENSE'
[0.696s] writing manifest file '../../build/launch_param_builder/launch_param_builder.egg-info/SOURCES.txt'
[0.696s] running build
[0.696s] running build_py
[0.697s] running install
[0.698s] running install_lib
[0.699s] running install_data
[0.699s] running install_egg_info
[0.700s] removing '/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages/launch_param_builder-0.1.1-py3.10.egg-info' (and everything under it)
[0.700s] Copying ../../build/launch_param_builder/launch_param_builder.egg-info to /home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages/launch_param_builder-0.1.1-py3.10.egg-info
[0.701s] running install_scripts
[0.715s] writing list of installed files to '/home/<USER>/ws_moveit2/build/launch_param_builder/install.log'
[0.751s] Invoked command in '/home/<USER>/ws_moveit2/src/launch_param_builder' returned '0': PYTHONPATH=/home/<USER>/ws_moveit2/build/launch_param_builder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/launch_param_builder build --build-base /home/<USER>/ws_moveit2/build/launch_param_builder/build install --record /home/<USER>/ws_moveit2/build/launch_param_builder/install.log --single-version-externally-managed install_data
