[0.032s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_core -- -j8 -l8
[0.059s] [  1%] Built target gtest
[0.060s] [  3%] Built target gtest_main
[0.060s] [  5%] Built target moveit_utils
[0.061s] [  6%] Built target moveit_kinematics_base
[0.061s] [  7%] Built target moveit_exceptions
[0.062s] [  8%] Built target moveit_transforms
[0.062s] [ 10%] Built target gmock
[0.071s] [ 13%] Built target moveit_distance_field
[0.071s] [ 14%] Built target gmock_main
[0.073s] [ 16%] Built target moveit_butterworth_parameters
[0.076s] [ 17%] Built target moveit_smoothing_base
[0.076s] [ 18%] Built target moveit_version
[0.079s] [ 19%] Built target test_transforms
[0.085s] [ 20%] Built target test_voxel_grid
[0.088s] [ 26%] Built target moveit_robot_model
[0.088s] [ 27%] Built target test_distance_field
[0.103s] [ 29%] Built target moveit_test_utils
[0.103s] [ 30%] Built target moveit_butterworth_filter
[0.106s] [ 33%] Built target moveit_robot_state
[0.120s] [ 34%] Built target test_butterworth_filter
[0.121s] [ 35%] Built target test_robot_model
[0.121s] [ 36%] Built target moveit_robot_trajectory
[0.123s] [ 37%] Built target moveit_dynamics_solver
[0.123s] [ 38%] Built target moveit_kinematics_metrics
[0.124s] [ 39%] Built target test_robot_state_benchmark
[0.126s] [ 41%] Built target test_robot_state
[0.132s] [ 46%] Built target moveit_collision_detection
[0.134s] [ 47%] Built target test_robot_state_complex
[0.138s] [ 48%] Built target test_cartesian_interpolator
[0.139s] [ 49%] Built target test_aabb
[0.142s] [ 51%] Built target moveit_planning_interface
[0.142s] [ 52%] Built target test_robot_trajectory
[0.145s] [ 53%] Built target test_world
[0.147s] [ 56%] Built target moveit_trajectory_processing
[0.148s] [ 58%] Built target test_world_diff
[0.152s] [ 60%] Built target moveit_collision_detection_fcl
[0.160s] [ 61%] Built target test_all_valid
[0.161s] [ 62%] Built target test_time_parameterization
[0.161s] [ 64%] Built target test_time_optimal_trajectory_generation
[0.161s] [ 65%] Built target test_ruckig_traj_smoothing
[0.172s] [ 67%] Built target test_fcl_collision_detection
[0.173s] [ 66%] Built target test_fcl_collision_env
[0.173s] [ 72%] Built target moveit_collision_detection_bullet
[0.176s] [ 74%] Built target moveit_kinematic_constraints
[0.180s] [ 76%] Built target test_fcl_collision_detection_panda
[0.189s] [ 78%] Built target test_bullet_collision_detection_panda
[0.189s] [ 79%] Built target test_bullet_continuous_collision_checking
[0.191s] [ 80%] Built target test_bullet_collision_detection
[0.191s] [ 81%] Built target test_constraints
[0.194s] [ 83%] Built target moveit_planning_scene
[0.195s] [ 84%] Built target test_orientation_constraints
[0.209s] [ 85%] Built target test_collision_objects
[0.210s] [ 86%] Built target moveit_planning_request_adapter
[0.213s] [ 87%] Built target test_multi_threaded
[0.213s] [ 88%] Built target collision_detector_bullet_plugin
[0.215s] [ 91%] Built target moveit_constraint_samplers
[0.217s] [ 92%] Built target collision_detector_fcl_plugin
[0.218s] [ 93%] Built target test_planning_scene
[0.220s] [ 96%] Built target moveit_collision_distance_field
[0.234s] [ 99%] Built target test_constraint_samplers
[0.235s] [100%] Built target test_collision_distance_field
