[0.037s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_core -- -j8 -l8
[0.111s] [  1%] Built target moveit_exceptions
[0.114s] [  3%] Built target gmock
[0.124s] [  4%] Built target moveit_kinematics_base
[0.124s] [  5%] Built target gtest_main
[0.126s] [  8%] Built target moveit_utils
[0.129s] [  9%] Built target gtest
[0.137s] [ 10%] Built target moveit_transforms
[0.137s] [ 11%] Built target gmock_main
[0.142s] [ 12%] Built target moveit_smoothing_base
[0.146s] [ 17%] Built target moveit_butterworth_parameters
[0.146s] [ 17%] Built target moveit_distance_field
[0.149s] [ 18%] Built target moveit_version
[0.151s] [ 19%] Built target test_transforms
[0.166s] [ 20%] Built target test_voxel_grid
[0.168s] [ 26%] Built target moveit_robot_model
[0.171s] [ 27%] Built target test_distance_field
[0.190s] [ 28%] Built target moveit_butterworth_filter
[0.191s] [ 30%] Built target moveit_test_utils
[0.196s] [ 33%] Built target moveit_robot_state
[0.215s] [ 34%] Built target test_butterworth_filter
[0.216s] [ 35%] Built target moveit_dynamics_solver
[0.224s] [ 36%] Built target moveit_robot_trajectory
[0.224s] [ 37%] Built target test_robot_model
[0.231s] [ 38%] Built target test_robot_state_benchmark
[0.232s] [ 39%] Built target moveit_kinematics_metrics
[0.239s] [ 42%] Built target test_aabb
[0.239s] [ 42%] Built target test_cartesian_interpolator
[0.240s] [ 43%] Built target test_robot_state
[0.240s] [ 44%] Built target test_robot_state_complex
[0.242s] [ 48%] Built target test_robot_trajectory
[0.243s] [ 50%] Built target moveit_collision_detection
[0.258s] [ 53%] Built target moveit_trajectory_processing
[0.258s] [ 55%] Built target moveit_planning_interface
[0.259s] [ 56%] Built target test_world
[0.260s] [ 58%] Built target test_world_diff
[0.263s] [ 59%] Built target test_all_valid
[0.269s] [ 61%] Built target moveit_collision_detection_fcl
[0.278s] [ 62%] Built target test_time_parameterization
[0.281s] [ 65%] Built target test_ruckig_traj_smoothing
[0.281s] [ 66%] Built target test_time_optimal_trajectory_generation
[0.281s] [ 70%] Built target moveit_collision_detection_bullet
[0.294s] [ 71%] Built target test_fcl_collision_env
[0.294s] [ 72%] Built target test_fcl_collision_detection
[0.298s] [ 74%] Built target moveit_kinematic_constraints
[0.299s] [ 76%] Built target test_fcl_collision_detection_panda
[0.302s] [ 77%] Built target test_bullet_collision_detection
[0.304s] [ 79%] Built target test_bullet_collision_detection_panda
[0.304s] [ 80%] Built target test_bullet_continuous_collision_checking
[0.316s] [ 81%] Built target test_constraints
[0.316s] [ 82%] Built target test_orientation_constraints
[0.319s] [ 84%] Built target moveit_planning_scene
[0.340s] [ 85%] Built target test_multi_threaded
[0.344s] [ 86%] Built target moveit_planning_request_adapter
[0.344s] [ 87%] Built target test_collision_objects
[0.349s] [ 88%] Built target test_planning_scene
[0.352s] [ 89%] Built target collision_detector_bullet_plugin
[0.352s] [ 90%] Built target collision_detector_fcl_plugin
[0.354s] [ 93%] Built target moveit_constraint_samplers
[0.358s] [ 96%] Built target moveit_collision_distance_field
[0.378s] [ 99%] Built target test_constraint_samplers
[0.382s] [100%] Built target test_collision_distance_field
