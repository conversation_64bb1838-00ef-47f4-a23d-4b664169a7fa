[0.105s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--mixin', 'release']
[0.105s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=['release'], verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffafcad450>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffafcacf40>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffafcacf40>>, mixin_verb=('build',))
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.303s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.304s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ws_moveit2'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ignore_ament_install'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['colcon_pkg']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'colcon_pkg'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['colcon_meta']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'colcon_meta'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extensions ['ros']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_param_builder) by extension 'ros'
[0.320s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launch_param_builder' with type 'ros.ament_python' and name 'launch_param_builder'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['ignore', 'ignore_ament_install']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ignore'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'ros'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['cmake', 'python']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'cmake'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'python'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extensions ['python_setup_py']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2) by extension 'python_setup_py'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['ignore', 'ignore_ament_install']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ignore'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'ros'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['cmake', 'python']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'cmake'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'python'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extensions ['python_setup_py']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/doc) by extension 'python_setup_py'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['ignore', 'ignore_ament_install']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ignore'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit) by extension 'ros'
[0.322s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit' with type 'ros.ament_cmake' and name 'moveit'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_commander) ignored
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ignore_ament_install'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['colcon_pkg']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'colcon_pkg'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['colcon_meta']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'colcon_meta'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extensions ['ros']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_common) by extension 'ros'
[0.323s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_common' with type 'ros.ament_cmake' and name 'moveit_common'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['ignore', 'ignore_ament_install']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ignore'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ignore_ament_install'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['colcon_pkg']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'colcon_pkg'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['colcon_meta']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'colcon_meta'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extensions ['ros']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_configs_utils) by extension 'ros'
[0.323s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_configs_utils' with type 'ros.ament_python' and name 'moveit_configs_utils'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['ignore', 'ignore_ament_install']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ignore'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ignore_ament_install'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['colcon_pkg']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'colcon_pkg'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['colcon_meta']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'colcon_meta'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extensions ['ros']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_core) by extension 'ros'
[0.325s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_core' with type 'ros.ament_cmake' and name 'moveit_core'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) by extensions ['ignore', 'ignore_ament_install']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) by extension 'ignore'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_experimental) ignored
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['ignore', 'ignore_ament_install']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ignore'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ignore_ament_install'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['colcon_pkg']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'colcon_pkg'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['colcon_meta']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'colcon_meta'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extensions ['ros']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_kinematics) by extension 'ros'
[0.327s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_kinematics' with type 'ros.ament_cmake' and name 'moveit_kinematics'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'ros'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['cmake', 'python']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'cmake'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'python'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extensions ['python_setup_py']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners) by extension 'python_setup_py'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'ros'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['cmake', 'python']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'cmake'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'python'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extensions ['python_setup_py']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp) by extension 'python_setup_py'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['colcon_pkg']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'colcon_pkg'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['colcon_meta']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'colcon_meta'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extensions ['ros']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_interface) by extension 'ros'
[0.329s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_interface' with type 'ros.ament_cmake' and name 'moveit_planners_chomp'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['ignore', 'ignore_ament_install']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ignore'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ignore_ament_install'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['colcon_pkg']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'colcon_pkg'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['colcon_meta']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'colcon_meta'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extensions ['ros']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_motion_planner) by extension 'ros'
[0.330s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_motion_planner' with type 'ros.ament_cmake' and name 'chomp_motion_planner'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['ignore', 'ignore_ament_install']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ignore'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ignore_ament_install'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['colcon_pkg']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'colcon_pkg'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['colcon_meta']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'colcon_meta'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extensions ['ros']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter) by extension 'ros'
[0.331s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter' with type 'ros.ament_cmake' and name 'moveit_chomp_optimizer_adapter'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['ignore', 'ignore_ament_install']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ignore'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ignore_ament_install'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['colcon_pkg']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'colcon_pkg'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['colcon_meta']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'colcon_meta'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extensions ['ros']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/moveit_planners) by extension 'ros'
[0.332s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/moveit_planners' with type 'ros.ament_cmake' and name 'moveit_planners'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['ignore', 'ignore_ament_install']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ignore'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ignore_ament_install'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['colcon_pkg']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'colcon_pkg'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['colcon_meta']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'colcon_meta'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extensions ['ros']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/ompl) by extension 'ros'
[0.333s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/ompl' with type 'ros.ament_cmake' and name 'moveit_planners_ompl'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['ignore', 'ignore_ament_install']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ignore'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ignore_ament_install'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['colcon_pkg']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'colcon_pkg'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['colcon_meta']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'colcon_meta'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extensions ['ros']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner) by extension 'ros'
[0.334s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/pilz_industrial_motion_planner' with type 'ros.ament_cmake' and name 'pilz_industrial_motion_planner'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['ignore', 'ignore_ament_install']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ignore'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ignore_ament_install'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['colcon_pkg']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'colcon_pkg'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['colcon_meta']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'colcon_meta'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extensions ['ros']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils) by extension 'ros'
[0.336s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils' with type 'ros.ament_cmake' and name 'pilz_industrial_motion_planner_testutils'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['ignore', 'ignore_ament_install']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ignore'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ignore_ament_install'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['colcon_pkg']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'colcon_pkg'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['colcon_meta']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'colcon_meta'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['ros']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'ros'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['cmake', 'python']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'cmake'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'python'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extensions ['python_setup_py']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs) by extension 'python_setup_py'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ignore'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ignore_ament_install'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['colcon_pkg']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'colcon_pkg'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['colcon_meta']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'colcon_meta'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extensions ['ros']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin) by extension 'ros'
[0.337s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_ikfast_manipulator_plugin'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ignore'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ignore_ament_install'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['colcon_pkg']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'colcon_pkg'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['colcon_meta']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'colcon_meta'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extensions ['ros']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_moveit_config) by extension 'ros'
[0.338s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_moveit_config'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ignore_ament_install'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['colcon_pkg']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'colcon_pkg'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['colcon_meta']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'colcon_meta'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extensions ['ros']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_pg70_support) by extension 'ros'
[0.338s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_pg70_support' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_pg70_support'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ignore_ament_install'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['colcon_pkg']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'colcon_pkg'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['colcon_meta']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'colcon_meta'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extensions ['ros']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/test_configs/prbt_support) by extension 'ros'
[0.340s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_planners/test_configs/prbt_support' with type 'ros.ament_cmake' and name 'moveit_resources_prbt_support'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_planners/trajopt) ignored
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'ros'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['cmake', 'python']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'cmake'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'python'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extensions ['python_setup_py']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins) by extension 'python_setup_py'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_plugins) by extension 'ros'
[0.341s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_plugins' with type 'ros.ament_cmake' and name 'moveit_plugins'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['ignore', 'ignore_ament_install']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_ros_control_interface) by extension 'ros'
[0.342s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_ros_control_interface' with type 'ros.ament_cmake' and name 'moveit_ros_control_interface'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_plugins/moveit_simple_controller_manager) by extension 'ros'
[0.343s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_plugins/moveit_simple_controller_manager' with type 'ros.ament_cmake' and name 'moveit_simple_controller_manager'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'colcon_meta'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['ros']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'ros'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['cmake', 'python']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'cmake'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'python'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extensions ['python_setup_py']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros) by extension 'python_setup_py'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'colcon_meta'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extensions ['ros']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/benchmarks) by extension 'ros'
[0.350s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/benchmarks' with type 'ros.ament_cmake' and name 'moveit_ros_benchmarks'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['ignore', 'ignore_ament_install']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ignore'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ignore_ament_install'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['colcon_pkg']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'colcon_pkg'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['colcon_meta']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'colcon_meta'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extensions ['ros']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/hybrid_planning) by extension 'ros'
[0.352s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/hybrid_planning' with type 'ros.ament_cmake' and name 'moveit_hybrid_planning'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['ignore', 'ignore_ament_install']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ignore'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ignore_ament_install'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['colcon_pkg']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'colcon_pkg'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['colcon_meta']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/move_group) by extension 'ros'
[0.353s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/move_group' with type 'ros.ament_cmake' and name 'moveit_ros_move_group'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ignore_ament_install'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['colcon_pkg']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'colcon_pkg'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['colcon_meta']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'colcon_meta'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extensions ['ros']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_ros) by extension 'ros'
[0.354s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/moveit_ros' with type 'ros.ament_cmake' and name 'moveit_ros'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ignore_ament_install'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['colcon_pkg']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'colcon_pkg'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['colcon_meta']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'colcon_meta'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extensions ['ros']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/moveit_servo) by extension 'ros'
[0.356s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/moveit_servo' with type 'ros.ament_cmake' and name 'moveit_servo'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['ignore', 'ignore_ament_install']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ignore'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ignore_ament_install'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['colcon_pkg']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'colcon_pkg'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['colcon_meta']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'colcon_meta'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extensions ['ros']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/occupancy_map_monitor) by extension 'ros'
[0.357s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/occupancy_map_monitor' with type 'ros.ament_cmake' and name 'moveit_ros_occupancy_map_monitor'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['ignore', 'ignore_ament_install']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ignore'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ignore_ament_install'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['colcon_pkg']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'colcon_pkg'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['colcon_meta']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'colcon_meta'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extensions ['ros']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/perception) by extension 'ros'
[0.359s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/perception' with type 'ros.ament_cmake' and name 'moveit_ros_perception'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['ignore', 'ignore_ament_install']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ignore'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ignore_ament_install'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['colcon_pkg']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'colcon_pkg'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['colcon_meta']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'colcon_meta'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extensions ['ros']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning) by extension 'ros'
[0.360s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/planning' with type 'ros.ament_cmake' and name 'moveit_ros_planning'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['ignore', 'ignore_ament_install']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ignore'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ignore_ament_install'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['colcon_pkg']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'colcon_pkg'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['colcon_meta']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'colcon_meta'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extensions ['ros']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/planning_interface) by extension 'ros'
[0.362s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/planning_interface' with type 'ros.ament_cmake' and name 'moveit_ros_planning_interface'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['ignore', 'ignore_ament_install']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ignore'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ignore_ament_install'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['colcon_pkg']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'colcon_pkg'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['colcon_meta']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'colcon_meta'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extensions ['ros']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/robot_interaction) by extension 'ros'
[0.363s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/robot_interaction' with type 'ros.ament_cmake' and name 'moveit_ros_robot_interaction'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['ignore', 'ignore_ament_install']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ignore'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ignore_ament_install'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['colcon_pkg']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'colcon_pkg'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['colcon_meta']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'colcon_meta'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extensions ['ros']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/visualization) by extension 'ros'
[0.364s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/visualization' with type 'ros.ament_cmake' and name 'moveit_ros_visualization'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['ignore', 'ignore_ament_install']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ignore'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ignore_ament_install'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['colcon_pkg']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'colcon_pkg'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['colcon_meta']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'colcon_meta'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extensions ['ros']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_ros/warehouse) by extension 'ros'
[0.365s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_ros/warehouse' with type 'ros.ament_cmake' and name 'moveit_ros_warehouse'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['ignore', 'ignore_ament_install']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ignore'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ignore_ament_install'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['colcon_pkg']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'colcon_pkg'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['colcon_meta']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'colcon_meta'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extensions ['ros']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_runtime) by extension 'ros'
[0.366s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_runtime' with type 'ros.ament_cmake' and name 'moveit_runtime'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['ignore', 'ignore_ament_install']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ignore'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ignore_ament_install'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['colcon_pkg']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'colcon_pkg'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['colcon_meta']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'colcon_meta'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['ros']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'ros'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['cmake', 'python']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'cmake'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'python'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extensions ['python_setup_py']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant) by extension 'python_setup_py'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'colcon_meta'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['ros']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'ros'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['cmake', 'python']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'cmake'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'python'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extensions ['python_setup_py']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics) by extension 'python_setup_py'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'colcon_meta'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['ros']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'ros'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['cmake', 'python']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'cmake'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'python'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extensions ['python_setup_py']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/graphics/source) by extension 'python_setup_py'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'colcon_meta'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extensions ['ros']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins) by extension 'ros'
[0.368s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_app_plugins'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['ignore', 'ignore_ament_install']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ignore'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ignore_ament_install'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['colcon_pkg']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'colcon_pkg'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['colcon_meta']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'colcon_meta'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extensions ['ros']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_assistant) by extension 'ros'
[0.369s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_assistant' with type 'ros.ament_cmake' and name 'moveit_setup_assistant'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['ignore', 'ignore_ament_install']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ignore'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ignore_ament_install'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['colcon_pkg']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'colcon_pkg'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['colcon_meta']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'colcon_meta'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extensions ['ros']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_controllers) by extension 'ros'
[0.370s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_controllers' with type 'ros.ament_cmake' and name 'moveit_setup_controllers'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ignore'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ignore_ament_install'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['colcon_pkg']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'colcon_pkg'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['colcon_meta']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'colcon_meta'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extensions ['ros']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins) by extension 'ros'
[0.371s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_core_plugins'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['ignore', 'ignore_ament_install']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ignore'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ignore_ament_install'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['colcon_pkg']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'colcon_pkg'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['colcon_meta']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'colcon_meta'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extensions ['ros']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_framework) by extension 'ros'
[0.373s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_framework' with type 'ros.ament_cmake' and name 'moveit_setup_framework'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) by extensions ['ignore', 'ignore_ament_install']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) by extension 'ignore'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_simulation) ignored
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['ignore', 'ignore_ament_install']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ignore'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ignore_ament_install'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['colcon_pkg']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'colcon_pkg'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['colcon_meta']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'colcon_meta'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extensions ['ros']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins) by extension 'ros'
[0.373s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins' with type 'ros.ament_cmake' and name 'moveit_setup_srdf_plugins'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['ignore', 'ignore_ament_install']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ignore'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ignore_ament_install'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['colcon_pkg']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'colcon_pkg'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['colcon_meta']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'colcon_meta'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['ros']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'ros'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['cmake', 'python']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'cmake'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'python'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extensions ['python_setup_py']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2/moveit_setup_assistant/unported_templates) by extension 'python_setup_py'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['ignore', 'ignore_ament_install']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ignore'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ignore_ament_install'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['colcon_pkg']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'colcon_pkg'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['colcon_meta']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'colcon_meta'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extensions ['ros']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit2_tutorials) by extension 'ros'
[0.375s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit2_tutorials' with type 'ros.ament_cmake' and name 'moveit2_tutorials'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['ignore', 'ignore_ament_install']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ignore'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ignore_ament_install'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['colcon_pkg']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'colcon_pkg'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['colcon_meta']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'colcon_meta'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['ros']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'ros'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['cmake', 'python']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'cmake'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'python'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extensions ['python_setup_py']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources) by extension 'python_setup_py'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['ignore', 'ignore_ament_install']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ignore'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ignore_ament_install'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['colcon_pkg']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'colcon_pkg'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['colcon_meta']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'colcon_meta'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extensions ['ros']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_description) by extension 'ros'
[0.377s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/fanuc_description' with type 'ros.ament_cmake' and name 'moveit_resources_fanuc_description'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ignore_ament_install'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['colcon_pkg']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'colcon_pkg'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['colcon_meta']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'colcon_meta'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extensions ['ros']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/fanuc_moveit_config) by extension 'ros'
[0.377s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/fanuc_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_fanuc_moveit_config'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ignore_ament_install'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['colcon_pkg']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'colcon_pkg'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['colcon_meta']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'colcon_meta'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extensions ['ros']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/moveit_resources) by extension 'ros'
[0.378s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/moveit_resources' with type 'ros.ament_cmake' and name 'moveit_resources'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['ignore', 'ignore_ament_install']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ignore'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ignore_ament_install'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['colcon_pkg']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'colcon_pkg'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['colcon_meta']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'colcon_meta'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extensions ['ros']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_description) by extension 'ros'
[0.379s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/panda_description' with type 'ros.ament_cmake' and name 'moveit_resources_panda_description'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ignore'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ignore_ament_install'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['colcon_pkg']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'colcon_pkg'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['colcon_meta']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'colcon_meta'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extensions ['ros']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/panda_moveit_config) by extension 'ros'
[0.379s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/panda_moveit_config' with type 'ros.ament_cmake' and name 'moveit_resources_panda_moveit_config'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['ignore', 'ignore_ament_install']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ignore'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ignore_ament_install'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['colcon_pkg']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'colcon_pkg'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['colcon_meta']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'colcon_meta'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extensions ['ros']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_resources/pr2_description) by extension 'ros'
[0.380s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_resources/pr2_description' with type 'ros.ament_cmake' and name 'moveit_resources_pr2_description'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['ignore', 'ignore_ament_install']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ignore'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ignore_ament_install'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['colcon_pkg']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'colcon_pkg'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['colcon_meta']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'colcon_meta'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['ros']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'ros'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['cmake', 'python']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'cmake'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'python'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extensions ['python_setup_py']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor) by extension 'python_setup_py'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['ignore', 'ignore_ament_install']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ignore'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ignore_ament_install'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['colcon_pkg']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'colcon_pkg'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['colcon_meta']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'colcon_meta'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extensions ['ros']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/capabilities) by extension 'ros'
[0.381s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/capabilities' with type 'ros.ament_cmake' and name 'moveit_task_constructor_capabilities'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['ignore', 'ignore_ament_install']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ignore'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ignore_ament_install'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['colcon_pkg']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'colcon_pkg'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['colcon_meta']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'colcon_meta'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extensions ['ros']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/core) by extension 'ros'
[0.382s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/core' with type 'ros.ament_cmake' and name 'moveit_task_constructor_core'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ignore_ament_install'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['colcon_pkg']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'colcon_pkg'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['colcon_meta']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/demo) by extension 'ros'
[0.383s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/demo' with type 'ros.ament_cmake' and name 'moveit_task_constructor_demo'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ignore_ament_install'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['colcon_pkg']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'colcon_pkg'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['colcon_meta']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/msgs) by extension 'ros'
[0.384s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/msgs' with type 'ros.ament_cmake' and name 'moveit_task_constructor_msgs'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'colcon_pkg'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['colcon_meta']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'colcon_meta'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extensions ['ros']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/rviz_marker_tools) by extension 'ros'
[0.385s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/rviz_marker_tools' with type 'ros.ament_cmake' and name 'rviz_marker_tools'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['ignore', 'ignore_ament_install']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ignore'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ignore_ament_install'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['colcon_pkg']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'colcon_pkg'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['colcon_meta']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_task_constructor/visualization) by extension 'ros'
[0.385s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_task_constructor/visualization' with type 'ros.ament_cmake' and name 'moveit_task_constructor_visualization'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['ignore', 'ignore_ament_install']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ignore'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ignore_ament_install'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['colcon_pkg']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'colcon_pkg'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['colcon_meta']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'colcon_meta'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extensions ['ros']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/moveit_visual_tools) by extension 'ros'
[0.387s] DEBUG:colcon.colcon_core.package_identification:Package 'src/moveit_visual_tools' with type 'ros.ament_cmake' and name 'moveit_visual_tools'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['ignore', 'ignore_ament_install']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ignore'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ignore_ament_install'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['colcon_pkg']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'colcon_pkg'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['colcon_meta']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'colcon_meta'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extensions ['ros']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/rosparam_shortcuts) by extension 'ros'
[0.387s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rosparam_shortcuts' with type 'ros.ament_cmake' and name 'rosparam_shortcuts'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['ignore', 'ignore_ament_install']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ignore'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ignore_ament_install'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['colcon_pkg']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'colcon_pkg'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['colcon_meta']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'colcon_meta'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extensions ['ros']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/srdfdom) by extension 'ros'
[0.388s] DEBUG:colcon.colcon_core.package_identification:Package 'src/srdfdom' with type 'ros.ament_cmake' and name 'srdfdom'
[0.388s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.388s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.388s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.388s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.388s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.437s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.437s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.439s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 335 installed packages in /opt/ros/humble
[0.439s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_target' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_clean_cache' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_clean_first' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'cmake_force_configure' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'ament_cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'catkin_cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'launch_param_builder' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.464s] DEBUG:colcon.colcon_core.verb:Building package 'launch_param_builder' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/launch_param_builder', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/launch_param_builder', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/launch_param_builder', 'symlink_install': False, 'test_result_base': None}
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_target' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_clean_cache' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_clean_first' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'cmake_force_configure' from command line to 'False'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'ament_cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'catkin_cmake_args' from command line to 'None'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_common' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.464s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_common' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_common', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_common', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_common', 'symlink_install': False, 'test_result_base': None}
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.464s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_target' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_clean_first' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'cmake_force_configure' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'ament_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.465s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_fanuc_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description', 'symlink_install': False, 'test_result_base': None}
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_target' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_clean_first' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'cmake_force_configure' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'ament_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.465s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_panda_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description', 'symlink_install': False, 'test_result_base': None}
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_target' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_clean_first' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'cmake_force_configure' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'ament_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_pr2_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.465s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_pr2_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description', 'symlink_install': False, 'test_result_base': None}
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_target' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_clean_cache' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_clean_first' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'cmake_force_configure' from command line to 'False'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'ament_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'catkin_cmake_args' from command line to 'None'
[0.465s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_support' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.465s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_support' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.466s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_clean_cache' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_clean_first' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'cmake_force_configure' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'ament_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'catkin_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'rosparam_shortcuts' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.466s] DEBUG:colcon.colcon_core.verb:Building package 'rosparam_shortcuts' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/rosparam_shortcuts', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/rosparam_shortcuts', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/rosparam_shortcuts', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_clean_cache' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_clean_first' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'cmake_force_configure' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'ament_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'catkin_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'srdfdom' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.466s] DEBUG:colcon.colcon_core.verb:Building package 'srdfdom' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/srdfdom', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/srdfdom', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/srdfdom', 'symlink_install': False, 'test_result_base': None}
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_target' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_clean_cache' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_clean_first' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'cmake_force_configure' from command line to 'False'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'ament_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'catkin_cmake_args' from command line to 'None'
[0.466s] Level 5:colcon.colcon_core.verb:set package 'moveit_configs_utils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.466s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_configs_utils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_configs_utils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_configs_utils', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_configs_utils', 'symlink_install': False, 'test_result_base': None}
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_fanuc_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.467s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_fanuc_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_panda_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.467s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_panda_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_target' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'rviz_marker_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.467s] DEBUG:colcon.colcon_core.verb:Building package 'rviz_marker_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/rviz_marker_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/rviz_marker_tools', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools', 'symlink_install': False, 'test_result_base': None}
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_target' from command line to 'None'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_clean_cache' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_clean_first' from command line to 'False'
[0.467s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'cmake_force_configure' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'ament_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'catkin_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_core' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.468s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_core' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_core', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_core', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_core', 'symlink_install': False, 'test_result_base': None}
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_target' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_clean_cache' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_clean_first' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'cmake_force_configure' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'ament_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'catkin_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.468s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources', 'symlink_install': False, 'test_result_base': None}
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_target' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'chomp_motion_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.468s] DEBUG:colcon.colcon_core.verb:Building package 'chomp_motion_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/chomp_motion_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/chomp_motion_planner', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_motion_planner', 'symlink_install': False, 'test_result_base': None}
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_target' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.468s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_ikfast_manipulator_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.468s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_ikfast_manipulator_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin', 'symlink_install': False, 'test_result_base': None}
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_target' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_clean_cache' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_clean_first' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'cmake_force_configure' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'ament_cmake_args' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'catkin_cmake_args' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_occupancy_map_monitor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.469s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_occupancy_map_monitor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_occupancy_map_monitor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/occupancy_map_monitor', 'symlink_install': False, 'test_result_base': None}
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_target' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_clean_cache' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_clean_first' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'cmake_force_configure' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'ament_cmake_args' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'catkin_cmake_args' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'moveit_simple_controller_manager' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.469s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_simple_controller_manager' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_simple_controller_manager', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_simple_controller_manager', 'symlink_install': False, 'test_result_base': None}
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_target' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_clean_cache' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_clean_first' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'cmake_force_configure' from command line to 'False'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'ament_cmake_args' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'catkin_cmake_args' from command line to 'None'
[0.469s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner_testutils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.469s] DEBUG:colcon.colcon_core.verb:Building package 'pilz_industrial_motion_planner_testutils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner_testutils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner_testutils', 'symlink_install': False, 'test_result_base': None}
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_target' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_clean_cache' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_clean_first' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'cmake_force_configure' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'ament_cmake_args' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'catkin_cmake_args' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_chomp_optimizer_adapter' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.470s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_chomp_optimizer_adapter' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_optimizer_adapter', 'symlink_install': False, 'test_result_base': None}
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_target' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_clean_cache' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_clean_first' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'cmake_force_configure' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'ament_cmake_args' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'catkin_cmake_args' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_chomp' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.470s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners_chomp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners_chomp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners_chomp', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/chomp/chomp_interface', 'symlink_install': False, 'test_result_base': None}
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_target' from command line to 'None'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.470s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.471s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins', 'symlink_install': False, 'test_result_base': None}
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_target' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_control_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.471s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_control_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_control_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_control_interface', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_ros_control_interface', 'symlink_install': False, 'test_result_base': None}
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_target' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_clean_cache' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_clean_first' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'cmake_force_configure' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'ament_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'catkin_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.471s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_planning' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_planning', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_planning', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning', 'symlink_install': False, 'test_result_base': None}
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_target' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_clean_cache' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_clean_first' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'cmake_force_configure' from command line to 'False'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'ament_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'catkin_cmake_args' from command line to 'None'
[0.471s] Level 5:colcon.colcon_core.verb:set package 'moveit_kinematics' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.471s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_kinematics' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_kinematics', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_kinematics', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_kinematics', 'symlink_install': False, 'test_result_base': None}
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_target' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_clean_cache' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_clean_first' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'cmake_force_configure' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'ament_cmake_args' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'catkin_cmake_args' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners_ompl' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.472s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners_ompl' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners_ompl', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners_ompl', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/ompl', 'symlink_install': False, 'test_result_base': None}
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_target' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_clean_cache' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_clean_first' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'cmake_force_configure' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'ament_cmake_args' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'catkin_cmake_args' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_perception' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.472s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_perception' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_perception', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_perception', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception', 'symlink_install': False, 'test_result_base': None}
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_target' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_clean_cache' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_clean_first' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'cmake_force_configure' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'ament_cmake_args' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'catkin_cmake_args' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_robot_interaction' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.472s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_robot_interaction' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_robot_interaction', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/robot_interaction', 'symlink_install': False, 'test_result_base': None}
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_target' from command line to 'None'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_clean_cache' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_clean_first' from command line to 'False'
[0.472s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'cmake_force_configure' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'ament_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'catkin_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_warehouse' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.473s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_warehouse' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_warehouse', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/warehouse', 'symlink_install': False, 'test_result_base': None}
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_target' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_visual_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.473s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_visual_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_visual_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_visual_tools', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_visual_tools', 'symlink_install': False, 'test_result_base': None}
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_target' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_clean_cache' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_clean_first' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'cmake_force_configure' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'ament_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'catkin_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_benchmarks' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.473s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_benchmarks' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_benchmarks', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/benchmarks', 'symlink_install': False, 'test_result_base': None}
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_target' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_clean_cache' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_clean_first' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'cmake_force_configure' from command line to 'False'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'ament_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'catkin_cmake_args' from command line to 'None'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_move_group' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.473s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_move_group' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_move_group', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_move_group', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/move_group', 'symlink_install': False, 'test_result_base': None}
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.473s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.474s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_target' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_planning_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.474s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_planning_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_planning_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/planning_interface', 'symlink_install': False, 'test_result_base': None}
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_target' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_clean_cache' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_clean_first' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'cmake_force_configure' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'ament_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'catkin_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_hybrid_planning' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.474s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_hybrid_planning' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_hybrid_planning', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_hybrid_planning', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/hybrid_planning', 'symlink_install': False, 'test_result_base': None}
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_target' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_clean_cache' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_clean_first' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'cmake_force_configure' from command line to 'False'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'ament_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'catkin_cmake_args' from command line to 'None'
[0.474s] Level 5:colcon.colcon_core.verb:set package 'moveit_resources_prbt_pg70_support' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.474s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_resources_prbt_pg70_support' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_pg70_support', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_pg70_support', 'symlink_install': False, 'test_result_base': None}
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_target' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_clean_cache' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_clean_first' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'cmake_force_configure' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'ament_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'catkin_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros_visualization' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.475s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros_visualization' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros_visualization', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros_visualization', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization', 'symlink_install': False, 'test_result_base': None}
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_target' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_clean_cache' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_clean_first' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'cmake_force_configure' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'ament_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'catkin_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_servo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.475s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_servo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_servo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_servo', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_servo', 'symlink_install': False, 'test_result_base': None}
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_target' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_clean_cache' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_clean_first' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'cmake_force_configure' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'ament_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'catkin_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_ros' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.475s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_ros' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_ros', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_ros', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_ros', 'symlink_install': False, 'test_result_base': None}
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_target' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_clean_cache' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_clean_first' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'cmake_force_configure' from command line to 'False'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'ament_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'catkin_cmake_args' from command line to 'None'
[0.475s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_framework' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.475s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_framework' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_framework', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_framework', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_framework', 'symlink_install': False, 'test_result_base': None}
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_target' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'pilz_industrial_motion_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.476s] DEBUG:colcon.colcon_core.verb:Building package 'pilz_industrial_motion_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/pilz_industrial_motion_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/pilz_industrial_motion_planner', 'symlink_install': False, 'test_result_base': None}
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_target' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_clean_cache' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_clean_first' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'cmake_force_configure' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'ament_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'catkin_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_planners' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.476s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_planners' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_planners', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_planners', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/moveit_planners', 'symlink_install': False, 'test_result_base': None}
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_target' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_app_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.476s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_app_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_app_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_app_plugins', 'symlink_install': False, 'test_result_base': None}
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_target' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_clean_cache' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_clean_first' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'cmake_force_configure' from command line to 'False'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'ament_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'catkin_cmake_args' from command line to 'None'
[0.476s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_controllers' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.476s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_controllers' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_controllers', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_controllers', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_controllers', 'symlink_install': False, 'test_result_base': None}
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_target' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_core_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.477s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_core_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_core_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_core_plugins', 'symlink_install': False, 'test_result_base': None}
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_target' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_clean_cache' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_clean_first' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'cmake_force_configure' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'ament_cmake_args' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'catkin_cmake_args' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_srdf_plugins' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.477s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_srdf_plugins' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_srdf_plugins', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_srdf_plugins', 'symlink_install': False, 'test_result_base': None}
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_target' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_clean_cache' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_clean_first' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'cmake_force_configure' from command line to 'False'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'ament_cmake_args' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'catkin_cmake_args' from command line to 'None'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_runtime' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.477s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_runtime' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_runtime', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_runtime', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_runtime', 'symlink_install': False, 'test_result_base': None}
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.477s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_target' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_clean_cache' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_clean_first' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'cmake_force_configure' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'ament_cmake_args' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'catkin_cmake_args' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_setup_assistant' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.478s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_setup_assistant' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_setup_assistant', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_setup_assistant', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_assistant', 'symlink_install': False, 'test_result_base': None}
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_target' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_clean_cache' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_clean_first' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'cmake_force_configure' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'ament_cmake_args' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'catkin_cmake_args' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_core' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.478s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_core' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_core', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/core', 'symlink_install': False, 'test_result_base': None}
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_target' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_clean_cache' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_clean_first' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'cmake_force_configure' from command line to 'False'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'ament_cmake_args' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'catkin_cmake_args' from command line to 'None'
[0.478s] Level 5:colcon.colcon_core.verb:set package 'moveit' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.478s] DEBUG:colcon.colcon_core.verb:Building package 'moveit' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2/moveit', 'symlink_install': False, 'test_result_base': None}
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_target' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_clean_cache' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_clean_first' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'cmake_force_configure' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'ament_cmake_args' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'catkin_cmake_args' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_capabilities' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.479s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_capabilities' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_capabilities', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_capabilities', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/capabilities', 'symlink_install': False, 'test_result_base': None}
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_target' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_clean_cache' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_clean_first' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'cmake_force_configure' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'ament_cmake_args' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'catkin_cmake_args' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_visualization' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.479s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_visualization' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_visualization', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_visualization', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/visualization', 'symlink_install': False, 'test_result_base': None}
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_target' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_clean_cache' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_clean_first' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'cmake_force_configure' from command line to 'False'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'ament_cmake_args' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'catkin_cmake_args' from command line to 'None'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit2_tutorials' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.479s] DEBUG:colcon.colcon_core.verb:Building package 'moveit2_tutorials' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit2_tutorials', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit2_tutorials', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit2_tutorials', 'symlink_install': False, 'test_result_base': None}
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.479s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_target' from command line to 'None'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_clean_cache' from command line to 'False'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_clean_first' from command line to 'False'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'cmake_force_configure' from command line to 'False'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'ament_cmake_args' from command line to 'None'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'catkin_cmake_args' from command line to 'None'
[0.480s] Level 5:colcon.colcon_core.verb:set package 'moveit_task_constructor_demo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.480s] DEBUG:colcon.colcon_core.verb:Building package 'moveit_task_constructor_demo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_demo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_moveit2/install/moveit_task_constructor_demo', 'merge_install': False, 'path': '/home/<USER>/ws_moveit2/src/moveit_task_constructor/demo', 'symlink_install': False, 'test_result_base': None}
[0.480s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.480s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.481s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description' with build type 'ament_cmake'
[0.481s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_description'
[0.485s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.485s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.485s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.487s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_common' with build type 'ament_cmake'
[0.487s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_common'
[0.487s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.487s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.489s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description' with build type 'ament_cmake'
[0.489s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/pr2_description'
[0.489s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.489s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.491s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/srdfdom' with build type 'ament_cmake'
[0.491s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/srdfdom'
[0.491s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.491s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.493s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_moveit2/src/launch_param_builder' with build type 'ament_python'
[0.493s] Level 1:colcon.colcon_core.shell:create_environment_hook('launch_param_builder', 'ament_prefix_path')
[0.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.ps1'
[0.495s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/hook/ament_prefix_path.sh'
[0.497s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.497s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.498s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description' with build type 'ament_cmake'
[0.498s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_description'
[0.499s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.499s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.501s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support' with build type 'ament_cmake'
[0.501s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_support'
[0.501s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.501s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.503s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs' with build type 'ament_cmake'
[0.503s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs'
[0.503s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.503s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.510s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_description -- -j8 -l8
[0.514s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.518s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description -- -j8 -l8
[0.521s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.667s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_moveit2/src/launch_param_builder'
[0.667s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.667s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.673s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description -- -j8 -l8
[0.676s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support -- -j8 -l8
[0.678s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[0.689s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_description -- -j8 -l8
[0.692s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_description
[0.695s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description -- -j8 -l8
[0.700s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description
[0.703s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support -- -j8 -l8
[0.703s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support
[0.716s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_common -- -j8 -l8
[0.720s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_common': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.721s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description -- -j8 -l8
[0.727s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description
[0.745s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_description)
[0.752s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake module files
[0.752s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake config files
[0.753s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_description', 'cmake_prefix_path')
[0.753s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.ps1'
[0.755s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.755s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.dsv'
[0.760s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[0.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.sh'
[0.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/pkgconfig/moveit_resources_panda_description.pc'
[0.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/python3.10/site-packages'
[0.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.765s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.ps1'
[0.773s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_description
[0.776s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv'
[0.777s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh'
[0.778s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.bash'
[0.779s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.zsh'
[0.780s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/colcon-core/packages/moveit_resources_panda_description)
[0.785s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_description)
[0.785s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake module files
[0.787s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description' for CMake config files
[0.787s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_description', 'cmake_prefix_path')
[0.787s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.ps1'
[0.791s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.dsv'
[0.792s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/hook/cmake_prefix_path.sh'
[0.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/pkgconfig/moveit_resources_panda_description.pc'
[0.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/lib/python3.10/site-packages'
[0.793s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/bin'
[0.793s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.ps1'
[0.794s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv'
[0.794s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh'
[0.795s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.bash'
[0.795s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.zsh'
[0.795s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/colcon-core/packages/moveit_resources_panda_description)
[0.796s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_description)
[0.797s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake module files
[0.797s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake config files
[0.797s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_description', 'cmake_prefix_path')
[0.797s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.ps1'
[0.797s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.dsv'
[0.798s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description
[0.800s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.sh'
[0.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/pkgconfig/moveit_resources_fanuc_description.pc'
[0.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/python3.10/site-packages'
[0.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.802s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.ps1'
[0.803s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv'
[0.804s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh'
[0.804s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.bash'
[0.805s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.zsh'
[0.805s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/colcon-core/packages/moveit_resources_fanuc_description)
[0.806s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_description)
[0.807s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake module files
[0.807s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description' for CMake config files
[0.807s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_description', 'cmake_prefix_path')
[0.808s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.ps1'
[0.808s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.dsv'
[0.808s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/hook/cmake_prefix_path.sh'
[0.808s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.808s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/pkgconfig/moveit_resources_fanuc_description.pc'
[0.809s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/lib/python3.10/site-packages'
[0.809s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/bin'
[0.809s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.ps1'
[0.809s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv'
[0.809s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh'
[0.809s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.bash'
[0.810s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.zsh'
[0.810s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/colcon-core/packages/moveit_resources_fanuc_description)
[0.811s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_common)
[0.812s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_common' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_common
[0.812s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake module files
[0.813s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake config files
[0.813s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_common', 'cmake_prefix_path')
[0.813s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.ps1'
[0.813s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.dsv'
[0.813s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.sh'
[0.814s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.814s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/pkgconfig/moveit_common.pc'
[0.814s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/python3.10/site-packages'
[0.814s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.814s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.ps1'
[0.815s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv'
[0.815s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh'
[0.815s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.bash'
[0.815s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.zsh'
[0.815s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_common/share/colcon-core/packages/moveit_common)
[0.816s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_common)
[0.816s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake module files
[0.817s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common' for CMake config files
[0.817s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_common', 'cmake_prefix_path')
[0.817s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.ps1'
[0.818s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.dsv'
[0.818s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/hook/cmake_prefix_path.sh'
[0.819s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.819s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/pkgconfig/moveit_common.pc'
[0.819s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/lib/python3.10/site-packages'
[0.819s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_common/bin'
[0.819s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.ps1'
[0.819s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv'
[0.820s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh'
[0.820s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.bash'
[0.820s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.zsh'
[0.820s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_common/share/colcon-core/packages/moveit_common)
[0.821s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_prbt_support)
[0.821s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_prbt_support
[0.821s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake module files
[0.822s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake config files
[0.822s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_prbt_support', 'cmake_prefix_path')
[0.822s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.ps1'
[0.823s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.dsv'
[0.823s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.sh'
[0.824s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.824s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/pkgconfig/moveit_resources_prbt_support.pc'
[0.825s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/python3.10/site-packages'
[0.825s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.825s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.ps1'
[0.825s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv'
[0.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh'
[0.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.bash'
[0.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.zsh'
[0.827s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/colcon-core/packages/moveit_resources_prbt_support)
[0.827s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_prbt_support)
[0.828s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake module files
[0.828s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support' for CMake config files
[0.828s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_prbt_support', 'cmake_prefix_path')
[0.828s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.ps1'
[0.829s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.dsv'
[0.829s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/hook/cmake_prefix_path.sh'
[0.829s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.829s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/pkgconfig/moveit_resources_prbt_support.pc'
[0.829s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/lib/python3.10/site-packages'
[0.830s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/bin'
[0.830s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.ps1'
[0.830s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv'
[0.830s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh'
[0.830s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.bash'
[0.831s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.zsh'
[0.831s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/colcon-core/packages/moveit_resources_prbt_support)
[0.833s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config' with build type 'ament_cmake'
[0.833s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/panda_moveit_config'
[0.833s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.833s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.836s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config' with build type 'ament_cmake'
[0.836s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/fanuc_moveit_config'
[0.836s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.836s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.842s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools' with build type 'ament_cmake'
[0.842s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_task_constructor/rviz_marker_tools'
[0.842s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.842s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.845s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/rosparam_shortcuts' with build type 'ament_cmake'
[0.846s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/rosparam_shortcuts'
[0.846s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.846s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.849s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_pr2_description)
[0.850s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_pr2_description
[0.850s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake module files
[0.851s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake config files
[0.851s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_pr2_description', 'cmake_prefix_path')
[0.851s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.ps1'
[0.852s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.dsv'
[0.852s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.sh'
[0.853s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.853s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/pkgconfig/moveit_resources_pr2_description.pc'
[0.853s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/python3.10/site-packages'
[0.853s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.853s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.ps1'
[0.854s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv'
[0.854s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh'
[0.854s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.bash'
[0.854s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.zsh'
[0.854s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/colcon-core/packages/moveit_resources_pr2_description)
[0.855s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_pr2_description)
[0.855s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake module files
[0.855s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description' for CMake config files
[0.855s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_pr2_description', 'cmake_prefix_path')
[0.856s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.ps1'
[0.856s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.dsv'
[0.856s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/hook/cmake_prefix_path.sh'
[0.856s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.856s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/pkgconfig/moveit_resources_pr2_description.pc'
[0.857s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/lib/python3.10/site-packages'
[0.857s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/bin'
[0.857s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.ps1'
[0.857s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv'
[0.858s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh'
[0.858s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.bash'
[0.858s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.zsh'
[0.858s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/colcon-core/packages/moveit_resources_pr2_description)
[0.859s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(srdfdom)
[0.859s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/srdfdom
[0.860s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake module files
[0.860s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake config files
[0.860s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'cmake_prefix_path')
[0.860s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.ps1'
[0.861s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.dsv'
[0.861s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.sh'
[0.861s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib'
[0.862s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'ld_library_path_lib')
[0.863s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.ps1'
[0.864s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.dsv'
[0.865s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.sh'
[0.866s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.866s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/pkgconfig/srdfdom.pc'
[0.866s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/python3.10/site-packages'
[0.866s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.867s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.ps1'
[0.868s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv'
[0.868s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh'
[0.869s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.bash'
[0.869s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.zsh'
[0.870s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/srdfdom/share/colcon-core/packages/srdfdom)
[0.871s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(srdfdom)
[0.871s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake module files
[0.871s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom' for CMake config files
[0.871s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'cmake_prefix_path')
[0.872s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.ps1'
[0.872s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.dsv'
[0.874s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/cmake_prefix_path.sh'
[0.875s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib'
[0.875s] Level 1:colcon.colcon_core.shell:create_environment_hook('srdfdom', 'ld_library_path_lib')
[0.876s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.ps1'
[0.877s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.dsv'
[0.877s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/hook/ld_library_path_lib.sh'
[0.878s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.878s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/pkgconfig/srdfdom.pc'
[0.878s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/lib/python3.10/site-packages'
[0.878s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/srdfdom/bin'
[0.878s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.ps1'
[0.879s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv'
[0.879s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh'
[0.879s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.bash'
[0.880s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.zsh'
[0.881s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/srdfdom/share/colcon-core/packages/srdfdom)
[0.920s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config -- -j8 -l8
[0.940s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config -- -j8 -l8
[0.947s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[0.955s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[0.960s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config -- -j8 -l8
[0.965s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config
[0.972s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config -- -j8 -l8
[0.972s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config
[0.980s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_moveit_config)
[0.981s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake module files
[0.982s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake config files
[0.982s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_moveit_config', 'cmake_prefix_path')
[0.982s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.ps1'
[0.983s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config
[0.986s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.dsv'
[0.987s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.sh'
[0.987s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.987s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/pkgconfig/moveit_resources_fanuc_moveit_config.pc'
[0.987s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/python3.10/site-packages'
[0.988s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.988s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.ps1'
[0.988s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv'
[0.989s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh'
[0.990s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.bash'
[0.990s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.zsh'
[0.990s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/colcon-core/packages/moveit_resources_fanuc_moveit_config)
[0.991s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_fanuc_moveit_config)
[0.993s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake module files
[0.994s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config' for CMake config files
[0.994s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_fanuc_moveit_config', 'cmake_prefix_path')
[0.994s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.ps1'
[0.995s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.dsv'
[0.995s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/hook/cmake_prefix_path.sh'
[0.995s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.996s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/pkgconfig/moveit_resources_fanuc_moveit_config.pc'
[0.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/lib/python3.10/site-packages'
[0.997s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/bin'
[0.997s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.ps1'
[0.998s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv'
[0.999s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh'
[1.000s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.bash'
[1.001s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.zsh'
[1.001s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/colcon-core/packages/moveit_resources_fanuc_moveit_config)
[1.003s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_moveit_config)
[1.003s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake module files
[1.004s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake config files
[1.004s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_moveit_config', 'cmake_prefix_path')
[1.004s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.ps1'
[1.006s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config
[1.006s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.dsv'
[1.007s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.sh'
[1.008s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.008s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/pkgconfig/moveit_resources_panda_moveit_config.pc'
[1.008s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/python3.10/site-packages'
[1.008s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.008s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.ps1'
[1.009s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv'
[1.012s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh'
[1.013s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.bash'
[1.014s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.zsh'
[1.017s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/colcon-core/packages/moveit_resources_panda_moveit_config)
[1.019s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_resources_panda_moveit_config)
[1.019s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake module files
[1.019s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config' for CMake config files
[1.019s] Level 1:colcon.colcon_core.shell:create_environment_hook('moveit_resources_panda_moveit_config', 'cmake_prefix_path')
[1.019s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.ps1'
[1.020s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.dsv'
[1.020s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/hook/cmake_prefix_path.sh'
[1.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/pkgconfig/moveit_resources_panda_moveit_config.pc'
[1.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/lib/python3.10/site-packages'
[1.021s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/bin'
[1.021s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.ps1'
[1.022s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv'
[1.023s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh'
[1.023s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.bash'
[1.024s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.zsh'
[1.024s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/colcon-core/packages/moveit_resources_panda_moveit_config)
[1.025s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_core' with build type 'ament_cmake'
[1.025s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit2/moveit_core'
[1.025s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.025s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.031s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources' with build type 'ament_cmake'
[1.032s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_moveit2/src/moveit_resources/moveit_resources'
[1.032s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.032s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.044s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rviz_marker_tools -- -j8 -l8
[1.044s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[1.062s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_core': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/srdfdom/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_core -- -j8 -l8
[1.082s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources -- -j8 -l8
[1.083s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rviz_marker_tools)
[1.083s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake module files
[1.083s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rviz_marker_tools' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rviz_marker_tools
[1.084s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake config files
[1.084s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'cmake_prefix_path')
[1.084s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.ps1'
[1.084s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.dsv'
[1.084s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.sh'
[1.085s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib'
[1.085s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'ld_library_path_lib')
[1.085s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.ps1'
[1.085s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.dsv'
[1.085s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.sh'
[1.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/pkgconfig/rviz_marker_tools.pc'
[1.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/python3.10/site-packages'
[1.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.086s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.ps1'
[1.089s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv'
[1.090s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.sh'
[1.090s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.bash'
[1.090s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.zsh'
[1.091s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/colcon-core/packages/rviz_marker_tools)
[1.091s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rviz_marker_tools)
[1.091s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake module files
[1.091s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools' for CMake config files
[1.092s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'cmake_prefix_path')
[1.092s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.ps1'
[1.092s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.dsv'
[1.092s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/cmake_prefix_path.sh'
[1.092s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib'
[1.092s] Level 1:colcon.colcon_core.shell:create_environment_hook('rviz_marker_tools', 'ld_library_path_lib')
[1.093s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.ps1'
[1.093s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.dsv'
[1.095s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/hook/ld_library_path_lib.sh'
[1.095s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.095s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/pkgconfig/rviz_marker_tools.pc'
[1.095s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/python3.10/site-packages'
[1.095s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/rviz_marker_tools/bin'
[1.095s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.ps1'
[1.096s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv'
[1.097s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.sh'
[1.098s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.bash'
[1.098s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.zsh'
[1.099s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/rviz_marker_tools/share/colcon-core/packages/rviz_marker_tools)
[1.101s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[1.104s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[1.107s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_resources' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_resources -- -j8 -l8
[1.107s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_moveit2/build/moveit_resources': AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/ws_moveit2/build/moveit_resources
[1.110s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(moveit_task_constructor_msgs)
[1.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs' for CMake module files
[1.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs' for CMake config files
[1.111s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[1.112s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/bin'
[1.112s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/lib/pkgconfig/moveit_task_constructor_msgs.pc'
[1.115s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/lib/python3.10/site-packages'
[1.115s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/bin'
[1.115s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.ps1'
[1.117s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.dsv'
[1.117s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.sh'
[1.118s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.bash'
[1.119s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/moveit_task_constructor_msgs/package.zsh'
[1.119s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs/share/colcon-core/packages/moveit_task_constructor_msgs)
[1.422s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.423s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.423s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[1.423s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.430s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.430s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.430s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.454s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.454s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.ps1'
[1.455s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_moveit2/install/_local_setup_util_ps1.py'
[1.457s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.ps1'
[1.457s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.sh'
[1.458s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_moveit2/install/_local_setup_util_sh.py'
[1.458s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.sh'
[1.459s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.bash'
[1.460s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.bash'
[1.460s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_moveit2/install/local_setup.zsh'
[1.461s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_moveit2/install/setup.zsh'
