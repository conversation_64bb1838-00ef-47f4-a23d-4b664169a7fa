[0.000000] (-) TimerEvent: {}
[0.000791] (launch_param_builder) JobQueued: {'identifier': 'launch_param_builder', 'dependencies': OrderedDict()}
[0.000820] (moveit_common) JobQueued: {'identifier': 'moveit_common', 'dependencies': OrderedDict()}
[0.000831] (moveit_resources_fanuc_description) JobQueued: {'identifier': 'moveit_resources_fanuc_description', 'dependencies': OrderedDict()}
[0.000841] (moveit_resources_panda_description) JobQueued: {'identifier': 'moveit_resources_panda_description', 'dependencies': OrderedDict()}
[0.000855] (moveit_resources_pr2_description) JobQueued: {'identifier': 'moveit_resources_pr2_description', 'dependencies': OrderedDict()}
[0.000864] (moveit_resources_prbt_support) JobQueued: {'identifier': 'moveit_resources_prbt_support', 'dependencies': OrderedDict()}
[0.000878] (moveit_task_constructor_msgs) JobQueued: {'identifier': 'moveit_task_constructor_msgs', 'dependencies': OrderedDict()}
[0.000886] (rosparam_shortcuts) JobQueued: {'identifier': 'rosparam_shortcuts', 'dependencies': OrderedDict()}
[0.000894] (srdfdom) JobQueued: {'identifier': 'srdfdom', 'dependencies': OrderedDict()}
[0.000940] (moveit_configs_utils) JobQueued: {'identifier': 'moveit_configs_utils', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom')])}
[0.000971] (moveit_resources_fanuc_moveit_config) JobQueued: {'identifier': 'moveit_resources_fanuc_moveit_config', 'dependencies': OrderedDict([('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description')])}
[0.000989] (moveit_resources_panda_moveit_config) JobQueued: {'identifier': 'moveit_resources_panda_moveit_config', 'dependencies': OrderedDict([('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description')])}
[0.001005] (rviz_marker_tools) JobQueued: {'identifier': 'rviz_marker_tools', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common')])}
[0.001018] (moveit_core) JobQueued: {'identifier': 'moveit_core', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config')])}
[0.001202] (moveit_resources) JobQueued: {'identifier': 'moveit_resources', 'dependencies': OrderedDict([('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config')])}
[0.001235] (chomp_motion_planner) JobQueued: {'identifier': 'chomp_motion_planner', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core')])}
[0.001269] (moveit_resources_prbt_ikfast_manipulator_plugin) JobQueued: {'identifier': 'moveit_resources_prbt_ikfast_manipulator_plugin', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core')])}
[0.001290] (moveit_ros_occupancy_map_monitor) JobQueued: {'identifier': 'moveit_ros_occupancy_map_monitor', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core')])}
[0.001314] (moveit_simple_controller_manager) JobQueued: {'identifier': 'moveit_simple_controller_manager', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core')])}
[0.001335] (pilz_industrial_motion_planner_testutils) JobQueued: {'identifier': 'pilz_industrial_motion_planner_testutils', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core')])}
[0.001355] (moveit_chomp_optimizer_adapter) JobQueued: {'identifier': 'moveit_chomp_optimizer_adapter', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner')])}
[0.001373] (moveit_planners_chomp) JobQueued: {'identifier': 'moveit_planners_chomp', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner')])}
[0.001391] (moveit_plugins) JobQueued: {'identifier': 'moveit_plugins', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager')])}
[0.001412] (moveit_ros_control_interface) JobQueued: {'identifier': 'moveit_ros_control_interface', 'dependencies': OrderedDict([('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager')])}
[0.001432] (moveit_ros_planning) JobQueued: {'identifier': 'moveit_ros_planning', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor')])}
[0.001476] (moveit_kinematics) JobQueued: {'identifier': 'moveit_kinematics', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning')])}
[0.001495] (moveit_planners_ompl) JobQueued: {'identifier': 'moveit_planners_ompl', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning')])}
[0.001526] (moveit_ros_perception) JobQueued: {'identifier': 'moveit_ros_perception', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning')])}
[0.001550] (moveit_ros_robot_interaction) JobQueued: {'identifier': 'moveit_ros_robot_interaction', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning')])}
[0.001581] (moveit_ros_warehouse) JobQueued: {'identifier': 'moveit_ros_warehouse', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning')])}
[0.001599] (moveit_visual_tools) JobQueued: {'identifier': 'moveit_visual_tools', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning')])}
[0.001676] (moveit_ros_benchmarks) JobQueued: {'identifier': 'moveit_ros_benchmarks', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse')])}
[0.001895] (moveit_ros_move_group) JobQueued: {'identifier': 'moveit_ros_move_group', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics')])}
[0.001927] (moveit_resources_prbt_moveit_config) JobQueued: {'identifier': 'moveit_resources_prbt_moveit_config', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group')])}
[0.001945] (moveit_ros_planning_interface) JobQueued: {'identifier': 'moveit_ros_planning_interface', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group')])}
[0.001973] (moveit_hybrid_planning) JobQueued: {'identifier': 'moveit_hybrid_planning', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface')])}
[0.002036] (moveit_resources_prbt_pg70_support) JobQueued: {'identifier': 'moveit_resources_prbt_pg70_support', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config')])}
[0.002090] (moveit_ros_visualization) JobQueued: {'identifier': 'moveit_ros_visualization', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface')])}
[0.002110] (moveit_servo) JobQueued: {'identifier': 'moveit_servo', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface')])}
[0.002141] (moveit_ros) JobQueued: {'identifier': 'moveit_ros', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_benchmarks', '/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization')])}
[0.002189] (moveit_setup_framework) JobQueued: {'identifier': 'moveit_setup_framework', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization')])}
[0.002230] (pilz_industrial_motion_planner) JobQueued: {'identifier': 'pilz_industrial_motion_planner', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support')])}
[0.002305] (moveit_planners) JobQueued: {'identifier': 'moveit_planners', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner')])}
[0.002480] (moveit_setup_app_plugins) JobQueued: {'identifier': 'moveit_setup_app_plugins', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework')])}
[0.002538] (moveit_setup_controllers) JobQueued: {'identifier': 'moveit_setup_controllers', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework')])}
[0.002606] (moveit_setup_core_plugins) JobQueued: {'identifier': 'moveit_setup_core_plugins', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework')])}
[0.002667] (moveit_setup_srdf_plugins) JobQueued: {'identifier': 'moveit_setup_srdf_plugins', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework')])}
[0.002731] (moveit_runtime) JobQueued: {'identifier': 'moveit_runtime', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_plugins', '/home/<USER>/ws_moveit2/install/moveit_plugins'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_perception', '/home/<USER>/ws_moveit2/install/moveit_ros_perception'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners')])}
[0.002777] (moveit_setup_assistant) JobQueued: {'identifier': 'moveit_setup_assistant', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework'), ('moveit_setup_app_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins'), ('moveit_setup_controllers', '/home/<USER>/ws_moveit2/install/moveit_setup_controllers'), ('moveit_setup_core_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins'), ('moveit_setup_srdf_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins')])}
[0.002880] (moveit_task_constructor_core) JobQueued: {'identifier': 'moveit_task_constructor_core', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('moveit_task_constructor_msgs', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('rviz_marker_tools', '/home/<USER>/ws_moveit2/install/rviz_marker_tools'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners')])}
[0.002986] (moveit) JobQueued: {'identifier': 'moveit', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_plugins', '/home/<USER>/ws_moveit2/install/moveit_plugins'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_benchmarks', '/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_ros', '/home/<USER>/ws_moveit2/install/moveit_ros'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners'), ('moveit_setup_app_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins'), ('moveit_setup_controllers', '/home/<USER>/ws_moveit2/install/moveit_setup_controllers'), ('moveit_setup_core_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins'), ('moveit_setup_srdf_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins'), ('moveit_setup_assistant', '/home/<USER>/ws_moveit2/install/moveit_setup_assistant')])}
[0.003080] (moveit_task_constructor_capabilities) JobQueued: {'identifier': 'moveit_task_constructor_capabilities', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('moveit_task_constructor_msgs', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('rviz_marker_tools', '/home/<USER>/ws_moveit2/install/rviz_marker_tools'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners'), ('moveit_task_constructor_core', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core')])}
[0.003162] (moveit_task_constructor_visualization) JobQueued: {'identifier': 'moveit_task_constructor_visualization', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('moveit_task_constructor_msgs', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('rviz_marker_tools', '/home/<USER>/ws_moveit2/install/rviz_marker_tools'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners'), ('moveit_task_constructor_core', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core')])}
[0.003199] (moveit2_tutorials) JobQueued: {'identifier': 'moveit2_tutorials', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('moveit_task_constructor_msgs', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('rviz_marker_tools', '/home/<USER>/ws_moveit2/install/rviz_marker_tools'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_plugins', '/home/<USER>/ws_moveit2/install/moveit_plugins'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_perception', '/home/<USER>/ws_moveit2/install/moveit_ros_perception'), ('moveit_ros_robot_interaction', '/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_visual_tools', '/home/<USER>/ws_moveit2/install/moveit_visual_tools'), ('moveit_ros_benchmarks', '/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_hybrid_planning', '/home/<USER>/ws_moveit2/install/moveit_hybrid_planning'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('moveit_ros_visualization', '/home/<USER>/ws_moveit2/install/moveit_ros_visualization'), ('moveit_servo', '/home/<USER>/ws_moveit2/install/moveit_servo'), ('moveit_ros', '/home/<USER>/ws_moveit2/install/moveit_ros'), ('moveit_setup_framework', '/home/<USER>/ws_moveit2/install/moveit_setup_framework'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners'), ('moveit_setup_app_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins'), ('moveit_setup_controllers', '/home/<USER>/ws_moveit2/install/moveit_setup_controllers'), ('moveit_setup_core_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins'), ('moveit_setup_srdf_plugins', '/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins'), ('moveit_setup_assistant', '/home/<USER>/ws_moveit2/install/moveit_setup_assistant'), ('moveit_task_constructor_core', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core'), ('moveit', '/home/<USER>/ws_moveit2/install/moveit')])}
[0.003250] (moveit_task_constructor_demo) JobQueued: {'identifier': 'moveit_task_constructor_demo', 'dependencies': OrderedDict([('launch_param_builder', '/home/<USER>/ws_moveit2/install/launch_param_builder'), ('moveit_common', '/home/<USER>/ws_moveit2/install/moveit_common'), ('moveit_resources_fanuc_description', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description'), ('moveit_resources_panda_description', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description'), ('moveit_resources_pr2_description', '/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description'), ('moveit_resources_prbt_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support'), ('moveit_task_constructor_msgs', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'), ('srdfdom', '/home/<USER>/ws_moveit2/install/srdfdom'), ('moveit_configs_utils', '/home/<USER>/ws_moveit2/install/moveit_configs_utils'), ('moveit_resources_fanuc_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config'), ('moveit_resources_panda_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config'), ('rviz_marker_tools', '/home/<USER>/ws_moveit2/install/rviz_marker_tools'), ('moveit_core', '/home/<USER>/ws_moveit2/install/moveit_core'), ('chomp_motion_planner', '/home/<USER>/ws_moveit2/install/chomp_motion_planner'), ('moveit_resources_prbt_ikfast_manipulator_plugin', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin'), ('moveit_ros_occupancy_map_monitor', '/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor'), ('moveit_simple_controller_manager', '/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager'), ('pilz_industrial_motion_planner_testutils', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils'), ('moveit_planners_chomp', '/home/<USER>/ws_moveit2/install/moveit_planners_chomp'), ('moveit_ros_planning', '/home/<USER>/ws_moveit2/install/moveit_ros_planning'), ('moveit_kinematics', '/home/<USER>/ws_moveit2/install/moveit_kinematics'), ('moveit_planners_ompl', '/home/<USER>/ws_moveit2/install/moveit_planners_ompl'), ('moveit_ros_warehouse', '/home/<USER>/ws_moveit2/install/moveit_ros_warehouse'), ('moveit_ros_move_group', '/home/<USER>/ws_moveit2/install/moveit_ros_move_group'), ('moveit_resources_prbt_moveit_config', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config'), ('moveit_ros_planning_interface', '/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface'), ('moveit_resources_prbt_pg70_support', '/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support'), ('pilz_industrial_motion_planner', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner'), ('moveit_planners', '/home/<USER>/ws_moveit2/install/moveit_planners'), ('moveit_task_constructor_core', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_core'), ('moveit_task_constructor_capabilities', '/home/<USER>/ws_moveit2/install/moveit_task_constructor_capabilities')])}
[0.003278] (moveit_resources_panda_description) JobStarted: {'identifier': 'moveit_resources_panda_description'}
[0.007195] (moveit_common) JobStarted: {'identifier': 'moveit_common'}
[0.008627] (moveit_resources_pr2_description) JobStarted: {'identifier': 'moveit_resources_pr2_description'}
[0.010853] (srdfdom) JobStarted: {'identifier': 'srdfdom'}
[0.012746] (launch_param_builder) JobStarted: {'identifier': 'launch_param_builder'}
[0.018657] (moveit_resources_fanuc_description) JobStarted: {'identifier': 'moveit_resources_fanuc_description'}
[0.020620] (moveit_resources_prbt_support) JobStarted: {'identifier': 'moveit_resources_prbt_support'}
[0.022609] (moveit_task_constructor_msgs) JobStarted: {'identifier': 'moveit_task_constructor_msgs'}
[0.026534] (moveit_resources_panda_description) JobProgress: {'identifier': 'moveit_resources_panda_description', 'progress': 'cmake'}
[0.028355] (moveit_resources_panda_description) JobProgress: {'identifier': 'moveit_resources_panda_description', 'progress': 'build'}
[0.028498] (moveit_resources_panda_description) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.030550] (moveit_common) JobProgress: {'identifier': 'moveit_common', 'progress': 'cmake'}
[0.032655] (moveit_common) JobProgress: {'identifier': 'moveit_common', 'progress': 'build'}
[0.032707] (moveit_common) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_common', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_common', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_common'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.033834] (moveit_resources_pr2_description) JobProgress: {'identifier': 'moveit_resources_pr2_description', 'progress': 'cmake'}
[0.037268] (moveit_resources_pr2_description) JobProgress: {'identifier': 'moveit_resources_pr2_description', 'progress': 'build'}
[0.037303] (moveit_resources_pr2_description) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.037696] (srdfdom) JobProgress: {'identifier': 'srdfdom', 'progress': 'cmake'}
[0.039589] (srdfdom) JobProgress: {'identifier': 'srdfdom', 'progress': 'build'}
[0.039789] (srdfdom) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/srdfdom', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/srdfdom', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/srdfdom'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.099778] (-) TimerEvent: {}
[0.189346] (moveit_resources_fanuc_description) JobProgress: {'identifier': 'moveit_resources_fanuc_description', 'progress': 'cmake'}
[0.190405] (moveit_resources_fanuc_description) JobProgress: {'identifier': 'moveit_resources_fanuc_description', 'progress': 'build'}
[0.191284] (moveit_resources_fanuc_description) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.192232] (moveit_resources_prbt_support) JobProgress: {'identifier': 'moveit_resources_prbt_support', 'progress': 'cmake'}
[0.194676] (moveit_resources_prbt_support) JobProgress: {'identifier': 'moveit_resources_prbt_support', 'progress': 'build'}
[0.194715] (moveit_resources_prbt_support) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.196455] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'cmake'}
[0.197028] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'build'}
[0.197192] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.200050] (-) TimerEvent: {}
[0.201485] (srdfdom) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_srdfdom\n'}
[0.201686] (srdfdom) StdoutLine: {'line': b'[ 18%] Built target gtest\n'}
[0.201741] (srdfdom) StdoutLine: {'line': b'[ 36%] Built target gtest_main\n'}
[0.201773] (srdfdom) StdoutLine: {'line': b'[ 63%] Built target srdfdom\n'}
[0.201801] (srdfdom) StdoutLine: {'line': b'[ 81%] Built target test_cpp_nl_NL.UTF-8\n'}
[0.201828] (srdfdom) StdoutLine: {'line': b'[100%] Built target test_cpp_C\n'}
[0.201856] (srdfdom) StdoutLine: {'line': b'running egg_info\n'}
[0.201884] (srdfdom) StdoutLine: {'line': b'writing srdfdom.egg-info/PKG-INFO\n'}
[0.201910] (srdfdom) StdoutLine: {'line': b'writing dependency_links to srdfdom.egg-info/dependency_links.txt\n'}
[0.201936] (srdfdom) StdoutLine: {'line': b'writing top-level names to srdfdom.egg-info/top_level.txt\n'}
[0.201961] (srdfdom) StdoutLine: {'line': b"reading manifest file 'srdfdom.egg-info/SOURCES.txt'\n"}
[0.201987] (srdfdom) StdoutLine: {'line': b"writing manifest file 'srdfdom.egg-info/SOURCES.txt'\n"}
[0.208632] (moveit_resources_panda_description) CommandEnded: {'returncode': 0}
[0.208884] (moveit_resources_panda_description) JobProgress: {'identifier': 'moveit_resources_panda_description', 'progress': 'install'}
[0.208900] (srdfdom) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_srdfdom_egg\n'}
[0.211701] (moveit_resources_panda_description) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_description'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.214525] (moveit_resources_fanuc_description) CommandEnded: {'returncode': 0}
[0.214908] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.214980] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/ament_index/resource_index/package_run_dependencies/moveit_resources_panda_description\n'}
[0.215025] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/ament_index/resource_index/parent_prefix_path/moveit_resources_panda_description\n'}
[0.215077] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/environment/ament_prefix_path.sh\n'}
[0.215106] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/environment/ament_prefix_path.dsv\n'}
[0.215146] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/environment/path.sh\n'}
[0.215181] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/environment/path.dsv\n'}
[0.215217] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/local_setup.bash\n'}
[0.215252] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/local_setup.sh\n'}
[0.215279] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/local_setup.zsh\n'}
[0.215304] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/local_setup.dsv\n'}
[0.215330] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.dsv\n'}
[0.215356] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/ament_index/resource_index/packages/moveit_resources_panda_description\n'}
[0.215382] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/cmake/moveit_resources_panda_descriptionConfig.cmake\n'}
[0.215408] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/cmake/moveit_resources_panda_descriptionConfig-version.cmake\n'}
[0.215434] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.xml\n'}
[0.215459] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes\n'}
[0.215485] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision\n'}
[0.215510] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/finger.stl\n'}
[0.215536] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/hand.stl\n'}
[0.215561] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link0.stl\n'}
[0.215587] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link1.stl\n'}
[0.215612] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link2.stl\n'}
[0.215639] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link3.stl\n'}
[0.215666] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link4.stl\n'}
[0.215692] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link5.stl\n'}
[0.215730] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link6.stl\n'}
[0.215776] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/collision/link7.stl\n'}
[0.215833] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual\n'}
[0.215864] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/finger.dae\n'}
[0.215891] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/hand.dae\n'}
[0.215918] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link0.dae\n'}
[0.215944] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link1.dae\n'}
[0.215969] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link2.dae\n'}
[0.216072] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link3.dae\n'}
[0.216256] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link4.dae\n'}
[0.216308] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link5.dae\n'}
[0.217050] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link6.dae\n'}
[0.217131] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/meshes/visual/link7.dae\n'}
[0.219485] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/urdf\n'}
[0.219519] (moveit_resources_panda_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/urdf/panda.urdf\n'}
[0.219548] (moveit_resources_fanuc_description) JobProgress: {'identifier': 'moveit_resources_fanuc_description', 'progress': 'install'}
[0.219560] (moveit_resources_fanuc_description) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_description'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.222626] (moveit_resources_prbt_support) CommandEnded: {'returncode': 0}
[0.222839] (moveit_resources_prbt_support) JobProgress: {'identifier': 'moveit_resources_prbt_support', 'progress': 'install'}
[0.222856] (moveit_resources_prbt_support) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_prbt_support'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.230404] (moveit_common) CommandEnded: {'returncode': 0}
[0.235528] (moveit_common) JobProgress: {'identifier': 'moveit_common', 'progress': 'install'}
[0.238596] (moveit_common) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_common'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_common', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_common'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.239600] (moveit_resources_pr2_description) CommandEnded: {'returncode': 0}
[0.240444] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  0%] Built target moveit_task_constructor_msgs__cpp\n'}
[0.243691] (moveit_resources_pr2_description) JobProgress: {'identifier': 'moveit_resources_pr2_description', 'progress': 'install'}
[0.244293] (moveit_resources_pr2_description) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_pr2_description'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.246345] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs\n'}
[0.257333] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.257427] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/ament_index/resource_index/package_run_dependencies/moveit_resources_prbt_support\n'}
[0.257466] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/ament_index/resource_index/parent_prefix_path/moveit_resources_prbt_support\n'}
[0.257497] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/environment/ament_prefix_path.sh\n'}
[0.257525] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/environment/ament_prefix_path.dsv\n'}
[0.257552] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/environment/path.sh\n'}
[0.257578] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/environment/path.dsv\n'}
[0.257604] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/local_setup.bash\n'}
[0.257630] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/local_setup.sh\n'}
[0.257656] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/local_setup.zsh\n'}
[0.257681] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/local_setup.dsv\n'}
[0.257707] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.dsv\n'}
[0.257733] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/ament_index/resource_index/packages/moveit_resources_prbt_support\n'}
[0.257758] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/cmake/moveit_resources_prbt_supportConfig.cmake\n'}
[0.257784] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/cmake/moveit_resources_prbt_supportConfig-version.cmake\n'}
[0.257810] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.xml\n'}
[0.257835] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/urdf\n'}
[0.257862] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/urdf/prbt.ros2_control.xacro\n'}
[0.257887] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/urdf/prbt.xacro\n'}
[0.257912] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/urdf/prbt_macro.xacro\n'}
[0.257938] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/urdf/simple_gripper_brackets.urdf.xacro\n'}
[0.257964] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes\n'}
[0.257992] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/flange.dae\n'}
[0.258017] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/flange.stl\n'}
[0.258042] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/foot.dae\n'}
[0.258070] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/foot.stl\n'}
[0.258097] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_1.dae\n'}
[0.258123] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_1.stl\n'}
[0.258148] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_2.dae\n'}
[0.258174] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_2.stl\n'}
[0.258199] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_3.dae\n'}
[0.258235] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_3.stl\n'}
[0.258275] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_4.dae\n'}
[0.258316] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_4.stl\n'}
[0.258342] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_5.dae\n'}
[0.258619] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/meshes/link_5.stl\n'}
[0.258725] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/config\n'}
[0.258759] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/config/manipulator_controller.yaml\n'}
[0.258789] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/config/manipulator_driver.yaml\n'}
[0.258816] (moveit_resources_prbt_support) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/config/prbt_0_1.dcf\n'}
[0.258842] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.258933] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/ament_index/resource_index/package_run_dependencies/moveit_resources_fanuc_description\n'}
[0.259122] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/ament_index/resource_index/parent_prefix_path/moveit_resources_fanuc_description\n'}
[0.259166] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/environment/ament_prefix_path.sh\n'}
[0.259195] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/environment/ament_prefix_path.dsv\n'}
[0.259221] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/environment/path.sh\n'}
[0.259258] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/environment/path.dsv\n'}
[0.259284] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/local_setup.bash\n'}
[0.259310] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/local_setup.sh\n'}
[0.259335] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/local_setup.zsh\n'}
[0.259361] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/local_setup.dsv\n'}
[0.259386] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.dsv\n'}
[0.259412] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/ament_index/resource_index/packages/moveit_resources_fanuc_description\n'}
[0.259437] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/cmake/moveit_resources_fanuc_descriptionConfig.cmake\n'}
[0.259463] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/cmake/moveit_resources_fanuc_descriptionConfig-version.cmake\n'}
[0.259494] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.xml\n'}
[0.259521] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes\n'}
[0.259547] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision\n'}
[0.259578] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/base_link.stl\n'}
[0.259624] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/link_1.stl\n'}
[0.259663] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/link_2.stl\n'}
[0.259691] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/link_3.stl\n'}
[0.259718] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/link_4.stl\n'}
[0.259745] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/link_5.stl\n'}
[0.259773] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/collision/link_6.stl\n'}
[0.259798] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual\n'}
[0.259825] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/base_link.stl\n'}
[0.259852] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/link_1.stl\n'}
[0.259878] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/link_2.stl\n'}
[0.259906] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/link_3.stl\n'}
[0.259933] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/link_4.stl\n'}
[0.259959] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/link_5.stl\n'}
[0.259985] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/meshes/visual/link_6.stl\n'}
[0.260010] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/urdf\n'}
[0.260036] (moveit_resources_fanuc_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/urdf/fanuc.urdf\n'}
[0.260062] (moveit_common) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.260100] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/ament_index/resource_index/package_run_dependencies/moveit_common\n'}
[0.260133] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/ament_index/resource_index/parent_prefix_path/moveit_common\n'}
[0.260162] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/ament_prefix_path.sh\n'}
[0.260188] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/ament_prefix_path.dsv\n'}
[0.263364] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/path.sh\n'}
[0.263418] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/environment/path.dsv\n'}
[0.263451] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.bash\n'}
[0.263479] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.sh\n'}
[0.263505] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.zsh\n'}
[0.263531] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/local_setup.dsv\n'}
[0.263557] (moveit_common) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.dsv\n'}
[0.263582] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/ament_index/resource_index/packages/moveit_common\n'}
[0.263609] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_common-extras.cmake\n'}
[0.263635] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_commonConfig.cmake\n'}
[0.263660] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_commonConfig-version.cmake\n'}
[0.263686] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.xml\n'}
[0.263711] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake\n'}
[0.263737] (moveit_common) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_package.cmake\n'}
[0.263763] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.263797] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/ament_index/resource_index/package_run_dependencies/moveit_resources_pr2_description\n'}
[0.263828] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/ament_index/resource_index/parent_prefix_path/moveit_resources_pr2_description\n'}
[0.263853] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/environment/ament_prefix_path.sh\n'}
[0.263879] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/environment/ament_prefix_path.dsv\n'}
[0.263905] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/environment/path.sh\n'}
[0.263931] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/environment/path.dsv\n'}
[0.269633] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/local_setup.bash\n'}
[0.269722] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/local_setup.sh\n'}
[0.269767] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/local_setup.zsh\n'}
[0.271011] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/local_setup.dsv\n'}
[0.271135] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.dsv\n'}
[0.271170] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/ament_index/resource_index/packages/moveit_resources_pr2_description\n'}
[0.271220] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/cmake/moveit_resources_pr2_descriptionConfig.cmake\n'}
[0.271259] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/cmake/moveit_resources_pr2_descriptionConfig-version.cmake\n'}
[0.271289] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.xml\n'}
[0.271354] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/srdf\n'}
[0.271406] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/srdf/robot.xml\n'}
[0.273578] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf\n'}
[0.273732] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/materials\n'}
[0.273774] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/materials/textures\n'}
[0.273808] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/materials/textures/pr2_caster_texture.png\n'}
[0.273838] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/materials/textures/pr2_wheel_left.png\n'}
[0.273864] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/materials/textures/pr2_wheel_right.png\n'}
[0.273893] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes\n'}
[0.274029] (srdfdom) CommandEnded: {'returncode': 0}
[0.274172] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0\n'}
[0.274210] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/base.dae\n'}
[0.274240] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/base.stl\n'}
[0.274289] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/base_L.stl\n'}
[0.274357] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/base_color.tif\n'}
[0.275967] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/base_normals.tif\n'}
[0.276023] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/caster.stl\n'}
[0.276065] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/caster_L.stl\n'}
[0.276103] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex\n'}
[0.276146] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base.dae\n'}
[0.276185] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base.xml\n'}
[0.276226] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base_L.dae\n'}
[0.276288] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base_L.xml\n'}
[0.276335] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base_L_convex.stla\n'}
[0.276377] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base_L_convex.stlb\n'}
[0.276416] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base_convex.stla\n'}
[0.276459] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/base_convex.stlb\n'}
[0.276503] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster.dae\n'}
[0.276544] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster.xml\n'}
[0.276581] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster_L.dae\n'}
[0.276626] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster_L.xml\n'}
[0.276671] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster_L_convex.stla\n'}
[0.276725] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster_L_convex.stlb\n'}
[0.276771] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster_convex.stla\n'}
[0.276820] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/caster_convex.stlb\n'}
[0.276887] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/pr2_wheel.dae\n'}
[0.276952] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/pr2_wheel.xml\n'}
[0.277011] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/pr2_wheel_convex.stla\n'}
[0.277057] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/pr2_wheel_convex.stlb\n'}
[0.277116] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/wheel.dae\n'}
[0.277260] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/wheel.xml\n'}
[0.277328] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/wheel_convex.stla\n'}
[0.277382] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/convex/wheel_convex.stlb\n'}
[0.277430] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/pr2_wheel.stl\n'}
[0.277479] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/wheel.dae\n'}
[0.277522] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/wheel.stl\n'}
[0.277564] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/wheel_color.tif\n'}
[0.277622] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/wheel_h.dae\n'}
[0.277685] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/wheel_h_color.tif\n'}
[0.277741] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/base_v0/wheel_normals.tif\n'}
[0.277796] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0\n'}
[0.277841] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex\n'}
[0.277885] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/forearm.dae\n'}
[0.277924] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/forearm.xml\n'}
[0.277968] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/forearm_convex.stla\n'}
[0.278020] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/forearm_convex.stlb\n'}
[0.278073] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_flex.dae\n'}
[0.278110] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_flex.xml\n'}
[0.278293] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_flex_convex.stla\n'}
[0.278332] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_flex_convex.stlb\n'}
[0.278370] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll.dae\n'}
[0.278405] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll.xml\n'}
[0.278441] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll_L.dae\n'}
[0.278480] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll_L.xml\n'}
[0.278517] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll_L_convex.stla\n'}
[0.278556] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll_L_convex.stlb\n'}
[0.278594] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll_convex.stla\n'}
[0.278640] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/convex/wrist_roll_convex.stlb\n'}
[0.278678] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/forearm.dae\n'}
[0.278715] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/forearm.jpg\n'}
[0.278751] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/forearm.stl\n'}
[0.278786] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/forearm_color.tif\n'}
[0.278823] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/forearm_normals.tif\n'}
[0.278860] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/wrist_color.tif\n'}
[0.278896] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/wrist_flex.dae\n'}
[0.278932] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/wrist_flex.stl\n'}
[0.278967] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/wrist_normals.tif\n'}
[0.279005] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/wrist_roll.stl\n'}
[0.279042] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/forearm_v0/wrist_roll_L.stl\n'}
[0.279079] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0\n'}
[0.279116] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex\n'}
[0.279154] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_l.dae\n'}
[0.279192] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_l.xml\n'}
[0.279235] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_l_convex.stla\n'}
[0.279282] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_l_convex.stlb\n'}
[0.279321] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_l.dae\n'}
[0.279359] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_l.xml\n'}
[0.279394] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_l_convex.stla\n'}
[0.279430] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_l_convex.stlb\n'}
[0.279468] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_r.dae\n'}
[0.279504] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_r.xml\n'}
[0.279542] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_r_convex.stla\n'}
[0.279583] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_pad2_r_convex.stlb\n'}
[0.279621] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_r.dae\n'}
[0.279659] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_r.xml\n'}
[0.279697] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_r_convex.stla\n'}
[0.279731] (srdfdom) JobProgress: {'identifier': 'srdfdom', 'progress': 'install'}
[0.279747] (srdfdom) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/srdfdom'], 'cwd': '/home/<USER>/ws_moveit2/build/srdfdom', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/srdfdom'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.279912] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/finger_tip_r_convex.stlb\n'}
[0.279958] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/gripper_palm.dae\n'}
[0.279992] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/gripper_palm.xml\n'}
[0.280030] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/gripper_palm_convex.stla\n'}
[0.280066] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/gripper_palm_convex.stlb\n'}
[0.280102] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger.dae\n'}
[0.280140] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger.xml\n'}
[0.280174] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger_convex.stla\n'}
[0.280209] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger_convex.stlb\n'}
[0.280250] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger_tip.dae\n'}
[0.280296] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger_tip.xml\n'}
[0.280344] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger_tip_convex.stla\n'}
[0.280388] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_finger_tip_convex.stlb\n'}
[0.280432] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_floating.dae\n'}
[0.280469] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_floating.xml\n'}
[0.280512] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_floating_convex.stla\n'}
[0.280553] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/l_floating_convex.stlb\n'}
[0.280591] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_l.dae\n'}
[0.280634] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_l.xml\n'}
[0.280673] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_l_convex.stla\n'}
[0.280708] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_l_convex.stlb\n'}
[0.280757] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_r.dae\n'}
[0.280798] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_r.xml\n'}
[0.280839] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_r_convex.stla\n'}
[0.280879] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 12%] Built target moveit_task_constructor_msgs__rosidl_generator_c\n'}
[0.280929] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/convex/upper_finger_r_convex.stlb\n'}
[0.280990] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_H_Color_100430.tif\n'}
[0.281035] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_H_UV_100430.dae\n'}
[0.281079] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_tip_H_UV_100430.dae\n'}
[0.281169] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_tip_l.stl\n'}
[0.281212] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_tip_pad2_l.stl\n'}
[0.281262] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_tip_pad2_r.stl\n'}
[0.281302] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/finger_tip_r.stl\n'}
[0.281341] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/fingertip_H_Color_100430.tif\n'}
[0.281378] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/float_H_Color_100430.tif\n'}
[0.281418] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/float_H_UV_100430.dae\n'}
[0.282475] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/gripper_palm.dae\n'}
[0.282574] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/gripper_palm.stl\n'}
[0.282605] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/gripper_palm_color.tif\n'}
[0.283347] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/gripper_palm_normals.tif\n'}
[0.283407] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger.dae\n'}
[0.283444] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger.stl\n'}
[0.283483] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger_color.tif\n'}
[0.283520] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger_normals.tif\n'}
[0.283562] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger_tip.dae\n'}
[0.283605] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger_tip.stl\n'}
[0.283661] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger_tip_color.tif\n'}
[0.286443] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_finger_tip_normals.tif\n'}
[0.286484] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_float.dae\n'}
[0.286525] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_float_color.tif\n'}
[0.286553] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_float_normals.tif\n'}
[0.286579] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/l_floating.stl\n'}
[0.286605] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/palm_H_Color_100430.tif\n'}
[0.286630] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/palm_H_UV_100430.dae\n'}
[0.286657] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/upper_finger_l.stl\n'}
[0.286684] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/gripper_v0/upper_finger_r.stl\n'}
[0.286710] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0\n'}
[0.286737] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex\n'}
[0.286763] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan.dae\n'}
[0.286790] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan.xml\n'}
[0.286818] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan_L.dae\n'}
[0.286844] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan_L.xml\n'}
[0.286869] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan_L_convex.stla\n'}
[0.286896] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan_L_convex.stlb\n'}
[0.286942] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan_convex.stla\n'}
[0.286973] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_pan_convex.stlb\n'}
[0.286998] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt.dae\n'}
[0.287024] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt.xml\n'}
[0.287060] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt_L.dae\n'}
[0.287088] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt_L.xml\n'}
[0.287113] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt_L_convex.stla\n'}
[0.287139] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt_L_convex.stlb\n'}
[0.287175] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt_convex.stla\n'}
[0.287205] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/convex/head_tilt_convex.stlb\n'}
[0.287232] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_pan.dae\n'}
[0.287346] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_pan.stl\n'}
[0.287411] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_pan_L.stl\n'}
[0.287457] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_pan_color.tif\n'}
[0.287509] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_pan_normals.tif\n'}
[0.287554] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt.dae\n'}
[0.289628] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt.stl\n'}
[0.289677] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt_L.stl\n'}
[0.289711] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt_color.tif\n'}
[0.289743] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt_color_red.tif\n'}
[0.289773] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt_color_yellow.tif\n'}
[0.289802] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt_green.tif\n'}
[0.289829] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/head_v0/head_tilt_normals.tif\n'}
[0.289857] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors\n'}
[0.289894] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0\n'}
[0.289922] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0/115x100_swept_back--coarse.STL\n'}
[0.289950] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0/115x100_swept_back_no_sensors--coarse.STL\n'}
[0.289978] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0/115x100_swept_fwd--coarse.STL\n'}
[0.290005] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0/115x100_swept_fwd_no_sensors--coarse.STL\n'}
[0.290033] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0/68-04546_Kinect_Sensor--coarse.STL\n'}
[0.290059] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_prosilica_v0/Prosilica_w_Lens--coarse.STL\n'}
[0.290084] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_v0\n'}
[0.290246] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_v0/kinect.dae\n'}
[0.290274] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_v0/kinect.tga\n'}
[0.290302] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_v0/kinect_color.tga\n'}
[0.291492] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/sensors/kinect_v0/kinect_mount.stl\n'}
[0.291553] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0\n'}
[0.291593] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex\n'}
[0.291633] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_lift.dae\n'}
[0.291669] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_lift.xml\n'}
[0.291705] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_lift_convex.stla\n'}
[0.291744] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_lift_convex.stlb\n'}
[0.291771] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_pan.dae\n'}
[0.291800] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_pan.xml\n'}
[0.292038] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_pan_convex.stla\n'}
[0.292069] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_pan_convex.stlb\n'}
[0.292095] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_yaw.dae\n'}
[0.292121] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_yaw.xml\n'}
[0.292146] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_yaw_convex.stla\n'}
[0.292171] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/shoulder_yaw_convex.stlb\n'}
[0.292196] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll.dae\n'}
[0.292222] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll.xml\n'}
[0.292255] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll_L.dae\n'}
[0.292282] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll_L.xml\n'}
[0.292308] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll_L_convex.stla\n'}
[0.292333] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll_L_convex.stlb\n'}
[0.292358] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll_convex.stla\n'}
[0.292383] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/convex/upper_arm_roll_convex.stlb\n'}
[0.292409] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_lift.dae\n'}
[0.292447] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_lift.stl\n'}
[0.292478] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_lift_color.tif\n'}
[0.292503] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_lift_normals.tif\n'}
[0.292528] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_pan.dae\n'}
[0.292554] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_pan.stl\n'}
[0.292580] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_pan_color.tif\n'}
[0.292605] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_pan_normals.tif\n'}
[0.292630] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/shoulder_yaw.stl\n'}
[0.292656] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/upper_arm_roll.dae\n'}
[0.292682] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/upper_arm_roll.stl\n'}
[0.292708] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/upper_arm_roll_L.stl\n'}
[0.292735] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/upper_arm_roll_color.tif\n'}
[0.292761] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/shoulder_v0/upper_arm_roll_normals.tif\n'}
[0.292787] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0\n'}
[0.292812] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex\n'}
[0.292837] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/hok_tilt.dae\n'}
[0.292862] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/hok_tilt.xml\n'}
[0.292887] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/hok_tilt_convex.stla\n'}
[0.292913] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/hok_tilt_convex.stlb\n'}
[0.292940] (moveit_resources_panda_description) CommandEnded: {'returncode': 0}
[0.293105] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo.dae\n'}
[0.293149] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo.xml\n'}
[0.293185] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo_L.dae\n'}
[0.293223] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo_L.xml\n'}
[0.293263] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo_L_convex.stla\n'}
[0.293300] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo_L_convex.stlb\n'}
[0.293356] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo_convex.stla\n'}
[0.293412] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/convex/tilting_hokuyo_convex.stlb\n'}
[0.293457] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/hok_tilt.stl\n'}
[0.293495] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/tilting_hokuyo.dae\n'}
[0.293531] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/tilting_hokuyo.stl\n'}
[0.293568] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/tilting_hokuyo_L.stl\n'}
[0.293606] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/tilting_hokuyo_color.tif\n'}
[0.293643] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/tilting_laser_v0/tilting_hokuyo_normals.tif\n'}
[0.293680] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0\n'}
[0.293710] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex\n'}
[0.293735] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso.dae\n'}
[0.293761] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso.xml\n'}
[0.293787] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_convex.stla\n'}
[0.293811] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_convex.stlb\n'}
[0.293837] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift.dae\n'}
[0.293862] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift.xml\n'}
[0.293887] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift_L.dae\n'}
[0.293912] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift_L.xml\n'}
[0.293937] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift_L_convex.stla\n'}
[0.293989] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift_L_convex.stlb\n'}
[0.294031] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift_convex.stla\n'}
[0.294068] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/convex/torso_lift_convex.stlb\n'}
[0.294105] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/torso.stl\n'}
[0.294143] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/torso_lift.dae\n'}
[0.294178] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/torso_lift.stl\n'}
[0.294228] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/torso_lift_L.stl\n'}
[0.298489] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/torso_lift_color.tif\n'}
[0.305347] (-) TimerEvent: {}
[0.305437] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/torso_v0/torso_lift_normals.tif\n'}
[0.305474] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0\n'}
[0.305503] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex\n'}
[0.305532] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/elbow_flex.dae\n'}
[0.305563] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/elbow_flex.xml\n'}
[0.305589] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/elbow_flex_convex.stla\n'}
[0.305615] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/elbow_flex_convex.stlb\n'}
[0.305641] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll.dae\n'}
[0.305666] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll.xml\n'}
[0.305692] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll_L.dae\n'}
[0.305719] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll_L.xml\n'}
[0.305746] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll_L_convex.stla\n'}
[0.305775] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll_L_convex.stlb\n'}
[0.305846] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll_convex.stla\n'}
[0.305873] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/forearm_roll_convex.stlb\n'}
[0.305900] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/upper_arm.dae\n'}
[0.305925] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/upper_arm.xml\n'}
[0.305950] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/upper_arm_convex.stla\n'}
[0.305976] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/convex/upper_arm_convex.stlb\n'}
[0.306005] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/elbow_flex.dae\n'}
[0.306046] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/elbow_flex.stl\n'}
[0.306089] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/elbow_flex_color.tif\n'}
[0.306115] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/elbow_flex_normals.tif\n'}
[0.306157] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/forearm_roll.stl\n'}
[0.306192] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/forearm_roll_L.stl\n'}
[0.306217] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/upper_arm.dae\n'}
[0.306250] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/upper_arm.jpg\n'}
[0.306279] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/upper_arm.stl\n'}
[0.306306] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/upper_arm_color.tif\n'}
[0.306332] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/meshes/upper_arm_v0/upper_arm_normals.tif\n'}
[0.306373] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/urdf/robot.xml\n'}
[0.306402] (moveit_resources_pr2_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/kinect.dae\n'}
[0.306428] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 24%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp\n'}
[0.306463] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 36%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp\n'}
[0.311786] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 52%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[0.313413] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 61%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c\n'}
[0.313477] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 71%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c\n'}
[0.315256] (moveit_resources_panda_description) JobEnded: {'identifier': 'moveit_resources_panda_description', 'rc': 0}
[0.317424] (moveit_resources_fanuc_description) CommandEnded: {'returncode': 0}
[0.317590] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c\n'}
[0.317698] (srdfdom) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.317737] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so.2.0.8\n'}
[0.317765] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so\n'}
[0.317803] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom\n'}
[0.317829] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom\n'}
[0.317855] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/model.h\n'}
[0.317881] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/srdf_writer.h\n'}
[0.318359] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom/srdfdom/visibility_control.h\n'}
[0.318399] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/lib/srdfdom/display_srdf\n'}
[0.318430] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/pythonpath.sh\n'}
[0.318461] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/pythonpath.dsv\n'}
[0.318490] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info\n'}
[0.318517] (srdfdom) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/PKG-INFO\n'}
[0.318543] (srdfdom) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/dependency_links.txt\n'}
[0.318571] (srdfdom) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/top_level.txt\n'}
[0.318601] (srdfdom) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom-2.0.8-py3.10.egg-info/SOURCES.txt\n'}
[0.318627] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom\n'}
[0.318653] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom/__init__.py\n'}
[0.318679] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom/srdf.py\n'}
[0.318705] (srdfdom) StdoutLine: {'line': b"Listing '/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages/srdfdom'...\n"}
[0.318768] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/library_path.sh\n'}
[0.318798] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/library_path.dsv\n'}
[0.318824] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/package_run_dependencies/srdfdom\n'}
[0.318850] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/parent_prefix_path/srdfdom\n'}
[0.318876] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/ament_prefix_path.sh\n'}
[0.318902] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/ament_prefix_path.dsv\n'}
[0.318928] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/path.sh\n'}
[0.319205] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/environment/path.dsv\n'}
[0.319294] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.bash\n'}
[0.319342] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.sh\n'}
[0.319372] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.zsh\n'}
[0.319399] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/local_setup.dsv\n'}
[0.319426] (srdfdom) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.dsv\n'}
[0.319453] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/ament_index/resource_index/packages/srdfdom\n'}
[0.320017] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport.cmake\n'}
[0.320072] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport-release.cmake\n'}
[0.320100] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.320126] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.320156] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig.cmake\n'}
[0.320220] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig-version.cmake\n'}
[0.320267] (srdfdom) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.xml\n'}
[0.325690] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs\n'}
[0.330119] (moveit_resources_fanuc_description) JobEnded: {'identifier': 'moveit_resources_fanuc_description', 'rc': 0}
[0.330761] (moveit_common) CommandEnded: {'returncode': 0}
[0.336821] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 84%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[0.340516] (moveit_common) JobEnded: {'identifier': 'moveit_common', 'rc': 0}
[0.340707] (moveit_resources_prbt_support) CommandEnded: {'returncode': 0}
[0.350950] (moveit_resources_prbt_support) JobEnded: {'identifier': 'moveit_resources_prbt_support', 'rc': 0}
[0.352093] (moveit_resources_panda_moveit_config) JobStarted: {'identifier': 'moveit_resources_panda_moveit_config'}
[0.355380] (moveit_resources_fanuc_moveit_config) JobStarted: {'identifier': 'moveit_resources_fanuc_moveit_config'}
[0.364038] (rviz_marker_tools) JobStarted: {'identifier': 'rviz_marker_tools'}
[0.365073] (rosparam_shortcuts) JobStarted: {'identifier': 'rosparam_shortcuts'}
[0.368679] (moveit_resources_pr2_description) CommandEnded: {'returncode': 0}
[0.378202] (moveit_resources_pr2_description) JobEnded: {'identifier': 'moveit_resources_pr2_description', 'rc': 0}
[0.378387] (srdfdom) CommandEnded: {'returncode': 0}
[0.400506] (srdfdom) JobEnded: {'identifier': 'srdfdom', 'rc': 0}
[0.405450] (-) TimerEvent: {}
[0.410917] (moveit_resources_panda_moveit_config) JobProgress: {'identifier': 'moveit_resources_panda_moveit_config', 'progress': 'cmake'}
[0.438225] (moveit_resources_panda_moveit_config) JobProgress: {'identifier': 'moveit_resources_panda_moveit_config', 'progress': 'build'}
[0.438447] (moveit_resources_panda_moveit_config) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble')]), 'shell': False}
[0.455745] (moveit_resources_fanuc_moveit_config) JobProgress: {'identifier': 'moveit_resources_fanuc_moveit_config', 'progress': 'cmake'}
[0.455820] (moveit_resources_fanuc_moveit_config) JobProgress: {'identifier': 'moveit_resources_fanuc_moveit_config', 'progress': 'build'}
[0.455847] (moveit_resources_fanuc_moveit_config) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble')]), 'shell': False}
[0.463307] (rviz_marker_tools) JobProgress: {'identifier': 'rviz_marker_tools', 'progress': 'cmake'}
[0.466431] (rviz_marker_tools) JobProgress: {'identifier': 'rviz_marker_tools', 'progress': 'build'}
[0.466623] (rviz_marker_tools) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/rviz_marker_tools', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/rviz_marker_tools', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/rviz_marker_tools'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble')]), 'shell': False}
[0.470820] (rosparam_shortcuts) JobProgress: {'identifier': 'rosparam_shortcuts', 'progress': 'cmake'}
[0.471606] (rosparam_shortcuts) JobProgress: {'identifier': 'rosparam_shortcuts', 'progress': 'build'}
[0.474055] (rosparam_shortcuts) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/rosparam_shortcuts', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/rosparam_shortcuts', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/rosparam_shortcuts'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.479473] (moveit_resources_fanuc_moveit_config) CommandEnded: {'returncode': 0}
[0.480004] (moveit_resources_fanuc_moveit_config) JobProgress: {'identifier': 'moveit_resources_fanuc_moveit_config', 'progress': 'install'}
[0.483968] (moveit_resources_fanuc_moveit_config) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_fanuc_moveit_config'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble')]), 'shell': False}
[0.487635] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.487742] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/ament_index/resource_index/package_run_dependencies/moveit_resources_fanuc_moveit_config\n'}
[0.487792] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/ament_index/resource_index/parent_prefix_path/moveit_resources_fanuc_moveit_config\n'}
[0.487821] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/environment/ament_prefix_path.sh\n'}
[0.487860] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/environment/ament_prefix_path.dsv\n'}
[0.487889] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/environment/path.sh\n'}
[0.487917] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/environment/path.dsv\n'}
[0.487951] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/local_setup.bash\n'}
[0.487983] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/local_setup.sh\n'}
[0.488010] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/local_setup.zsh\n'}
[0.488035] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/local_setup.dsv\n'}
[0.488080] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.dsv\n'}
[0.488125] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/ament_index/resource_index/packages/moveit_resources_fanuc_moveit_config\n'}
[0.488384] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/cmake/moveit_resources_fanuc_moveit_configConfig.cmake\n'}
[0.488445] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/cmake/moveit_resources_fanuc_moveit_configConfig-version.cmake\n'}
[0.488480] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.xml\n'}
[0.488507] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/launch\n'}
[0.488534] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/launch/demo.launch.py\n'}
[0.488579] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/launch/moveit.rviz\n'}
[0.488638] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config\n'}
[0.488709] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/chomp_planning.yaml\n'}
[0.488747] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/fanuc.ros2_control.xacro\n'}
[0.488806] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/fanuc.srdf\n'}
[0.488835] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/fanuc.urdf.xacro\n'}
[0.488861] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/initial_positions.yaml\n'}
[0.488887] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/joint_limits.yaml\n'}
[0.488925] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/kinematics.yaml\n'}
[0.488953] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/moveit_controllers.yaml\n'}
[0.488982] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/ompl_planning.yaml\n'}
[0.489010] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/pilz_cartesian_limits.yaml\n'}
[0.489037] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/ros2_controllers.yaml\n'}
[0.489063] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/config/sensors_3d.yaml\n'}
[0.489099] (moveit_resources_fanuc_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/.setup_assistant\n'}
[0.491005] (moveit_resources_panda_moveit_config) CommandEnded: {'returncode': 0}
[0.491863] (moveit_resources_panda_moveit_config) JobProgress: {'identifier': 'moveit_resources_panda_moveit_config', 'progress': 'install'}
[0.491887] (moveit_resources_panda_moveit_config) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources_panda_moveit_config'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/opt/ros/humble')]), 'shell': False}
[0.502272] (moveit_resources_fanuc_moveit_config) CommandEnded: {'returncode': 0}
[0.505525] (-) TimerEvent: {}
[0.510717] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.510803] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/ament_index/resource_index/package_run_dependencies/moveit_resources_panda_moveit_config\n'}
[0.510870] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/ament_index/resource_index/parent_prefix_path/moveit_resources_panda_moveit_config\n'}
[0.510935] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/environment/ament_prefix_path.sh\n'}
[0.510982] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/environment/ament_prefix_path.dsv\n'}
[0.511032] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/environment/path.sh\n'}
[0.511073] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/environment/path.dsv\n'}
[0.511113] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/local_setup.bash\n'}
[0.511140] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/local_setup.sh\n'}
[0.511166] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/local_setup.zsh\n'}
[0.511192] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/local_setup.dsv\n'}
[0.511217] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.dsv\n'}
[0.511255] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/ament_index/resource_index/packages/moveit_resources_panda_moveit_config\n'}
[0.511290] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/cmake/moveit_resources_panda_moveit_configConfig.cmake\n'}
[0.511316] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/cmake/moveit_resources_panda_moveit_configConfig-version.cmake\n'}
[0.511342] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.xml\n'}
[0.511368] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/launch\n'}
[0.511393] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/launch/demo.launch.py\n'}
[0.511421] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/launch/moveit.rviz\n'}
[0.511465] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/launch/moveit_empty.rviz\n'}
[0.511505] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/launch/moveit_rviz.launch.py\n'}
[0.511530] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config\n'}
[0.511555] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/chomp_planning.yaml\n'}
[0.511601] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/gripper_moveit_controllers.yaml\n'}
[0.511629] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/hand.xacro\n'}
[0.511655] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/initial_positions.yaml\n'}
[0.511680] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/joint_limits.yaml\n'}
[0.511715] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/kinematics.yaml\n'}
[0.511757] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/lerp_planning.yaml\n'}
[0.511901] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/moveit_controllers.yaml\n'}
[0.511950] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/ompl_planning.yaml\n'}
[0.511979] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda.ros2_control.xacro\n'}
[0.512004] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda.srdf\n'}
[0.512029] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda.urdf.xacro\n'}
[0.512054] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda_arm.srdf.xacro\n'}
[0.512082] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda_arm.xacro\n'}
[0.512139] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda_arm_hand.srdf.xacro\n'}
[0.512169] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/panda_hand.ros2_control.xacro\n'}
[0.512194] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/pilz_cartesian_limits.yaml\n'}
[0.512219] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/pilz_industrial_motion_planner_planning.yaml\n'}
[0.512251] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/ros2_controllers.yaml\n'}
[0.512277] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/sensors_kinect_depthmap.yaml\n'}
[0.512302] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/sensors_kinect_pointcloud.yaml\n'}
[0.512327] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/config/trajopt_planning.yaml\n'}
[0.512353] (moveit_resources_panda_moveit_config) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/.setup_assistant\n'}
[0.521283] (moveit_resources_fanuc_moveit_config) JobEnded: {'identifier': 'moveit_resources_fanuc_moveit_config', 'rc': 0}
[0.525422] (moveit_resources_panda_moveit_config) CommandEnded: {'returncode': 0}
[0.526388] (moveit_task_constructor_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[0.526491] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 8, in <module>\n'}
[0.526532] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_generator_py import generate_py\n'}
[0.526562] (moveit_task_constructor_msgs) StderrLine: {'line': b"ImportError: cannot import name 'generate_py'\n"}
[0.530338] (rviz_marker_tools) StdoutLine: {'line': b'[100%] Built target rviz_marker_tools\n'}
[0.535713] (rosparam_shortcuts) StdoutLine: {'line': b'[ 15%] Built target gtest_main\n'}
[0.536144] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[2]: *** [/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/build.make:334: rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c] Error 1\n'}
[0.536839] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:725: moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/all] Error 2\n'}
[0.536926] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[1]: *** Waiting for unfinished jobs....\n'}
[0.543852] (moveit_resources_panda_moveit_config) JobEnded: {'identifier': 'moveit_resources_panda_moveit_config', 'rc': 0}
[0.544530] (moveit_core) JobStarted: {'identifier': 'moveit_core'}
[0.550368] (rosparam_shortcuts) StdoutLine: {'line': b'[ 30%] Built target gtest\n'}
[0.550833] (rosparam_shortcuts) StdoutLine: {'line': b'[ 53%] Built target rosparam_shortcuts\n'}
[0.551100] (moveit_resources) JobStarted: {'identifier': 'moveit_resources'}
[0.562943] (rviz_marker_tools) CommandEnded: {'returncode': 0}
[0.563384] (rviz_marker_tools) JobProgress: {'identifier': 'rviz_marker_tools', 'progress': 'install'}
[0.563538] (rviz_marker_tools) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/rviz_marker_tools'], 'cwd': '/home/<USER>/ws_moveit2/build/rviz_marker_tools', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/rviz_marker_tools'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble')]), 'shell': False}
[0.566787] (moveit_core) JobProgress: {'identifier': 'moveit_core', 'progress': 'cmake'}
[0.570273] (rviz_marker_tools) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.570898] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/include\n'}
[0.570961] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/include/rviz_marker_tools\n'}
[0.571015] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/include/rviz_marker_tools/marker_creation.h\n'}
[0.571071] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/lib/librviz_marker_tools.so\n'}
[0.571119] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/library_path.sh\n'}
[0.571170] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/library_path.dsv\n'}
[0.571223] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/ament_index/resource_index/package_run_dependencies/rviz_marker_tools\n'}
[0.571557] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/ament_index/resource_index/parent_prefix_path/rviz_marker_tools\n'}
[0.571615] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/ament_prefix_path.sh\n'}
[0.571669] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/ament_prefix_path.dsv\n'}
[0.571710] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/path.sh\n'}
[0.571823] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/environment/path.dsv\n'}
[0.571922] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.bash\n'}
[0.571966] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.sh\n'}
[0.571995] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.zsh\n'}
[0.572061] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/local_setup.dsv\n'}
[0.572279] (rviz_marker_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.dsv\n'}
[0.574033] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/ament_index/resource_index/packages/rviz_marker_tools\n'}
[0.576462] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsTargetsExport.cmake\n'}
[0.576624] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsTargetsExport-release.cmake\n'}
[0.576698] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.577053] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.577207] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsConfig.cmake\n'}
[0.577453] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/cmake/rviz_marker_toolsConfig-version.cmake\n'}
[0.580842] (rviz_marker_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rviz_marker_tools/share/rviz_marker_tools/package.xml\n'}
[0.580901] (moveit_core) JobProgress: {'identifier': 'moveit_core', 'progress': 'build'}
[0.580920] (moveit_core) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_core', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_core', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/srdfdom/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_core'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble')]), 'shell': False}
[0.581745] (moveit_task_constructor_msgs) StdoutLine: {'line': b'running egg_info\n'}
[0.582479] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing moveit_task_constructor_msgs.egg-info/PKG-INFO\n'}
[0.583216] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt\n'}
[0.583321] (rosparam_shortcuts) StdoutLine: {'line': b'[ 69%] Built target rosparam_shortcuts_example\n'}
[0.583465] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt\n'}
[0.585554] (rosparam_shortcuts) StdoutLine: {'line': b'[ 84%] Built target rosparam_shortcuts_node_parameters_example\n'}
[0.586828] (moveit_task_constructor_msgs) StdoutLine: {'line': b"reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[0.589804] (moveit_task_constructor_msgs) StdoutLine: {'line': b"writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[0.593318] (moveit_resources) JobProgress: {'identifier': 'moveit_resources', 'progress': 'cmake'}
[0.601426] (moveit_resources) JobProgress: {'identifier': 'moveit_resources', 'progress': 'build'}
[0.601558] (moveit_resources) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_resources', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble')]), 'shell': False}
[0.601989] (rosparam_shortcuts) StdoutLine: {'line': b'[100%] Built target test_node_parameters\n'}
[0.602384] (rviz_marker_tools) CommandEnded: {'returncode': 0}
[0.605879] (-) TimerEvent: {}
[0.618201] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 84%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg\n'}
[0.619111] (rviz_marker_tools) JobEnded: {'identifier': 'rviz_marker_tools', 'rc': 0}
[0.619892] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[0.620095] (rosparam_shortcuts) CommandEnded: {'returncode': 0}
[0.622541] (rosparam_shortcuts) JobProgress: {'identifier': 'rosparam_shortcuts', 'progress': 'install'}
[0.623007] (rosparam_shortcuts) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/rosparam_shortcuts'], 'cwd': '/home/<USER>/ws_moveit2/build/rosparam_shortcuts', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/rosparam_shortcuts'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.625839] (moveit_resources) CommandEnded: {'returncode': 0}
[0.626375] (moveit_resources) JobProgress: {'identifier': 'moveit_resources', 'progress': 'install'}
[0.626415] (moveit_resources) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/ws_moveit2/build/moveit_resources'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_resources', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-1813b4a8-c1ec-4bb1-bad1-229d09edbc73.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-287a5b9a2ec5b328.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-84913459'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_resources'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/opt/ros/humble')]), 'shell': False}
[0.629722] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 2}
[0.630481] (rosparam_shortcuts) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.630539] (moveit_resources) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.630591] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/ament_index/resource_index/package_run_dependencies/moveit_resources\n'}
[0.630639] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/ament_index/resource_index/parent_prefix_path/moveit_resources\n'}
[0.630678] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/ament_prefix_path.sh\n'}
[0.630708] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/ament_prefix_path.dsv\n'}
[0.630734] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/path.sh\n'}
[0.630775] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/environment/path.dsv\n'}
[0.630804] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.bash\n'}
[0.630829] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.sh\n'}
[0.630855] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.zsh\n'}
[0.631355] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/local_setup.dsv\n'}
[0.632853] (moveit_resources) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.dsv\n'}
[0.632976] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/ament_index/resource_index/packages/moveit_resources\n'}
[0.633023] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/cmake/moveit_resourcesConfig.cmake\n'}
[0.633060] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/librosparam_shortcuts.so\n'}
[0.633093] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/rosparam_shortcuts/example\n'}
[0.633119] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib/rosparam_shortcuts/node_parameters_example\n'}
[0.633146] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include\n'}
[0.633172] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts\n'}
[0.633197] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts/node_parameters.h\n'}
[0.633222] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/include/rosparam_shortcuts/rosparam_shortcuts.h\n'}
[0.633893] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/launch\n'}
[0.634202] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/launch/example.launch.py\n'}
[0.635816] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/config\n'}
[0.635864] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/config/example.yaml\n'}
[0.635893] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/library_path.sh\n'}
[0.635920] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/library_path.dsv\n'}
[0.635946] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/package_run_dependencies/rosparam_shortcuts\n'}
[0.635972] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/parent_prefix_path/rosparam_shortcuts\n'}
[0.635998] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/ament_prefix_path.sh\n'}
[0.636024] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/ament_prefix_path.dsv\n'}
[0.636055] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/path.sh\n'}
[0.636083] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/environment/path.dsv\n'}
[0.636110] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.bash\n'}
[0.636135] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.sh\n'}
[0.636161] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.zsh\n'}
[0.636187] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/local_setup.dsv\n'}
[0.636213] (rosparam_shortcuts) StdoutLine: {'line': b'-- Installing: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.dsv\n'}
[0.636238] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/ament_index/resource_index/packages/rosparam_shortcuts\n'}
[0.636623] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/export_rosparam_shortcutsExport.cmake\n'}
[0.636652] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/export_rosparam_shortcutsExport-release.cmake\n'}
[0.636681] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.636710] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.636736] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/rosparam_shortcutsConfig.cmake\n'}
[0.636762] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/cmake/rosparam_shortcutsConfig-version.cmake\n'}
[0.636787] (rosparam_shortcuts) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/rosparam_shortcuts/share/rosparam_shortcuts/package.xml\n'}
[0.636813] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/cmake/moveit_resourcesConfig-version.cmake\n'}
[0.636843] (moveit_resources) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ws_moveit2/install/moveit_resources/share/moveit_resources/package.xml\n'}
[0.639813] (moveit_task_constructor_msgs) JobEnded: {'identifier': 'moveit_task_constructor_msgs', 'rc': 2}
[0.641395] (moveit_resources) JobEnded: {'identifier': 'moveit_resources', 'rc': 'SIGINT'}
[0.642664] (rosparam_shortcuts) JobEnded: {'identifier': 'rosparam_shortcuts', 'rc': 'SIGINT'}
[0.646931] (launch_param_builder) JobEnded: {'identifier': 'launch_param_builder', 'rc': 'SIGINT'}
[0.654974] (moveit_core) StdoutLine: {'line': b'[  1%] Built target moveit_exceptions\n'}
[0.658439] (moveit_core) StdoutLine: {'line': b'[  3%] Built target gmock\n'}
[0.668088] (moveit_core) StdoutLine: {'line': b'[  4%] Built target moveit_kinematics_base\n'}
[0.668241] (moveit_core) StdoutLine: {'line': b'[  5%] Built target gtest_main\n'}
[0.670836] (moveit_core) StdoutLine: {'line': b'[  8%] Built target moveit_utils\n'}
[0.673042] (moveit_core) StdoutLine: {'line': b'[  9%] Built target gtest\n'}
[0.681223] (moveit_core) StdoutLine: {'line': b'[ 10%] Built target moveit_transforms\n'}
[0.681371] (moveit_core) StdoutLine: {'line': b'[ 11%] Built target gmock_main\n'}
[0.686869] (moveit_core) StdoutLine: {'line': b'[ 12%] Built target moveit_smoothing_base\n'}
[0.690128] (moveit_core) StdoutLine: {'line': b'[ 17%] Built target moveit_butterworth_parameters\n'}
[0.690232] (moveit_core) StdoutLine: {'line': b'[ 17%] Built target moveit_distance_field\n'}
[0.693551] (moveit_core) StdoutLine: {'line': b'[ 18%] Built target moveit_version\n'}
[0.695581] (moveit_core) StdoutLine: {'line': b'[ 19%] Built target test_transforms\n'}
[0.706155] (-) TimerEvent: {}
[0.710380] (moveit_core) StdoutLine: {'line': b'[ 20%] Built target test_voxel_grid\n'}
[0.712646] (moveit_core) StdoutLine: {'line': b'[ 26%] Built target moveit_robot_model\n'}
[0.715345] (moveit_core) StdoutLine: {'line': b'[ 27%] Built target test_distance_field\n'}
[0.734700] (moveit_core) StdoutLine: {'line': b'[ 28%] Built target moveit_butterworth_filter\n'}
[0.735323] (moveit_core) StdoutLine: {'line': b'[ 30%] Built target moveit_test_utils\n'}
[0.740663] (moveit_core) StdoutLine: {'line': b'[ 33%] Built target moveit_robot_state\n'}
[0.759617] (moveit_core) StdoutLine: {'line': b'[ 34%] Built target test_butterworth_filter\n'}
[0.760238] (moveit_core) StdoutLine: {'line': b'[ 35%] Built target moveit_dynamics_solver\n'}
[0.768232] (moveit_core) StdoutLine: {'line': b'[ 36%] Built target moveit_robot_trajectory\n'}
[0.768375] (moveit_core) StdoutLine: {'line': b'[ 37%] Built target test_robot_model\n'}
[0.775827] (moveit_core) StdoutLine: {'line': b'[ 38%] Built target test_robot_state_benchmark\n'}
[0.776263] (moveit_core) StdoutLine: {'line': b'[ 39%] Built target moveit_kinematics_metrics\n'}
[0.783689] (moveit_core) StdoutLine: {'line': b'[ 42%] Built target test_aabb\n'}
[0.783938] (moveit_core) StdoutLine: {'line': b'[ 42%] Built target test_cartesian_interpolator\n'}
[0.784157] (moveit_core) StdoutLine: {'line': b'[ 43%] Built target test_robot_state\n'}
[0.784810] (moveit_core) StdoutLine: {'line': b'[ 44%] Built target test_robot_state_complex\n'}
[0.786928] (moveit_core) StdoutLine: {'line': b'[ 48%] Built target test_robot_trajectory\n'}
[0.787016] (moveit_core) StdoutLine: {'line': b'[ 50%] Built target moveit_collision_detection\n'}
[0.802469] (moveit_core) StdoutLine: {'line': b'[ 53%] Built target moveit_trajectory_processing\n'}
[0.802615] (moveit_core) StdoutLine: {'line': b'[ 55%] Built target moveit_planning_interface\n'}
[0.803516] (moveit_core) StdoutLine: {'line': b'[ 56%] Built target test_world\n'}
[0.804938] (moveit_core) StdoutLine: {'line': b'[ 58%] Built target test_world_diff\n'}
[0.807050] (moveit_core) StdoutLine: {'line': b'[ 59%] Built target test_all_valid\n'}
[0.807126] (-) TimerEvent: {}
[0.813460] (moveit_core) StdoutLine: {'line': b'[ 61%] Built target moveit_collision_detection_fcl\n'}
[0.822109] (moveit_core) StdoutLine: {'line': b'[ 62%] Built target test_time_parameterization\n'}
[0.825315] (moveit_core) StdoutLine: {'line': b'[ 65%] Built target test_ruckig_traj_smoothing\n'}
[0.825496] (moveit_core) StdoutLine: {'line': b'[ 66%] Built target test_time_optimal_trajectory_generation\n'}
[0.825741] (moveit_core) StdoutLine: {'line': b'[ 70%] Built target moveit_collision_detection_bullet\n'}
[0.838829] (moveit_core) StdoutLine: {'line': b'[ 71%] Built target test_fcl_collision_env\n'}
[0.838958] (moveit_core) StdoutLine: {'line': b'[ 72%] Built target test_fcl_collision_detection\n'}
[0.842859] (moveit_core) StdoutLine: {'line': b'[ 74%] Built target moveit_kinematic_constraints\n'}
[0.843519] (moveit_core) StdoutLine: {'line': b'[ 76%] Built target test_fcl_collision_detection_panda\n'}
[0.846822] (moveit_core) StdoutLine: {'line': b'[ 77%] Built target test_bullet_collision_detection\n'}
[0.848563] (moveit_core) StdoutLine: {'line': b'[ 79%] Built target test_bullet_collision_detection_panda\n'}
[0.848822] (moveit_core) StdoutLine: {'line': b'[ 80%] Built target test_bullet_continuous_collision_checking\n'}
[0.860292] (moveit_core) StdoutLine: {'line': b'[ 81%] Built target test_constraints\n'}
[0.860762] (moveit_core) StdoutLine: {'line': b'[ 82%] Built target test_orientation_constraints\n'}
[0.863766] (moveit_core) StdoutLine: {'line': b'[ 84%] Built target moveit_planning_scene\n'}
[0.884225] (moveit_core) StdoutLine: {'line': b'[ 85%] Built target test_multi_threaded\n'}
[0.888295] (moveit_core) StdoutLine: {'line': b'[ 86%] Built target moveit_planning_request_adapter\n'}
[0.888457] (moveit_core) StdoutLine: {'line': b'[ 87%] Built target test_collision_objects\n'}
[0.893631] (moveit_core) StdoutLine: {'line': b'[ 88%] Built target test_planning_scene\n'}
[0.896010] (moveit_core) StdoutLine: {'line': b'[ 89%] Built target collision_detector_bullet_plugin\n'}
[0.896165] (moveit_core) StdoutLine: {'line': b'[ 90%] Built target collision_detector_fcl_plugin\n'}
[0.898775] (moveit_core) StdoutLine: {'line': b'[ 93%] Built target moveit_constraint_samplers\n'}
[0.902427] (moveit_core) StdoutLine: {'line': b'[ 96%] Built target moveit_collision_distance_field\n'}
[0.908210] (-) TimerEvent: {}
[0.922457] (moveit_core) StdoutLine: {'line': b'[ 99%] Built target test_constraint_samplers\n'}
[0.926272] (moveit_core) StdoutLine: {'line': b'[100%] Built target test_collision_distance_field\n'}
[0.930065] (moveit_core) JobEnded: {'identifier': 'moveit_core', 'rc': 'SIGINT'}
[0.942334] (moveit_configs_utils) JobSkipped: {'identifier': 'moveit_configs_utils'}
[0.942463] (chomp_motion_planner) JobSkipped: {'identifier': 'chomp_motion_planner'}
[0.942552] (moveit_resources_prbt_ikfast_manipulator_plugin) JobSkipped: {'identifier': 'moveit_resources_prbt_ikfast_manipulator_plugin'}
[0.942574] (moveit_ros_occupancy_map_monitor) JobSkipped: {'identifier': 'moveit_ros_occupancy_map_monitor'}
[0.942585] (moveit_simple_controller_manager) JobSkipped: {'identifier': 'moveit_simple_controller_manager'}
[0.942600] (pilz_industrial_motion_planner_testutils) JobSkipped: {'identifier': 'pilz_industrial_motion_planner_testutils'}
[0.942608] (moveit_chomp_optimizer_adapter) JobSkipped: {'identifier': 'moveit_chomp_optimizer_adapter'}
[0.942616] (moveit_planners_chomp) JobSkipped: {'identifier': 'moveit_planners_chomp'}
[0.942625] (moveit_plugins) JobSkipped: {'identifier': 'moveit_plugins'}
[0.942633] (moveit_ros_control_interface) JobSkipped: {'identifier': 'moveit_ros_control_interface'}
[0.942641] (moveit_ros_planning) JobSkipped: {'identifier': 'moveit_ros_planning'}
[0.942648] (moveit_kinematics) JobSkipped: {'identifier': 'moveit_kinematics'}
[0.942656] (moveit_planners_ompl) JobSkipped: {'identifier': 'moveit_planners_ompl'}
[0.942663] (moveit_ros_perception) JobSkipped: {'identifier': 'moveit_ros_perception'}
[0.942671] (moveit_ros_robot_interaction) JobSkipped: {'identifier': 'moveit_ros_robot_interaction'}
[0.942678] (moveit_ros_warehouse) JobSkipped: {'identifier': 'moveit_ros_warehouse'}
[0.942686] (moveit_visual_tools) JobSkipped: {'identifier': 'moveit_visual_tools'}
[0.942693] (moveit_ros_benchmarks) JobSkipped: {'identifier': 'moveit_ros_benchmarks'}
[0.942701] (moveit_ros_move_group) JobSkipped: {'identifier': 'moveit_ros_move_group'}
[0.942708] (moveit_resources_prbt_moveit_config) JobSkipped: {'identifier': 'moveit_resources_prbt_moveit_config'}
[0.942716] (moveit_ros_planning_interface) JobSkipped: {'identifier': 'moveit_ros_planning_interface'}
[0.942723] (moveit_hybrid_planning) JobSkipped: {'identifier': 'moveit_hybrid_planning'}
[0.942731] (moveit_resources_prbt_pg70_support) JobSkipped: {'identifier': 'moveit_resources_prbt_pg70_support'}
[0.942738] (moveit_ros_visualization) JobSkipped: {'identifier': 'moveit_ros_visualization'}
[0.942746] (moveit_servo) JobSkipped: {'identifier': 'moveit_servo'}
[0.942753] (moveit_ros) JobSkipped: {'identifier': 'moveit_ros'}
[0.942761] (moveit_setup_framework) JobSkipped: {'identifier': 'moveit_setup_framework'}
[0.942768] (pilz_industrial_motion_planner) JobSkipped: {'identifier': 'pilz_industrial_motion_planner'}
[0.942776] (moveit_planners) JobSkipped: {'identifier': 'moveit_planners'}
[0.942783] (moveit_setup_app_plugins) JobSkipped: {'identifier': 'moveit_setup_app_plugins'}
[0.942795] (moveit_setup_controllers) JobSkipped: {'identifier': 'moveit_setup_controllers'}
[0.942809] (moveit_setup_core_plugins) JobSkipped: {'identifier': 'moveit_setup_core_plugins'}
[0.942816] (moveit_setup_srdf_plugins) JobSkipped: {'identifier': 'moveit_setup_srdf_plugins'}
[0.942824] (moveit_runtime) JobSkipped: {'identifier': 'moveit_runtime'}
[0.942831] (moveit_setup_assistant) JobSkipped: {'identifier': 'moveit_setup_assistant'}
[0.942838] (moveit_task_constructor_core) JobSkipped: {'identifier': 'moveit_task_constructor_core'}
[0.942846] (moveit) JobSkipped: {'identifier': 'moveit'}
[0.942853] (moveit_task_constructor_capabilities) JobSkipped: {'identifier': 'moveit_task_constructor_capabilities'}
[0.942861] (moveit_task_constructor_visualization) JobSkipped: {'identifier': 'moveit_task_constructor_visualization'}
[0.942868] (moveit2_tutorials) JobSkipped: {'identifier': 'moveit2_tutorials'}
[0.942876] (moveit_task_constructor_demo) JobSkipped: {'identifier': 'moveit_task_constructor_demo'}
[0.942884] (-) EventReactorShutdown: {}
