-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[33mCMake Warning (dev) at /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:20 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)
  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)
  CMakeLists.txt:32 (rosidl_generate_interfaces)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)
  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)
  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)
  CMakeLists.txt:32 (rosidl_generate_interfaces)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)
  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)
  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)
  CMakeLists.txt:32 (rosidl_generate_interfaces)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
-- Using PYTHON_EXECUTABLE: /usr/local/bin/python3.6
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
-- Configuring done (1.7s)
-- Generating done (0.1s)
[33mCMake Warning:
  Manually-specified variables were not used by the project:

    ROSIDL_GENERATE_INTERFACES_SKIP_PYTHON

[0m
-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs
[  0%] Built target moveit_task_constructor_msgs__cpp
[  0%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs
[ 12%] Built target moveit_task_constructor_msgs__rosidl_generator_c
[ 24%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp
[ 36%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c
[ 48%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c
[ 60%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp
[ 82%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp
[ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c
[ 83%] Built target moveit_task_constructor_msgs
[ 84%] [34m[1mGenerating Python code for ROS interfaces[0m
Traceback (most recent call last):
  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 8, in <module>
    from rosidl_generator_py import generate_py
ImportError: cannot import name 'generate_py'
gmake[2]: *** [/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/build.make:334: rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:725: moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/all] Error 2
gmake[1]: *** Waiting for unfinished jobs....
running egg_info
writing moveit_task_constructor_msgs.egg-info/PKG-INFO
writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt
writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt
reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'
writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'
[ 84%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg
gmake: *** [Makefile:146: all] Error 2
