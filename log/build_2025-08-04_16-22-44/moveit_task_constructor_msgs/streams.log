[0.006s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs -DCMAKE_BUILD_TYPE=Release -DROSIDL_GENERATE_INTERFACES_SKIP_PYTHON=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs
[0.018s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.122s] -- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)
[0.138s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.140s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.144s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.153s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.164s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.174s] -- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)
[0.296s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.304s] -- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[0.651s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.305s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.561s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.563s] [33mCMake Warning (dev) at /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:20 (find_package):
[1.563s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.563s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.563s]   the cmake_policy command to set the policy and suppress this warning.
[1.563s] 
[1.563s] Call Stack (most recent call first):
[1.563s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)
[1.563s]   /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)
[1.563s]   CMakeLists.txt:32 (rosidl_generate_interfaces)
[1.563s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.563s] [0m
[1.577s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.577s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[1.577s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.577s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.577s]   the cmake_policy command to set the policy and suppress this warning.
[1.577s] 
[1.577s] Call Stack (most recent call first):
[1.577s]   /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)
[1.577s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)
[1.577s]   /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)
[1.577s]   CMakeLists.txt:32 (rosidl_generate_interfaces)
[1.577s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.577s] [0m
[1.590s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[1.590s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.590s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.590s]   the cmake_policy command to set the policy and suppress this warning.
[1.590s] 
[1.590s] Call Stack (most recent call first):
[1.590s]   /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)
[1.590s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)
[1.590s]   /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)
[1.590s]   CMakeLists.txt:32 (rosidl_generate_interfaces)
[1.590s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.590s] [0m
[1.591s] -- Using PYTHON_EXECUTABLE: /usr/local/bin/python3.6
[1.591s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.591s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[1.730s] -- Configuring done (1.7s)
[1.802s] -- Generating done (0.1s)
[1.802s] [33mCMake Warning:
[1.802s]   Manually-specified variables were not used by the project:
[1.802s] 
[1.802s]     ROSIDL_GENERATE_INTERFACES_SKIP_PYTHON
[1.802s] 
[1.802s] [0m
[1.811s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs
[1.817s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs -DCMAKE_BUILD_TYPE=Release -DROSIDL_GENERATE_INTERFACES_SKIP_PYTHON=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs
[1.817s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
[1.839s] [  0%] Built target moveit_task_constructor_msgs__cpp
[1.839s] [  0%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs
[1.846s] [ 12%] Built target moveit_task_constructor_msgs__rosidl_generator_c
[1.869s] [ 24%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp
[1.872s] [ 36%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c
[1.874s] [ 48%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c
[1.877s] [ 60%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp
[1.883s] [ 82%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp
[1.884s] [ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c
[1.890s] [ 83%] Built target moveit_task_constructor_msgs
[1.897s] [ 84%] [34m[1mGenerating Python code for ROS interfaces[0m
[1.945s] Traceback (most recent call last):
[1.945s]   File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 8, in <module>
[1.945s]     from rosidl_generator_py import generate_py
[1.945s] ImportError: cannot import name 'generate_py'
[1.950s] gmake[2]: *** [/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/build.make:334: rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c] Error 1
[1.950s] gmake[1]: *** [CMakeFiles/Makefile2:725: moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/all] Error 2
[1.950s] gmake[1]: *** Waiting for unfinished jobs....
[1.951s] running egg_info
[1.952s] writing moveit_task_constructor_msgs.egg-info/PKG-INFO
[1.953s] writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt
[1.953s] writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt
[1.955s] reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'
[1.955s] writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'
[1.968s] [ 84%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg
[1.969s] gmake: *** [Makefile:146: all] Error 2
[1.970s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs -- -j8 -l8
