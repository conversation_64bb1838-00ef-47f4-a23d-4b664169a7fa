[0.000000] (-) TimerEvent: {}
[0.000869] (-) JobUnselected: {'identifier': 'chomp_motion_planner'}
[0.000892] (-) JobUnselected: {'identifier': 'launch_param_builder'}
[0.000902] (-) JobUnselected: {'identifier': 'moveit'}
[0.000911] (-) JobUnselected: {'identifier': 'moveit2_tutorials'}
[0.000918] (-) JobUnselected: {'identifier': 'moveit_chomp_optimizer_adapter'}
[0.000926] (-) JobUnselected: {'identifier': 'moveit_common'}
[0.000933] (-) JobUnselected: {'identifier': 'moveit_configs_utils'}
[0.000940] (-) JobUnselected: {'identifier': 'moveit_core'}
[0.000947] (-) JobUnselected: {'identifier': 'moveit_hybrid_planning'}
[0.000955] (-) JobUnselected: {'identifier': 'moveit_kinematics'}
[0.000962] (-) JobUnselected: {'identifier': 'moveit_planners'}
[0.000969] (-) JobUnselected: {'identifier': 'moveit_planners_chomp'}
[0.000976] (-) JobUnselected: {'identifier': 'moveit_planners_ompl'}
[0.000983] (-) JobUnselected: {'identifier': 'moveit_plugins'}
[0.000990] (-) JobUnselected: {'identifier': 'moveit_resources'}
[0.000997] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_description'}
[0.001005] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_moveit_config'}
[0.001012] (-) JobUnselected: {'identifier': 'moveit_resources_panda_description'}
[0.001019] (-) JobUnselected: {'identifier': 'moveit_resources_panda_moveit_config'}
[0.001027] (-) JobUnselected: {'identifier': 'moveit_resources_pr2_description'}
[0.001034] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_ikfast_manipulator_plugin'}
[0.001041] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_moveit_config'}
[0.001048] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_pg70_support'}
[0.001055] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_support'}
[0.001062] (-) JobUnselected: {'identifier': 'moveit_ros'}
[0.001069] (-) JobUnselected: {'identifier': 'moveit_ros_benchmarks'}
[0.001076] (-) JobUnselected: {'identifier': 'moveit_ros_control_interface'}
[0.001083] (-) JobUnselected: {'identifier': 'moveit_ros_move_group'}
[0.001090] (-) JobUnselected: {'identifier': 'moveit_ros_occupancy_map_monitor'}
[0.001097] (-) JobUnselected: {'identifier': 'moveit_ros_perception'}
[0.001104] (-) JobUnselected: {'identifier': 'moveit_ros_planning'}
[0.001112] (-) JobUnselected: {'identifier': 'moveit_ros_planning_interface'}
[0.001119] (-) JobUnselected: {'identifier': 'moveit_ros_robot_interaction'}
[0.001126] (-) JobUnselected: {'identifier': 'moveit_ros_visualization'}
[0.001133] (-) JobUnselected: {'identifier': 'moveit_ros_warehouse'}
[0.001140] (-) JobUnselected: {'identifier': 'moveit_runtime'}
[0.001147] (-) JobUnselected: {'identifier': 'moveit_servo'}
[0.001154] (-) JobUnselected: {'identifier': 'moveit_setup_app_plugins'}
[0.001161] (-) JobUnselected: {'identifier': 'moveit_setup_assistant'}
[0.001168] (-) JobUnselected: {'identifier': 'moveit_setup_controllers'}
[0.001175] (-) JobUnselected: {'identifier': 'moveit_setup_core_plugins'}
[0.001182] (-) JobUnselected: {'identifier': 'moveit_setup_framework'}
[0.001189] (-) JobUnselected: {'identifier': 'moveit_setup_srdf_plugins'}
[0.001196] (-) JobUnselected: {'identifier': 'moveit_simple_controller_manager'}
[0.001203] (-) JobUnselected: {'identifier': 'moveit_task_constructor_capabilities'}
[0.001210] (-) JobUnselected: {'identifier': 'moveit_task_constructor_core'}
[0.001217] (-) JobUnselected: {'identifier': 'moveit_task_constructor_demo'}
[0.001224] (-) JobUnselected: {'identifier': 'moveit_task_constructor_visualization'}
[0.001231] (-) JobUnselected: {'identifier': 'moveit_visual_tools'}
[0.001238] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner'}
[0.001245] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner_testutils'}
[0.001252] (-) JobUnselected: {'identifier': 'rosparam_shortcuts'}
[0.001259] (-) JobUnselected: {'identifier': 'rviz_marker_tools'}
[0.001266] (-) JobUnselected: {'identifier': 'srdfdom'}
[0.001274] (moveit_task_constructor_msgs) JobQueued: {'identifier': 'moveit_task_constructor_msgs', 'dependencies': OrderedDict()}
[0.001286] (moveit_task_constructor_msgs) JobStarted: {'identifier': 'moveit_task_constructor_msgs'}
[0.006488] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'cmake'}
[0.006944] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', '-DCMAKE_BUILD_TYPE=Release', '-DROSIDL_GENERATE_INTERFACES_SKIP_PYTHON=ON', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.019280] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.101248] (-) TimerEvent: {}
[0.123173] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)\n'}
[0.139161] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.140885] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.145156] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.154013] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.165267] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.175281] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)\n'}
[0.204576] (-) TimerEvent: {}
[0.297056] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.305365] (-) TimerEvent: {}
[0.305529] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[0.406351] (-) TimerEvent: {}
[0.507819] (-) TimerEvent: {}
[0.608173] (-) TimerEvent: {}
[0.651904] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.708476] (-) TimerEvent: {}
[0.809072] (-) TimerEvent: {}
[0.911686] (-) TimerEvent: {}
[1.012187] (-) TimerEvent: {}
[1.114459] (-) TimerEvent: {}
[1.214920] (-) TimerEvent: {}
[1.306599] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.317008] (-) TimerEvent: {}
[1.418783] (-) TimerEvent: {}
[1.519411] (-) TimerEvent: {}
[1.562259] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.563776] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:20 (find_package):\n'}
[1.563831] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[1.563866] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[1.563892] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[1.563917] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[1.563953] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.563980] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[1.564005] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[1.564030] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[1.564054] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.564078] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[1.577922] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[1.578140] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):\n'}
[1.578186] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[1.578214] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[1.578240] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[1.578265] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[1.578289] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.578314] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)\n'}
[1.578339] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[1.578363] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[1.578387] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[1.578413] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.578437] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[1.591047] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):\n'}
[1.591119] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules\n'}
[1.591149] (moveit_task_constructor_msgs) StderrLine: {'line': b'  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use\n'}
[1.591176] (moveit_task_constructor_msgs) StderrLine: {'line': b'  the cmake_policy command to set the policy and suppress this warning.\n'}
[1.591200] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[1.591224] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.591249] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake:23 (find_package)\n'}
[1.591275] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake:48 (include)\n'}
[1.591301] (moveit_task_constructor_msgs) StderrLine: {'line': b'  /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:286 (ament_execute_extensions)\n'}
[1.591325] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[1.591350] (moveit_task_constructor_msgs) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.591375] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[1.592464] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/local/bin/python3.6\n'}
[1.592527] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[1.592558] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so\n'}
[1.624436] (-) TimerEvent: {}
[1.727253] (-) TimerEvent: {}
[1.731252] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Configuring done (1.7s)\n'}
[1.802973] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[1.803129] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[33mCMake Warning:\n'}
[1.803166] (moveit_task_constructor_msgs) StderrLine: {'line': b'  Manually-specified variables were not used by the project:\n'}
[1.803194] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[1.803220] (moveit_task_constructor_msgs) StderrLine: {'line': b'    ROSIDL_GENERATE_INTERFACES_SKIP_PYTHON\n'}
[1.803246] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[1.803271] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[1.812364] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs\n'}
[1.818398] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 0}
[1.818604] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'build'}
[1.818618] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[1.827955] (-) TimerEvent: {}
[1.840347] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  0%] Built target moveit_task_constructor_msgs__cpp\n'}
[1.840474] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_moveit_task_constructor_msgs\n'}
[1.847018] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 12%] Built target moveit_task_constructor_msgs__rosidl_generator_c\n'}
[1.869960] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 24%] Built target moveit_task_constructor_msgs__rosidl_typesupport_cpp\n'}
[1.872785] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 36%] Built target moveit_task_constructor_msgs__rosidl_typesupport_c\n'}
[1.875143] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 48%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_c\n'}
[1.878538] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 60%] Built target moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp\n'}
[1.884682] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 82%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[1.885115] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c\n'}
[1.891367] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 83%] Built target moveit_task_constructor_msgs\n'}
[1.898679] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 84%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[1.933122] (-) TimerEvent: {}
[1.946358] (moveit_task_constructor_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n'}
[1.946456] (moveit_task_constructor_msgs) StderrLine: {'line': b'  File "/opt/ros/humble/share/rosidl_generator_py/cmake/../../../lib/rosidl_generator_py/rosidl_generator_py", line 8, in <module>\n'}
[1.946488] (moveit_task_constructor_msgs) StderrLine: {'line': b'    from rosidl_generator_py import generate_py\n'}
[1.946514] (moveit_task_constructor_msgs) StderrLine: {'line': b"ImportError: cannot import name 'generate_py'\n"}
[1.951054] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[2]: *** [/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/build.make:334: rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c] Error 1\n'}
[1.951190] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:725: moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.dir/all] Error 2\n'}
[1.951229] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake[1]: *** Waiting for unfinished jobs....\n'}
[1.952750] (moveit_task_constructor_msgs) StdoutLine: {'line': b'running egg_info\n'}
[1.953160] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing moveit_task_constructor_msgs.egg-info/PKG-INFO\n'}
[1.953769] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing dependency_links to moveit_task_constructor_msgs.egg-info/dependency_links.txt\n'}
[1.954057] (moveit_task_constructor_msgs) StdoutLine: {'line': b'writing top-level names to moveit_task_constructor_msgs.egg-info/top_level.txt\n'}
[1.956000] (moveit_task_constructor_msgs) StdoutLine: {'line': b"reading manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[1.956343] (moveit_task_constructor_msgs) StdoutLine: {'line': b"writing manifest file 'moveit_task_constructor_msgs.egg-info/SOURCES.txt'\n"}
[1.969399] (moveit_task_constructor_msgs) StdoutLine: {'line': b'[ 84%] Built target ament_cmake_python_build_moveit_task_constructor_msgs_egg\n'}
[1.969810] (moveit_task_constructor_msgs) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[1.971383] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 2}
[1.976317] (moveit_task_constructor_msgs) JobEnded: {'identifier': 'moveit_task_constructor_msgs', 'rc': 2}
[1.976896] (-) EventReactorShutdown: {}
