[0.017s] Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/srdfdom -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/srdfdom
[0.155s] -- The CXX compiler identification is GNU 11.4.0
[0.156s] -- Detecting CXX compiler ABI info
[0.156s] -- Detecting CXX compiler ABI info - done
[0.157s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.157s] -- Detecting CXX compile features
[0.159s] -- Detecting CXX compile features - done
[0.169s] [33mCMake Warning (dev) at CMakeLists.txt:8 (find_package):
[0.169s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.169s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.170s]   set the policy and suppress this warning.
[0.170s] 
[0.170s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.170s] [0m
[0.178s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0")
[0.201s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.293s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.353s] -- Found urdf: 2.6.1 (/opt/ros/humble/share/urdf/cmake)
[0.397s] -- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
[0.461s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[0.463s] -- Found ament_cmake_pytest: 1.3.12 (/opt/ros/humble/share/ament_cmake_pytest/cmake)
[0.643s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[0.679s] -- The C compiler identification is GNU 11.4.0
[0.685s] -- Detecting C compiler ABI info
[0.734s] -- Detecting C compiler ABI info - done
[0.744s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.744s] -- Detecting C compile features
[0.745s] -- Detecting C compile features - done
[0.748s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[0.748s]   Compatibility with CMake < 3.10 will be removed from a future version of
[0.748s]   CMake.
[0.748s] 
[0.748s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[0.748s]   to tell CMake that the project requires at least <min> but has been updated
[0.748s]   to work with policies introduced by <max> or earlier.
[0.748s] 
[0.748s] [0m
[0.753s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.795s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.795s] -- Found Threads: TRUE
[0.801s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.828s] -- Configuring done (0.8s)
[0.840s] -- Generating done (0.0s)
[0.845s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/srdfdom
[0.852s] Invoked command in '/home/<USER>/ws_moveit2/build/srdfdom' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/srdfdom -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/srdfdom
[0.852s] Invoking command in '/home/<USER>/ws_moveit2/build/srdfdom': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/srdfdom -- -j8 -l8
[0.867s] [  9%] [32mBuilding CXX object CMakeFiles/srdfdom.dir/src/model.cpp.o[0m
[0.869s] [ 18%] [32mBuilding CXX object CMakeFiles/srdfdom.dir/src/srdf_writer.cpp.o[0m
[0.871s] [ 18%] Built target ament_cmake_python_copy_srdfdom
[0.873s] [ 27%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o[0m
[0.880s] [ 36%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/src/gtest-all.cc.o[0m
[1.066s] running egg_info
[1.068s] creating srdfdom.egg-info
[1.068s] writing srdfdom.egg-info/PKG-INFO
[1.069s] writing dependency_links to srdfdom.egg-info/dependency_links.txt
[1.070s] writing top-level names to srdfdom.egg-info/top_level.txt
[1.076s] writing manifest file 'srdfdom.egg-info/SOURCES.txt'
[1.079s] reading manifest file 'srdfdom.egg-info/SOURCES.txt'
[1.081s] writing manifest file 'srdfdom.egg-info/SOURCES.txt'
[1.095s] [ 36%] Built target ament_cmake_python_build_srdfdom_egg
[1.648s] [ 45%] [32m[1mLinking CXX static library libgtest_main.a[0m
[1.661s] [ 45%] Built target gtest_main
[4.102s] [ 54%] [32m[1mLinking CXX shared library libsrdfdom.so[0m
[4.206s] [ 54%] Built target srdfdom
[10.570s] [ 63%] [32m[1mLinking CXX static library libgtest.a[0m
[10.641s] [ 63%] Built target gtest
[10.663s] [ 81%] [32mBuilding CXX object CMakeFiles/test_cpp_C.dir/test/test_parser.cpp.o[0m
[10.663s] [ 81%] [32mBuilding CXX object CMakeFiles/test_cpp_nl_NL.UTF-8.dir/test/test_parser.cpp.o[0m
[12.667s] [ 90%] [32m[1mLinking CXX executable test_cpp_C[0m
[12.717s] [ 90%] Built target test_cpp_C
[12.724s] [100%] [32m[1mLinking CXX executable test_cpp_nl_NL.UTF-8[0m
[12.773s] [100%] Built target test_cpp_nl_NL.UTF-8
