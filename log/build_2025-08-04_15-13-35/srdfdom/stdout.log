-- The CXX compiler identification is GNU 11.4.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0")
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
-- Found urdf: 2.6.1 (/opt/ros/humble/share/urdf/cmake)
-- Found TinyXML2 via Config file: TinyXML2_DIR-NOTFOUND
-- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
-- Found ament_cmake_pytest: 1.3.12 (/opt/ros/humble/share/ament_cmake_pytest/cmake)
-- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
-- The C compiler identification is GNU 11.4.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Configuring done (0.8s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ws_moveit2/build/srdfdom
[  9%] [32mBuilding CXX object CMakeFiles/srdfdom.dir/src/model.cpp.o[0m
[ 18%] [32mBuilding CXX object CMakeFiles/srdfdom.dir/src/srdf_writer.cpp.o[0m
[ 18%] Built target ament_cmake_python_copy_srdfdom
[ 27%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o[0m
[ 36%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/src/gtest-all.cc.o[0m
running egg_info
creating srdfdom.egg-info
writing srdfdom.egg-info/PKG-INFO
writing dependency_links to srdfdom.egg-info/dependency_links.txt
writing top-level names to srdfdom.egg-info/top_level.txt
writing manifest file 'srdfdom.egg-info/SOURCES.txt'
reading manifest file 'srdfdom.egg-info/SOURCES.txt'
writing manifest file 'srdfdom.egg-info/SOURCES.txt'
[ 36%] Built target ament_cmake_python_build_srdfdom_egg
[ 45%] [32m[1mLinking CXX static library libgtest_main.a[0m
[ 45%] Built target gtest_main
[ 54%] [32m[1mLinking CXX shared library libsrdfdom.so[0m
[ 54%] Built target srdfdom
[ 63%] [32m[1mLinking CXX static library libgtest.a[0m
[ 63%] Built target gtest
[ 81%] [32mBuilding CXX object CMakeFiles/test_cpp_C.dir/test/test_parser.cpp.o[0m
[ 81%] [32mBuilding CXX object CMakeFiles/test_cpp_nl_NL.UTF-8.dir/test/test_parser.cpp.o[0m
[ 90%] [32m[1mLinking CXX executable test_cpp_C[0m
[ 90%] Built target test_cpp_C
[100%] [32m[1mLinking CXX executable test_cpp_nl_NL.UTF-8[0m
[100%] Built target test_cpp_nl_NL.UTF-8
