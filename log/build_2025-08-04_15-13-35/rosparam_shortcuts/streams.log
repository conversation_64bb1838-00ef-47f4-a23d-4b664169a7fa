[0.053s] Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/rosparam_shortcuts -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rosparam_shortcuts
[0.087s] -- The C compiler identification is GNU 11.4.0
[0.137s] -- The CXX compiler identification is GNU 11.4.0
[0.142s] -- Detecting C compiler ABI info
[0.179s] -- Detecting C compiler ABI info - done
[0.197s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.197s] -- Detecting C compile features
[0.197s] -- Detecting C compile features - done
[0.205s] -- Detecting CXX compiler ABI info
[0.261s] -- Detecting CXX compiler ABI info - done
[0.269s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.269s] -- Detecting CXX compile features
[0.269s] -- Detecting CXX compile features - done
[0.276s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.383s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.575s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.619s] -- Found Eigen3: TRUE (found version "3.4.0")
[0.619s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.683s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.723s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.739s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.767s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.779s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.840s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.910s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.919s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.037s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.081s] -- Found FastRTPS: /opt/ros/humble/include
[1.169s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.177s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.257s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.257s] -- Found Threads: TRUE
[1.294s] -- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
[1.411s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.411s] -- Found ament_cmake_gtest: 1.3.12 (/opt/ros/humble/share/ament_cmake_gtest/cmake)
[1.413s] -- Found ros_testing: 0.4.0 (/opt/ros/humble/share/ros_testing/cmake)
[1.414s] -- Found launch_testing_ament_cmake: 1.0.10 (/opt/ros/humble/share/launch_testing_ament_cmake/cmake)
[1.416s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.416s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:52 (find_package):
[1.416s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.416s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.416s]   the cmake_policy command to set the policy and suppress this warning.
[1.417s] 
[1.417s] Call Stack (most recent call first):
[1.417s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.417s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.417s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.417s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.417s]   CMakeLists.txt:97 (find_package)
[1.417s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.417s] [0m
[1.459s] [33mCMake Warning (dev) at /opt/ros/humble/share/python_cmake_module/cmake/Modules/FindPythonExtra.cmake:140 (find_package):
[1.459s]   Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
[1.460s]   are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
[1.460s]   the cmake_policy command to set the policy and suppress this warning.
[1.460s] 
[1.460s] Call Stack (most recent call first):
[1.460s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmake-extras.cmake:18 (find_package)
[1.460s]   /opt/ros/humble/share/launch_testing_ament_cmake/cmake/launch_testing_ament_cmakeConfig.cmake:41 (include)
[1.460s]   /opt/ros/humble/share/ros_testing/cmake/ros_testing-extras.cmake:15 (find_package)
[1.460s]   /opt/ros/humble/share/ros_testing/cmake/ros_testingConfig.cmake:41 (include)
[1.460s]   CMakeLists.txt:97 (find_package)
[1.460s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.461s] [0m
[1.465s] -- Found PythonInterp: /usr/local/bin/python3.6 (found suitable version "3.6.15", minimum required is "3.6")
[1.499s] -- Found PythonLibs: /usr/lib/aarch64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5")
[1.500s] -- Using PYTHON_EXECUTABLE: /usr/local/bin/python3.6
[1.500s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.500s] -- Using PYTHON_LIBRARIES: /usr/lib/aarch64-linux-gnu/libpython3.10.so
[1.515s] -- Found PythonExtra: .so
[1.565s] -- Found gtest sources under '/opt/ros/humble/src/gtest_vendor': C++ tests using 'Google Test' will be built
[1.567s] [0mCMake Deprecation Warning at /opt/ros/humble/src/gtest_vendor/CMakeLists.txt:2 (cmake_minimum_required):
[1.567s]   Compatibility with CMake < 3.10 will be removed from a future version of
[1.567s]   CMake.
[1.567s] 
[1.567s]   Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
[1.567s]   to tell CMake that the project requires at least <min> but has been updated
[1.568s]   to work with policies introduced by <max> or earlier.
[1.568s] 
[1.568s] [0m
[1.570s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.571s] -- Added test 'lint_cmake' to check CMake code style
[1.571s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.572s] -- Added test 'xmllint' to check XML markup files
[1.575s] -- Configuring done (1.5s)
[1.599s] -- Generating done (0.0s)
[1.604s] -- Build files have been written to: /home/<USER>/ws_moveit2/build/rosparam_shortcuts
[1.612s] Invoked command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/rosparam_shortcuts -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/rosparam_shortcuts
[1.612s] Invoking command in '/home/<USER>/ws_moveit2/build/rosparam_shortcuts': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/ws_moveit2/build/rosparam_shortcuts -- -j8 -l8
[1.631s] [  7%] [32mBuilding CXX object CMakeFiles/rosparam_shortcuts.dir/src/rosparam_shortcuts.cpp.o[0m
[1.633s] [ 15%] [32mBuilding CXX object CMakeFiles/rosparam_shortcuts.dir/src/node_parameters.cpp.o[0m
[1.637s] [ 23%] [32mBuilding CXX object gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o[0m
[1.647s] [ 30%] [32mBuilding CXX object gtest/CMakeFiles/gtest.dir/src/gtest-all.cc.o[0m
[2.563s] [ 38%] [32m[1mLinking CXX static library libgtest_main.a[0m
[2.574s] [ 38%] Built target gtest_main
[9.116s] [ 46%] [32m[1mLinking CXX shared library librosparam_shortcuts.so[0m
[9.176s] [ 46%] Built target rosparam_shortcuts
[9.197s] [ 61%] [32mBuilding CXX object CMakeFiles/rosparam_shortcuts_example.dir/src/example.cpp.o[0m
[9.197s] [ 61%] [32mBuilding CXX object CMakeFiles/rosparam_shortcuts_node_parameters_example.dir/src/node_parameters_example.cpp.o[0m
[11.124s] [ 69%] [32m[1mLinking CXX static library libgtest.a[0m
[11.137s] [ 69%] Built target gtest
[11.143s] [ 76%] [32mBuilding CXX object CMakeFiles/test_node_parameters.dir/test/test_node_parameters.cpp.o[0m
[12.517s] [ 84%] [32m[1mLinking CXX executable example[0m
[12.579s] [ 84%] Built target rosparam_shortcuts_example
[12.599s] [ 92%] [32m[1mLinking CXX executable node_parameters_example[0m
[12.653s] [ 92%] Built target rosparam_shortcuts_node_parameters_example
[14.445s] [100%] [32m[1mLinking CXX executable test_node_parameters[0m
[14.507s] [100%] Built target test_node_parameters
