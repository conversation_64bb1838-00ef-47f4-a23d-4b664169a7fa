[0.000000] (-) TimerEvent: {}
[0.000330] (-) JobUnselected: {'identifier': 'chomp_motion_planner'}
[0.000362] (-) JobUnselected: {'identifier': 'launch_param_builder'}
[0.000373] (-) JobUnselected: {'identifier': 'moveit'}
[0.000385] (-) JobUnselected: {'identifier': 'moveit2_tutorials'}
[0.000393] (-) JobUnselected: {'identifier': 'moveit_chomp_optimizer_adapter'}
[0.000404] (-) JobUnselected: {'identifier': 'moveit_common'}
[0.000413] (-) JobUnselected: {'identifier': 'moveit_configs_utils'}
[0.000421] (-) JobUnselected: {'identifier': 'moveit_core'}
[0.000428] (-) JobUnselected: {'identifier': 'moveit_hybrid_planning'}
[0.000436] (-) JobUnselected: {'identifier': 'moveit_kinematics'}
[0.000443] (-) JobUnselected: {'identifier': 'moveit_planners'}
[0.000454] (-) JobUnselected: {'identifier': 'moveit_planners_chomp'}
[0.000464] (-) JobUnselected: {'identifier': 'moveit_planners_ompl'}
[0.000472] (-) JobUnselected: {'identifier': 'moveit_plugins'}
[0.000480] (-) JobUnselected: {'identifier': 'moveit_resources'}
[0.000487] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_description'}
[0.000495] (-) JobUnselected: {'identifier': 'moveit_resources_fanuc_moveit_config'}
[0.000502] (-) JobUnselected: {'identifier': 'moveit_resources_panda_description'}
[0.000513] (-) JobUnselected: {'identifier': 'moveit_resources_panda_moveit_config'}
[0.000524] (-) JobUnselected: {'identifier': 'moveit_resources_pr2_description'}
[0.000532] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_ikfast_manipulator_plugin'}
[0.000540] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_moveit_config'}
[0.000547] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_pg70_support'}
[0.000555] (-) JobUnselected: {'identifier': 'moveit_resources_prbt_support'}
[0.000562] (-) JobUnselected: {'identifier': 'moveit_ros'}
[0.000570] (-) JobUnselected: {'identifier': 'moveit_ros_benchmarks'}
[0.000581] (-) JobUnselected: {'identifier': 'moveit_ros_control_interface'}
[0.000588] (-) JobUnselected: {'identifier': 'moveit_ros_move_group'}
[0.000596] (-) JobUnselected: {'identifier': 'moveit_ros_occupancy_map_monitor'}
[0.000603] (-) JobUnselected: {'identifier': 'moveit_ros_perception'}
[0.000611] (-) JobUnselected: {'identifier': 'moveit_ros_planning'}
[0.000618] (-) JobUnselected: {'identifier': 'moveit_ros_planning_interface'}
[0.000690] (-) JobUnselected: {'identifier': 'moveit_ros_robot_interaction'}
[0.000710] (-) JobUnselected: {'identifier': 'moveit_ros_visualization'}
[0.000720] (-) JobUnselected: {'identifier': 'moveit_ros_warehouse'}
[0.000731] (-) JobUnselected: {'identifier': 'moveit_runtime'}
[0.000742] (-) JobUnselected: {'identifier': 'moveit_servo'}
[0.000753] (-) JobUnselected: {'identifier': 'moveit_setup_app_plugins'}
[0.000761] (-) JobUnselected: {'identifier': 'moveit_setup_assistant'}
[0.000769] (-) JobUnselected: {'identifier': 'moveit_setup_controllers'}
[0.000777] (-) JobUnselected: {'identifier': 'moveit_setup_core_plugins'}
[0.000784] (-) JobUnselected: {'identifier': 'moveit_setup_framework'}
[0.000792] (-) JobUnselected: {'identifier': 'moveit_setup_srdf_plugins'}
[0.000802] (-) JobUnselected: {'identifier': 'moveit_simple_controller_manager'}
[0.000812] (-) JobUnselected: {'identifier': 'moveit_task_constructor_capabilities'}
[0.000820] (-) JobUnselected: {'identifier': 'moveit_task_constructor_core'}
[0.000828] (-) JobUnselected: {'identifier': 'moveit_task_constructor_demo'}
[0.000835] (-) JobUnselected: {'identifier': 'moveit_task_constructor_visualization'}
[0.000843] (-) JobUnselected: {'identifier': 'moveit_visual_tools'}
[0.000851] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner'}
[0.000858] (-) JobUnselected: {'identifier': 'pilz_industrial_motion_planner_testutils'}
[0.000869] (-) JobUnselected: {'identifier': 'rosparam_shortcuts'}
[0.000877] (-) JobUnselected: {'identifier': 'rviz_marker_tools'}
[0.000884] (-) JobUnselected: {'identifier': 'srdfdom'}
[0.000893] (moveit_task_constructor_msgs) JobQueued: {'identifier': 'moveit_task_constructor_msgs', 'dependencies': OrderedDict()}
[0.000908] (moveit_task_constructor_msgs) JobStarted: {'identifier': 'moveit_task_constructor_msgs'}
[0.005998] (moveit_task_constructor_msgs) JobProgress: {'identifier': 'moveit_task_constructor_msgs', 'progress': 'cmake'}
[0.006386] (moveit_task_constructor_msgs) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '/home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs'], 'cwd': '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit2/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit2/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit2/install/moveit_servo/lib:/home/<USER>/ws_moveit2/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit2/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit2/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit2/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit2/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit2/install/moveit_kinematics/lib:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/home/<USER>/ws_moveit2/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit2/install/rosparam_shortcuts/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/ws_moveit2'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-f4ce213e-4e70-40eb-8d07-e2b19976c9f1.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/ws_moveit2/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-73a4d40cb9.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-658342232'), ('AMENT_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ws_moveit2/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/ws_moveit2/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit2/install/moveit_runtime:/home/<USER>/ws_moveit2/install/moveit:/home/<USER>/ws_moveit2/install/moveit_planners:/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit2/install/moveit_ros_control_interface:/home/<USER>/ws_moveit2/install/moveit_plugins:/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit2/install/moveit_setup_assistant:/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_controllers:/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit2/install/moveit_setup_framework:/home/<USER>/ws_moveit2/install/moveit_servo:/home/<USER>/ws_moveit2/install/moveit_ros:/home/<USER>/ws_moveit2/install/moveit_ros_visualization:/home/<USER>/ws_moveit2/install/moveit_hybrid_planning:/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit2/install/moveit_ros_warehouse:/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit2/install/moveit_ros_perception:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit2/install/moveit_ros_move_group:/home/<USER>/ws_moveit2/install/moveit_planners_ompl:/home/<USER>/ws_moveit2/install/moveit_ros_planning:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit2/install/moveit_planners_chomp:/home/<USER>/ws_moveit2/install/moveit_kinematics:/home/<USER>/ws_moveit2/install/moveit_chomp_optimizer_adapter:/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/rviz_marker_tools:/home/<USER>/ws_moveit2/install/rosparam_shortcuts:/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit2/install/moveit_resources:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit2/install/moveit_common:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.048571] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.089553] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.094932] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.100783] (-) TimerEvent: {}
[0.133380] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.142797] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.143042] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.143320] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.150207] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.190697] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.200353] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.200565] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.200872] (-) TimerEvent: {}
[0.201102] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.211708] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.284266] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter\n'}
[0.302448] (-) TimerEvent: {}
[0.384949] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)\n'}
[0.405128] (-) TimerEvent: {}
[0.432183] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.442293] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.457002] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.473813] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.496429] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.507411] (-) TimerEvent: {}
[0.519819] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)\n'}
[0.610300] (-) TimerEvent: {}
[0.688923] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.706590] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[0.711233] (-) TimerEvent: {}
[0.815956] (-) TimerEvent: {}
[0.884369] (moveit_task_constructor_msgs) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[0.884538] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:162 (message):\n'}
[0.884613] (moveit_task_constructor_msgs) StderrLine: {'line': b'  rosidl_generate_interfaces() the passed dependency\n'}
[0.884654] (moveit_task_constructor_msgs) StderrLine: {'line': b"  'SKIP_PYTHON_INTERFACE_GENERATION' has not been found before using\n"}
[0.884681] (moveit_task_constructor_msgs) StderrLine: {'line': b'  find_package()\n'}
[0.884706] (moveit_task_constructor_msgs) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.884732] (moveit_task_constructor_msgs) StderrLine: {'line': b'  CMakeLists.txt:32 (rosidl_generate_interfaces)\n'}
[0.884757] (moveit_task_constructor_msgs) StderrLine: {'line': b'\n'}
[0.884782] (moveit_task_constructor_msgs) StderrLine: {'line': b'\x1b[0m\n'}
[0.891344] (moveit_task_constructor_msgs) CommandEnded: {'returncode': 1}
[0.900804] (moveit_task_constructor_msgs) JobEnded: {'identifier': 'moveit_task_constructor_msgs', 'rc': 1}
[0.910678] (-) EventReactorShutdown: {}
