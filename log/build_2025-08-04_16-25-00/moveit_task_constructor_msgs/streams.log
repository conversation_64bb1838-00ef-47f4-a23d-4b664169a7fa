[0.006s] Invoking command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs
[0.048s] -- The C compiler identification is GNU 11.4.0
[0.089s] -- The CXX compiler identification is GNU 11.4.0
[0.094s] -- Detecting C compiler ABI info
[0.133s] -- Detecting C compiler ABI info - done
[0.142s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.142s] -- Detecting C compile features
[0.142s] -- Detecting C compile features - done
[0.149s] -- Detecting CXX compiler ABI info
[0.190s] -- Detecting CXX compiler ABI info - done
[0.199s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.200s] -- Detecting CXX compile features
[0.200s] -- Detecting CXX compile features - done
[0.211s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.283s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter
[0.384s] -- Found builtin_interfaces: 1.2.2 (/opt/ros/humble/share/builtin_interfaces/cmake)
[0.431s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.441s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.456s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.473s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.496s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.519s] -- Found moveit_msgs: 2.2.1 (/opt/ros/humble/share/moveit_msgs/cmake)
[0.688s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.706s] -- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[0.884s] -- Configuring incomplete, errors occurred!
[0.884s] [31mCMake Error at /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:162 (message):
[0.884s]   rosidl_generate_interfaces() the passed dependency
[0.884s]   'SKIP_PYTHON_INTERFACE_GENERATION' has not been found before using
[0.884s]   find_package()
[0.884s] Call Stack (most recent call first):
[0.884s]   CMakeLists.txt:32 (rosidl_generate_interfaces)
[0.884s] 
[0.884s] [0m
[0.891s] Invoked command in '/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_moveit2/install/moveit_configs_utils:/home/<USER>/ws_moveit2/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/ws_moveit2/src/moveit_task_constructor/msgs -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs
