{"backtraceGraph": {"commands": ["install", "ament_index_register_resource", "ament_cmake_environment_generate_package_run_dependencies_marker", "include", "ament_execute_extensions", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_environment_hooks", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake"], "nodes": [{"file": 4}, {"command": 5, "file": 4, "line": 10, "parent": 0}, {"command": 4, "file": 3, "line": 66, "parent": 1}, {"command": 3, "file": 2, "line": 48, "parent": 2}, {"file": 1, "parent": 3}, {"command": 2, "file": 1, "line": 47, "parent": 4}, {"command": 1, "file": 1, "line": 29, "parent": 5}, {"command": 0, "file": 0, "line": 105, "parent": 6}, {"command": 6, "file": 1, "line": 48, "parent": 4}, {"command": 1, "file": 1, "line": 43, "parent": 8}, {"command": 0, "file": 0, "line": 105, "parent": 9}, {"command": 3, "file": 2, "line": 48, "parent": 2}, {"file": 6, "parent": 11}, {"command": 7, "file": 6, "line": 20, "parent": 12}, {"command": 0, "file": 5, "line": 70, "parent": 13}, {"command": 0, "file": 5, "line": 87, "parent": 13}, {"command": 0, "file": 5, "line": 70, "parent": 13}, {"command": 0, "file": 5, "line": 87, "parent": 13}, {"command": 8, "file": 6, "line": 26, "parent": 12}, {"command": 0, "file": 7, "line": 91, "parent": 18}, {"command": 0, "file": 7, "line": 91, "parent": 18}, {"command": 0, "file": 7, "line": 91, "parent": 18}, {"command": 0, "file": 7, "line": 107, "parent": 18}, {"command": 0, "file": 7, "line": 119, "parent": 18}, {"command": 3, "file": 2, "line": 48, "parent": 2}, {"file": 9, "parent": 24}, {"command": 9, "file": 9, "line": 16, "parent": 25}, {"command": 1, "file": 8, "line": 29, "parent": 26}, {"command": 0, "file": 0, "line": 105, "parent": 27}, {"command": 10, "file": 3, "line": 68, "parent": 1}, {"command": 0, "file": 3, "line": 150, "parent": 29}, {"command": 0, "file": 3, "line": 157, "parent": 29}]}, "installers": [{"backtrace": 7, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/moveit_plugins"], "type": "file"}, {"backtrace": 10, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/moveit_plugins"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "share/moveit_plugins/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 15, "component": "Unspecified", "destination": "share/moveit_plugins/environment", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 16, "component": "Unspecified", "destination": "share/moveit_plugins/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 17, "component": "Unspecified", "destination": "share/moveit_plugins/environment", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 19, "component": "Unspecified", "destination": "share/moveit_plugins", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "share/moveit_plugins", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 21, "component": "Unspecified", "destination": "share/moveit_plugins", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 22, "component": "Unspecified", "destination": "share/moveit_plugins", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 23, "component": "Unspecified", "destination": "share/moveit_plugins", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 28, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_index/share/ament_index/resource_index/packages/moveit_plugins"], "type": "file"}, {"backtrace": 30, "component": "Unspecified", "destination": "share/moveit_plugins/cmake", "paths": ["/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_core/moveit_pluginsConfig.cmake", "/home/<USER>/ws_moveit2/build/moveit_plugins/ament_cmake_core/moveit_pluginsConfig-version.cmake"], "type": "file"}, {"backtrace": 31, "component": "Unspecified", "destination": "share/moveit_plugins", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}