{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-40d0ed1a607176073935.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "moveit_plugins", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "moveit_plugins_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-moveit_plugins_uninstall-Release-83f7fac2cbcb713a673b.json", "name": "moveit_plugins_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-Release-c347d75cd2990d7c5f25.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ws_moveit2/build/moveit_plugins", "source": "/home/<USER>/ws_moveit2/src/moveit2/moveit_plugins/moveit_plugins"}, "version": {"major": 2, "minor": 8}}