AMENT_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common:/opt/ros/humble
BROWSER=/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/helpers/browser.sh
CMAKE_PREFIX_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner:/home/<USER>/ws_moveit2/install/moveit_core:/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit2/install/srdfdom:/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit2/install/moveit_resources_panda_description:/home/<USER>/ws_moveit2/install/moveit_common
COLCON=1
COLORTERM=truecolor
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/502/bus
DISPLAY=:1
GIT_ASKPASS=/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass.sh
HOME=/home/<USER>
LANG=C.UTF-8
LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib
LOGNAME=mac
PATH=/home/<USER>/ws_moveit2/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks
PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:
PWD=/home/<USER>/ws_moveit2/build/moveit_chomp_optimizer_adapter
PYTHONPATH=/home/<USER>/ws_moveit2/install/srdfdom/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
ROS_DISTRO=humble
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SHELL=/bin/bash
SHLVL=2
SSH_AUTH_SOCK=/run/user/502/vscode-ssh-auth-sock-371497857
SSH_CONNECTION=::1 0 ::1 22
SSL_CERT_DIR=/usr/lib/ssl/certs
SSL_CERT_FILE=/usr/lib/ssl/certs/ca-certificates.crt
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.102.3
USER=mac
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/node
VSCODE_GIT_IPC_HANDLE=/run/user/502/vscode-git-73a4d40cb9.sock
VSCODE_IPC_HOOK_CLI=/run/user/502/vscode-ipc-971d3301-ef23-4334-b9e8-1cefe209e976.sock
XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop
XDG_RUNTIME_DIR=/run/user/502
XDG_SESSION_CLASS=user
XDG_SESSION_TYPE=tty
_=/usr/bin/colcon
