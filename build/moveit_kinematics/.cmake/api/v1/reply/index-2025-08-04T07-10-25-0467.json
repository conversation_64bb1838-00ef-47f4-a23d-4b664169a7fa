{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/local/cmake/bin/cmake", "cpack": "/usr/local/cmake/bin/cpack", "ctest": "/usr/local/cmake/bin/ctest", "root": "/usr/local/cmake/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 0, "string": "4.0.0-rc5", "suffix": "rc5"}}, "objects": [{"jsonFile": "codemodel-v2-2cae11637114df5cd03c.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-2cae11637114df5cd03c.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}}}}