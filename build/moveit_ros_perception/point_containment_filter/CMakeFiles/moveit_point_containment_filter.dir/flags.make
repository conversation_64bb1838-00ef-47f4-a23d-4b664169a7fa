# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DBOOST_ALL_NO_LIB -DBOOST_FILESYSTEM_DYN_LINK -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DRCUTILS_ENABLE_FAULT_INJECTION -Dmoveit_point_containment_filter_EXPORTS

CXX_INCLUDES = -I/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/depth_image_octomap_updater/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/lazy_free_space_updater/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/mesh_filter/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/point_containment_filter/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/pointcloud_octomap_updater/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/semantic_world/include -isystem /usr/include/eigen3 -isystem /usr/local/include/opencv4 -isystem /opt/ros/humble/include/rclcpp -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/geometric_shapes -isystem /opt/ros/humble/include -isystem /opt/ros/humble/include/ament_index_cpp -isystem /opt/ros/humble/include/libstatistics_collector -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/humble/include/rcl -isystem /opt/ros/humble/include/rcl_interfaces -isystem /opt/ros/humble/include/rcl_logging_interface -isystem /opt/ros/humble/include/rcl_yaml_param_parser -isystem /opt/ros/humble/include/libyaml_vendor -isystem /opt/ros/humble/include/tracetools -isystem /opt/ros/humble/include/rcpputils -isystem /opt/ros/humble/include/statistics_msgs -isystem /opt/ros/humble/include/rosgraph_msgs -isystem /opt/ros/humble/include/rosidl_typesupport_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_c -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/std_msgs -isystem /opt/ros/humble/include/eigen_stl_containers -isystem /opt/ros/humble/include/resource_retriever -isystem /opt/ros/humble/include/shape_msgs -isystem /opt/ros/humble/include/visualization_msgs

CXX_FLAGS = -O3 -DNDEBUG -std=c++17 -fPIC  -fopenmp -Wall -Wextra -Wwrite-strings -Wunreachable-code -Wpointer-arith -Wredundant-decls -Wcast-qual -Wno-maybe-uninitialized

