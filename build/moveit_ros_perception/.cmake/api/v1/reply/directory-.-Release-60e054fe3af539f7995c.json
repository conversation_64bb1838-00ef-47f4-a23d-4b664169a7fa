{"backtraceGraph": {"commands": ["install", "ament_environment_hooks", "_ament_cmake_export_libraries_register_environment_hook", "ament_export_targets", "pluginlib_export_plugin_description_file", "ament_index_register_resource", "ament_cmake_environment_generate_package_run_dependencies_marker", "include", "ament_execute_extensions", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/humble/share/ament_cmake_export_libraries/cmake/ament_cmake_export_libraries-extras.cmake", "/opt/ros/humble/share/ament_cmake_export_targets/cmake/ament_export_targets.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlib_export_plugin_description_file.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlib_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_export_targets/cmake/ament_cmake_export_targets_package_hook.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 113, "parent": 0}, {"command": 3, "file": 0, "line": 122, "parent": 0}, {"command": 2, "file": 3, "line": 59, "parent": 2}, {"command": 1, "file": 2, "line": 25, "parent": 3}, {"command": 0, "file": 1, "line": 70, "parent": 4}, {"command": 0, "file": 1, "line": 87, "parent": 4}, {"command": 4, "file": 0, "line": 125, "parent": 0}, {"command": 0, "file": 4, "line": 89, "parent": 7}, {"command": 4, "file": 0, "line": 126, "parent": 0}, {"command": 0, "file": 4, "line": 89, "parent": 9}, {"command": 9, "file": 0, "line": 140, "parent": 0}, {"command": 8, "file": 8, "line": 66, "parent": 11}, {"command": 7, "file": 7, "line": 48, "parent": 12}, {"file": 6, "parent": 13}, {"command": 6, "file": 6, "line": 47, "parent": 14}, {"command": 5, "file": 6, "line": 29, "parent": 15}, {"command": 0, "file": 5, "line": 105, "parent": 16}, {"command": 10, "file": 6, "line": 48, "parent": 14}, {"command": 5, "file": 6, "line": 43, "parent": 18}, {"command": 0, "file": 5, "line": 105, "parent": 19}, {"command": 7, "file": 7, "line": 48, "parent": 12}, {"file": 9, "parent": 21}, {"command": 1, "file": 9, "line": 20, "parent": 22}, {"command": 0, "file": 1, "line": 70, "parent": 23}, {"command": 0, "file": 1, "line": 87, "parent": 23}, {"command": 0, "file": 1, "line": 70, "parent": 23}, {"command": 0, "file": 1, "line": 87, "parent": 23}, {"command": 11, "file": 9, "line": 26, "parent": 22}, {"command": 0, "file": 10, "line": 91, "parent": 28}, {"command": 0, "file": 10, "line": 91, "parent": 28}, {"command": 0, "file": 10, "line": 91, "parent": 28}, {"command": 0, "file": 10, "line": 107, "parent": 28}, {"command": 0, "file": 10, "line": 119, "parent": 28}, {"command": 7, "file": 7, "line": 48, "parent": 12}, {"file": 12, "parent": 34}, {"command": 12, "file": 12, "line": 16, "parent": 35}, {"command": 5, "file": 11, "line": 29, "parent": 36}, {"command": 0, "file": 5, "line": 105, "parent": 37}, {"command": 7, "file": 7, "line": 48, "parent": 12}, {"file": 13, "parent": 39}, {"command": 5, "file": 13, "line": 22, "parent": 40}, {"command": 0, "file": 5, "line": 105, "parent": 41}, {"command": 7, "file": 7, "line": 48, "parent": 12}, {"file": 14, "parent": 43}, {"command": 0, "file": 14, "line": 28, "parent": 44}, {"command": 13, "file": 8, "line": 68, "parent": 11}, {"command": 0, "file": 8, "line": 122, "parent": 46}, {"command": 0, "file": 8, "line": 122, "parent": 46}, {"command": 0, "file": 8, "line": 122, "parent": 46}, {"command": 0, "file": 8, "line": 150, "parent": 46}, {"command": 0, "file": 8, "line": 157, "parent": 46}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["depth_image_octomap_updater/libmoveit_depth_image_octomap_updater.so.2.5.9"], "targetId": "moveit_depth_image_octomap_updater::@79273e13a890ae3c421a", "targetIndex": 0, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["depth_image_octomap_updater/libmoveit_depth_image_octomap_updater.so"], "targetId": "moveit_depth_image_octomap_updater::@79273e13a890ae3c421a", "targetIndex": 0, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["depth_image_octomap_updater/libmoveit_depth_image_octomap_updater_core.so.2.5.9"], "targetId": "moveit_depth_image_octomap_updater_core::@79273e13a890ae3c421a", "targetIndex": 1, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["depth_image_octomap_updater/libmoveit_depth_image_octomap_updater_core.so"], "targetId": "moveit_depth_image_octomap_updater_core::@79273e13a890ae3c421a", "targetIndex": 1, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["lazy_free_space_updater/libmoveit_lazy_free_space_updater.so.2.5.9"], "targetId": "moveit_lazy_free_space_updater::@636bffe7f9a8d0cf893e", "targetIndex": 2, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["lazy_free_space_updater/libmoveit_lazy_free_space_updater.so"], "targetId": "moveit_lazy_free_space_updater::@636bffe7f9a8d0cf893e", "targetIndex": 2, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["mesh_filter/libmoveit_mesh_filter.so.2.5.9"], "targetId": "moveit_mesh_filter::@360edf22541e2aeb11dc", "targetIndex": 3, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["mesh_filter/libmoveit_mesh_filter.so"], "targetId": "moveit_mesh_filter::@360edf22541e2aeb11dc", "targetIndex": 3, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["point_containment_filter/libmoveit_point_containment_filter.so.2.5.9"], "targetId": "moveit_point_containment_filter::@35254f429e5b01ce593e", "targetIndex": 4, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["point_containment_filter/libmoveit_point_containment_filter.so"], "targetId": "moveit_point_containment_filter::@35254f429e5b01ce593e", "targetIndex": 4, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["pointcloud_octomap_updater/libmoveit_pointcloud_octomap_updater.so.2.5.9"], "targetId": "moveit_pointcloud_octomap_updater::@efeb61feb1844232124d", "targetIndex": 5, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["pointcloud_octomap_updater/libmoveit_pointcloud_octomap_updater.so"], "targetId": "moveit_pointcloud_octomap_updater::@efeb61feb1844232124d", "targetIndex": 5, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["pointcloud_octomap_updater/libmoveit_pointcloud_octomap_updater_core.so.2.5.9"], "targetId": "moveit_pointcloud_octomap_updater_core::@efeb61feb1844232124d", "targetIndex": 6, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["pointcloud_octomap_updater/libmoveit_pointcloud_octomap_updater_core.so"], "targetId": "moveit_pointcloud_octomap_updater_core::@efeb61feb1844232124d", "targetIndex": 6, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["semantic_world/libmoveit_semantic_world.so.2.5.9"], "targetId": "moveit_semantic_world::@353bfbdd7e50113a3631", "targetIndex": 8, "targetInstallNamelink": "skip", "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["semantic_world/libmoveit_semantic_world.so"], "targetId": "moveit_semantic_world::@353bfbdd7e50113a3631", "targetIndex": 8, "targetInstallNamelink": "only", "type": "target"}, {"backtrace": 5, "component": "Unspecified", "destination": "share/moveit_ros_perception/environment", "paths": ["/opt/ros/humble/lib/python3.10/site-packages/ament_package/template/environment_hook/library_path.sh"], "type": "file"}, {"backtrace": 6, "component": "Unspecified", "destination": "share/moveit_ros_perception/environment", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/library_path.dsv"], "type": "file"}, {"backtrace": 8, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["pointcloud_octomap_updater_plugin_description.xml"], "type": "file"}, {"backtrace": 10, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["depth_image_octomap_updater_plugin_description.xml"], "type": "file"}, {"backtrace": 17, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/moveit_ros_perception"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/moveit_ros_perception"], "type": "file"}, {"backtrace": 24, "component": "Unspecified", "destination": "share/moveit_ros_perception/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 25, "component": "Unspecified", "destination": "share/moveit_ros_perception/environment", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "share/moveit_ros_perception/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 27, "component": "Unspecified", "destination": "share/moveit_ros_perception/environment", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 29, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 30, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 31, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 32, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 33, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 38, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_index/share/ament_index/resource_index/packages/moveit_ros_perception"], "type": "file"}, {"backtrace": 42, "component": "Unspecified", "destination": "share/ament_index/resource_index/moveit_ros_occupancy_map_monitor__pluginlib__plugin", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_index/share/ament_index/resource_index/moveit_ros_occupancy_map_monitor__pluginlib__plugin/moveit_ros_perception"], "type": "file"}, {"backtrace": 45, "component": "Unspecified", "destination": "share/moveit_ros_perception/cmake", "exportName": "export_moveit_ros_perception", "exportTargets": [{"id": "moveit_depth_image_octomap_updater::@79273e13a890ae3c421a", "index": 0}, {"id": "moveit_depth_image_octomap_updater_core::@79273e13a890ae3c421a", "index": 1}, {"id": "moveit_lazy_free_space_updater::@636bffe7f9a8d0cf893e", "index": 2}, {"id": "moveit_mesh_filter::@360edf22541e2aeb11dc", "index": 3}, {"id": "moveit_point_containment_filter::@35254f429e5b01ce593e", "index": 4}, {"id": "moveit_pointcloud_octomap_updater::@efeb61feb1844232124d", "index": 5}, {"id": "moveit_pointcloud_octomap_updater_core::@efeb61feb1844232124d", "index": 6}, {"id": "moveit_semantic_world::@353bfbdd7e50113a3631", "index": 8}], "paths": ["CMakeFiles/Export/51ba1277fb606299a378192a3004c82d/export_moveit_ros_perceptionExport.cmake"], "type": "export"}, {"backtrace": 47, "component": "Unspecified", "destination": "share/moveit_ros_perception/cmake", "paths": ["ConfigExtras.cmake"], "type": "file"}, {"backtrace": 48, "component": "Unspecified", "destination": "share/moveit_ros_perception/cmake", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_export_targets/ament_cmake_export_targets-extras.cmake"], "type": "file"}, {"backtrace": 49, "component": "Unspecified", "destination": "share/moveit_ros_perception/cmake", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_export_dependencies/ament_cmake_export_dependencies-extras.cmake"], "type": "file"}, {"backtrace": 50, "component": "Unspecified", "destination": "share/moveit_ros_perception/cmake", "paths": ["/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_core/moveit_ros_perceptionConfig.cmake", "/home/<USER>/ws_moveit2/build/moveit_ros_perception/ament_cmake_core/moveit_ros_perceptionConfig-version.cmake"], "type": "file"}, {"backtrace": 51, "component": "Unspecified", "destination": "share/moveit_ros_perception", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}