{"artifacts": [{"path": "lazy_free_space_updater/libmoveit_lazy_free_space_updater.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "set_property", "boost_find_component", "find_dependency", "add_compile_options", "moveit_package", "include_directories", "target_include_directories"], "files": ["lazy_free_space_updater/CMakeLists.txt", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/cmake/export_moveit_ros_occupancy_map_monitorExport.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/cmake/moveit_ros_occupancy_map_monitorConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/export_moveit_coreExport.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library-extras.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_libraryConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/export_tf2_eigenExport.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/tf2_eigenConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake/urdfdomExport.cmake", "/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake/urdfdom-config.cmake", "/opt/ros/humble/share/urdf/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/urdf/cmake/urdfConfig.cmake", "/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.74.0/boost_thread-config.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindBoost.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgsConfig.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/parameter_traits/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/parameter_traits/cmake/parameter_traitsConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fcl/fcl-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fcl/fcl-config.cmake", "/opt/ros/humble/share/octomap/octomap-targets.cmake", "/opt/ros/humble/share/octomap/octomap-config.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgsConfig.cmake", "/opt/ros/humble/share/cv_bridge/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/cv_bridge/cmake/cv_bridgeConfig.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/export_geometric_shapesExport.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverExport.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverConfig.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindCURL.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendor-extras.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendorConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_package.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 113, "parent": 2}, {"command": 3, "file": 0, "line": 10, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 4}, {"command": 6, "file": 1, "line": 53, "parent": 2}, {"file": 5, "parent": 6}, {"command": 5, "file": 5, "line": 41, "parent": 7}, {"file": 4, "parent": 8}, {"command": 5, "file": 4, "line": 9, "parent": 9}, {"file": 3, "parent": 10}, {"command": 4, "file": 3, "line": 61, "parent": 11}, {"command": 6, "file": 1, "line": 40, "parent": 2}, {"file": 8, "parent": 13}, {"command": 5, "file": 8, "line": 41, "parent": 14}, {"file": 7, "parent": 15}, {"command": 5, "file": 7, "line": 9, "parent": 16}, {"file": 6, "parent": 17}, {"command": 4, "file": 6, "line": 78, "parent": 18}, {"command": 5, "file": 8, "line": 41, "parent": 14}, {"file": 14, "parent": 20}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 13, "parent": 22}, {"command": 5, "file": 13, "line": 41, "parent": 23}, {"file": 12, "parent": 24}, {"command": 6, "file": 12, "line": 33, "parent": 25}, {"file": 11, "parent": 26}, {"command": 5, "file": 11, "line": 41, "parent": 27}, {"file": 10, "parent": 28}, {"command": 5, "file": 10, "line": 9, "parent": 29}, {"file": 9, "parent": 30}, {"command": 4, "file": 9, "line": 56, "parent": 31}, {"command": 4, "file": 6, "line": 61, "parent": 18}, {"command": 4, "file": 6, "line": 119, "parent": 18}, {"command": 4, "file": 6, "line": 70, "parent": 18}, {"command": 4, "file": 6, "line": 239, "parent": 18}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 17, "parent": 37}, {"command": 5, "file": 17, "line": 41, "parent": 38}, {"file": 16, "parent": 39}, {"command": 5, "file": 16, "line": 9, "parent": 40}, {"file": 15, "parent": 41}, {"command": 4, "file": 15, "line": 56, "parent": 42}, {"command": 5, "file": 17, "line": 41, "parent": 38}, {"file": 21, "parent": 44}, {"command": 6, "file": 21, "line": 21, "parent": 45}, {"file": 20, "parent": 46}, {"command": 5, "file": 20, "line": 41, "parent": 47}, {"file": 19, "parent": 48}, {"command": 5, "file": 19, "line": 9, "parent": 49}, {"file": 18, "parent": 50}, {"command": 4, "file": 18, "line": 56, "parent": 51}, {"command": 5, "file": 20, "line": 41, "parent": 47}, {"file": 25, "parent": 53}, {"command": 6, "file": 25, "line": 21, "parent": 54}, {"file": 24, "parent": 55}, {"command": 5, "file": 24, "line": 41, "parent": 56}, {"file": 23, "parent": 57}, {"command": 5, "file": 23, "line": 9, "parent": 58}, {"file": 22, "parent": 59}, {"command": 4, "file": 22, "line": 56, "parent": 60}, {"command": 4, "file": 6, "line": 94, "parent": 18}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 31, "parent": 63}, {"command": 5, "file": 31, "line": 41, "parent": 64}, {"file": 30, "parent": 65}, {"command": 6, "file": 30, "line": 21, "parent": 66}, {"file": 29, "parent": 67}, {"command": 5, "file": 29, "line": 41, "parent": 68}, {"file": 28, "parent": 69}, {"command": 6, "file": 28, "line": 21, "parent": 70}, {"file": 27, "parent": 71}, {"command": 5, "file": 27, "line": 42, "parent": 72}, {"file": 26, "parent": 73}, {"command": 4, "file": 26, "line": 77, "parent": 74}, {"command": 4, "file": 6, "line": 135, "parent": 18}, {"command": 5, "file": 8, "line": 41, "parent": 14}, {"file": 35, "parent": 77}, {"command": 6, "file": 35, "line": 3, "parent": 78}, {"file": 34, "parent": 79}, {"command": 6, "file": 34, "line": 609, "parent": 80}, {"file": 33, "parent": 81}, {"command": 8, "file": 33, "line": 258, "parent": 82}, {"command": 6, "file": 33, "line": 141, "parent": 83}, {"file": 32, "parent": 84}, {"command": 7, "file": 32, "line": 101, "parent": 85}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 38, "parent": 87}, {"command": 5, "file": 38, "line": 41, "parent": 88}, {"file": 37, "parent": 89}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 36, "parent": 91}, {"command": 4, "file": 36, "line": 56, "parent": 92}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 39, "parent": 94}, {"command": 4, "file": 39, "line": 56, "parent": 95}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 40, "parent": 97}, {"command": 4, "file": 40, "line": 56, "parent": 98}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 41, "parent": 100}, {"command": 4, "file": 41, "line": 56, "parent": 101}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 42, "parent": 103}, {"command": 4, "file": 42, "line": 56, "parent": 104}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 43, "parent": 106}, {"command": 4, "file": 43, "line": 56, "parent": 107}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 44, "parent": 109}, {"command": 4, "file": 44, "line": 56, "parent": 110}, {"command": 5, "file": 37, "line": 9, "parent": 90}, {"file": 45, "parent": 112}, {"command": 4, "file": 45, "line": 56, "parent": 113}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 48, "parent": 115}, {"command": 5, "file": 48, "line": 41, "parent": 116}, {"file": 47, "parent": 117}, {"command": 5, "file": 47, "line": 9, "parent": 118}, {"file": 46, "parent": 119}, {"command": 4, "file": 46, "line": 56, "parent": 120}, {"command": 6, "file": 12, "line": 30, "parent": 25}, {"file": 53, "parent": 122}, {"command": 5, "file": 53, "line": 41, "parent": 123}, {"file": 52, "parent": 124}, {"command": 6, "file": 52, "line": 21, "parent": 125}, {"file": 51, "parent": 126}, {"command": 5, "file": 51, "line": 41, "parent": 127}, {"file": 50, "parent": 128}, {"command": 5, "file": 50, "line": 9, "parent": 129}, {"file": 49, "parent": 130}, {"command": 4, "file": 49, "line": 56, "parent": 131}, {"command": 5, "file": 51, "line": 41, "parent": 127}, {"file": 59, "parent": 133}, {"command": 6, "file": 59, "line": 21, "parent": 134}, {"file": 58, "parent": 135}, {"command": 5, "file": 58, "line": 41, "parent": 136}, {"file": 57, "parent": 137}, {"command": 6, "file": 57, "line": 21, "parent": 138}, {"file": 56, "parent": 139}, {"command": 5, "file": 56, "line": 41, "parent": 140}, {"file": 55, "parent": 141}, {"command": 5, "file": 55, "line": 9, "parent": 142}, {"file": 54, "parent": 143}, {"command": 4, "file": 54, "line": 56, "parent": 144}, {"command": 6, "file": 12, "line": 29, "parent": 25}, {"file": 61, "parent": 146}, {"command": 5, "file": 61, "line": 37, "parent": 147}, {"file": 60, "parent": 148}, {"command": 4, "file": 60, "line": 66, "parent": 149}, {"command": 5, "file": 56, "line": 41, "parent": 140}, {"file": 65, "parent": 151}, {"command": 6, "file": 65, "line": 21, "parent": 152}, {"file": 64, "parent": 153}, {"command": 5, "file": 64, "line": 41, "parent": 154}, {"file": 63, "parent": 155}, {"command": 5, "file": 63, "line": 9, "parent": 156}, {"file": 62, "parent": 157}, {"command": 4, "file": 62, "line": 56, "parent": 158}, {"command": 4, "file": 6, "line": 102, "parent": 18}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 67, "parent": 161}, {"command": 5, "file": 67, "line": 52, "parent": 162}, {"file": 66, "parent": 163}, {"command": 4, "file": 66, "line": 66, "parent": 164}, {"command": 9, "file": 67, "line": 50, "parent": 162}, {"command": 6, "file": 70, "line": 78, "parent": 166}, {"file": 69, "parent": 167}, {"command": 5, "file": 69, "line": 77, "parent": 168}, {"file": 68, "parent": 169}, {"command": 4, "file": 68, "line": 69, "parent": 170}, {"command": 6, "file": 1, "line": 39, "parent": 2}, {"file": 75, "parent": 172}, {"command": 5, "file": 75, "line": 41, "parent": 173}, {"file": 74, "parent": 174}, {"command": 6, "file": 74, "line": 21, "parent": 175}, {"file": 73, "parent": 176}, {"command": 5, "file": 73, "line": 41, "parent": 177}, {"file": 72, "parent": 178}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 71, "parent": 180}, {"command": 4, "file": 71, "line": 56, "parent": 181}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 76, "parent": 183}, {"command": 4, "file": 76, "line": 56, "parent": 184}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 77, "parent": 186}, {"command": 4, "file": 77, "line": 56, "parent": 187}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 78, "parent": 189}, {"command": 4, "file": 78, "line": 56, "parent": 190}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 79, "parent": 192}, {"command": 4, "file": 79, "line": 56, "parent": 193}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 80, "parent": 195}, {"command": 4, "file": 80, "line": 56, "parent": 196}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 81, "parent": 198}, {"command": 4, "file": 81, "line": 56, "parent": 199}, {"command": 6, "file": 14, "line": 21, "parent": 21}, {"file": 84, "parent": 201}, {"command": 5, "file": 84, "line": 41, "parent": 202}, {"file": 83, "parent": 203}, {"command": 5, "file": 83, "line": 9, "parent": 204}, {"file": 82, "parent": 205}, {"command": 4, "file": 82, "line": 56, "parent": 206}, {"command": 5, "file": 84, "line": 41, "parent": 202}, {"file": 88, "parent": 208}, {"command": 6, "file": 88, "line": 21, "parent": 209}, {"file": 87, "parent": 210}, {"command": 5, "file": 87, "line": 41, "parent": 211}, {"file": 86, "parent": 212}, {"command": 5, "file": 86, "line": 9, "parent": 213}, {"file": 85, "parent": 214}, {"command": 4, "file": 85, "line": 56, "parent": 215}, {"command": 5, "file": 87, "line": 41, "parent": 211}, {"file": 92, "parent": 217}, {"command": 6, "file": 92, "line": 21, "parent": 218}, {"file": 91, "parent": 219}, {"command": 5, "file": 91, "line": 41, "parent": 220}, {"file": 90, "parent": 221}, {"command": 6, "file": 90, "line": 7, "parent": 222}, {"file": 89, "parent": 223}, {"command": 7, "file": 89, "line": 252, "parent": 224}, {"command": 5, "file": 72, "line": 9, "parent": 179}, {"file": 93, "parent": 226}, {"command": 4, "file": 93, "line": 56, "parent": 227}, {"command": 5, "file": 73, "line": 41, "parent": 177}, {"file": 99, "parent": 229}, {"command": 6, "file": 99, "line": 21, "parent": 230}, {"file": 98, "parent": 231}, {"command": 5, "file": 98, "line": 41, "parent": 232}, {"file": 97, "parent": 233}, {"command": 6, "file": 97, "line": 21, "parent": 234}, {"file": 96, "parent": 235}, {"command": 5, "file": 96, "line": 41, "parent": 236}, {"file": 95, "parent": 237}, {"command": 5, "file": 95, "line": 9, "parent": 238}, {"file": 94, "parent": 239}, {"command": 4, "file": 94, "line": 56, "parent": 240}, {"command": 6, "file": 97, "line": 21, "parent": 234}, {"file": 104, "parent": 242}, {"command": 5, "file": 104, "line": 41, "parent": 243}, {"file": 103, "parent": 244}, {"command": 6, "file": 103, "line": 21, "parent": 245}, {"file": 102, "parent": 246}, {"command": 5, "file": 102, "line": 41, "parent": 247}, {"file": 101, "parent": 248}, {"command": 5, "file": 101, "line": 9, "parent": 249}, {"file": 100, "parent": 250}, {"command": 4, "file": 100, "line": 56, "parent": 251}, {"command": 11, "file": 1, "line": 6, "parent": 2}, {"command": 10, "file": 105, "line": 46, "parent": 253}, {"command": 10, "file": 105, "line": 67, "parent": 253}, {"command": 12, "file": 1, "line": 94, "parent": 2}, {"command": 12, "file": 1, "line": 95, "parent": 2}, {"command": 13, "file": 2, "line": 141, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -std=c++17 -fPIC"}, {"fragment": " -fopenmp"}, {"backtrace": 254, "fragment": "-Wall"}, {"backtrace": 254, "fragment": "-Wextra"}, {"backtrace": 254, "fragment": "-Wwrite-strings"}, {"backtrace": 254, "fragment": "-Wunreachable-code"}, {"backtrace": 254, "fragment": "-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"backtrace": 254, "fragment": "-Wredundant-decls"}, {"backtrace": 254, "fragment": "-Wcast-qual"}, {"backtrace": 255, "fragment": "-Wno-maybe-uninitialized"}], "defines": [{"backtrace": 5, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 5, "define": "BOOST_ATOMIC_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_CHRONO_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_REGEX_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 5, "define": "BOOST_THREAD_DYN_LINK"}, {"backtrace": 5, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 5, "define": "FMT_LOCALE"}, {"backtrace": 5, "define": "FMT_SHARED"}, {"backtrace": 5, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "moveit_lazy_free_space_updater_EXPORTS"}], "includes": [{"backtrace": 256, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/depth_image_octomap_updater/include"}, {"backtrace": 256, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/lazy_free_space_updater/include"}, {"backtrace": 256, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/mesh_filter/include"}, {"backtrace": 256, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/point_containment_filter/include"}, {"backtrace": 256, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/pointcloud_octomap_updater/include"}, {"backtrace": 256, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/semantic_world/include"}, {"backtrace": 257, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 257, "isSystem": true, "path": "/usr/local/include/opencv4"}, {"backtrace": 258, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/include"}, {"backtrace": 258, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 258, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 258, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 5, "isSystem": true, "path": "/usr/include/bullet"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/moveit_core/include"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/urdf"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/urdf_parser_plugin"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom_headers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/eigen_stl_containers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tf2_eigen"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/geometric_shapes"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/resource_retriever"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/angles"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/parameter_traits"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rsl"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/kdl_parser"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5, 5], "standard": "17"}, "sourceIndexes": [0]}], "id": "moveit_lazy_free_space_updater::@636bffe7f9a8d0cf893e", "install": {"destinations": [{"backtrace": 3, "path": "lib"}, {"backtrace": 3, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_ros_perception"}}, "link": {"commandFragments": [{"fragment": "-fopenmp", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/opt/ros/humble/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:/opt/ros/humble/lib/aarch64-linux-gnu:", "role": "libraries"}, {"backtrace": 5, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib/libmoveit_ros_occupancy_map_monitor.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libcollision_detector_bullet_plugin.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_butterworth_filter.so.2.5.9", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librsl.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_distance_field.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_detection_bullet.so.2.5.9", "role": "libraries"}, {"backtrace": 33, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletDynamics.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletCollision.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/usr/lib/aarch64-linux-gnu/libLinearMath.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletSoftBody.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_dynamics_solver.so.2.5.9", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libkdl_parser.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_constraint_samplers.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_distance_field.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_kinematics_metrics.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_planning_interface.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_planning_request_adapter.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_planning_scene.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_kinematic_constraints.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_detection_fcl.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_detection.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_smoothing_base.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_test_utils.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_trajectory_processing.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_robot_trajectory.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_robot_state.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_robot_model.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_exceptions.so.2.5.9", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_kinematics_base.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so.2.0.8", "role": "libraries"}, {"backtrace": 34, "fragment": "/usr/lib/aarch64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liburdf.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/libruckig.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_transforms.so.2.5.9", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_sensor.so.3.0", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_model_state.so.3.0", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_model.so.3.0", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_world.so.3.0", "role": "libraries"}, {"backtrace": 75, "fragment": "/usr/lib/aarch64-linux-gnu/libtinyxml.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_utils.so.2.5.9", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_iostreams.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_regex.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_serialization.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_system.so.1.74.0", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_thread.so.1.74.0", "role": "libraries"}, {"backtrace": 86, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.74.0", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 93, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 93, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 93, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 93, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/usr/lib/aarch64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libgeometric_shapes.so.2.3.2", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 145, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1", "role": "libraries"}, {"backtrace": 150, "fragment": "-Wl,--as-needed", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 159, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 160, "fragment": "/usr/lib/aarch64-linux-gnu/libfcl.so.0.7.0", "role": "libraries"}, {"backtrace": 165, "fragment": "/usr/lib/aarch64-linux-gnu/libccd.so", "role": "libraries"}, {"backtrace": 165, "fragment": "/usr/lib/aarch64-linux-gnu/libm.so", "role": "libraries"}, {"backtrace": 165, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so.1.9.8", "role": "libraries"}, {"backtrace": 171, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so.1.9.8", "role": "libraries"}, {"backtrace": 93, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 182, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 182, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 182, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 96, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 185, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 185, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 99, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 188, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 188, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 182, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 182, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 188, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 102, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 191, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 191, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 191, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 185, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 194, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 194, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 194, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 197, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 197, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 207, "fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 207, "fragment": "/opt/ros/humble/lib/libresource_retriever.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 216, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 225, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 207, "fragment": "/opt/ros/humble/lib/librandom_numbers.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 228, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 228, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 241, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 252, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "moveit_lazy_free_space_updater", "nameOnDisk": "libmoveit_lazy_free_space_updater.so", "paths": {"build": "lazy_free_space_updater", "source": "lazy_free_space_updater"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "lazy_free_space_updater/src/lazy_free_space_updater.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}