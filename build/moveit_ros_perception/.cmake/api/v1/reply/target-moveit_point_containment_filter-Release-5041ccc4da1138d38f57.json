{"artifacts": [{"path": "point_containment_filter/libmoveit_point_containment_filter.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "set_property", "add_compile_options", "moveit_package", "include_directories", "target_include_directories"], "files": ["point_containment_filter/CMakeLists.txt", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/parameter_traits/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/parameter_traits/cmake/parameter_traitsConfig.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library-extras.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_libraryConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/export_geometric_shapesExport.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverExport.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverConfig.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindCURL.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendor-extras.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendorConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgsConfig.cmake", "/opt/ros/humble/share/cv_bridge/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/cv_bridge/cmake/cv_bridgeConfig.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/sensor_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_package.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 113, "parent": 2}, {"command": 3, "file": 0, "line": 7, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 4}, {"command": 6, "file": 1, "line": 40, "parent": 2}, {"file": 11, "parent": 6}, {"command": 5, "file": 11, "line": 41, "parent": 7}, {"file": 10, "parent": 8}, {"command": 6, "file": 10, "line": 21, "parent": 9}, {"file": 9, "parent": 10}, {"command": 5, "file": 9, "line": 41, "parent": 11}, {"file": 8, "parent": 12}, {"command": 6, "file": 8, "line": 30, "parent": 13}, {"file": 7, "parent": 14}, {"command": 5, "file": 7, "line": 41, "parent": 15}, {"file": 6, "parent": 16}, {"command": 6, "file": 6, "line": 21, "parent": 17}, {"file": 5, "parent": 18}, {"command": 5, "file": 5, "line": 41, "parent": 19}, {"file": 4, "parent": 20}, {"command": 5, "file": 4, "line": 9, "parent": 21}, {"file": 3, "parent": 22}, {"command": 4, "file": 3, "line": 56, "parent": 23}, {"command": 5, "file": 5, "line": 41, "parent": 19}, {"file": 17, "parent": 25}, {"command": 6, "file": 17, "line": 21, "parent": 26}, {"file": 16, "parent": 27}, {"command": 5, "file": 16, "line": 41, "parent": 28}, {"file": 15, "parent": 29}, {"command": 6, "file": 15, "line": 21, "parent": 30}, {"file": 14, "parent": 31}, {"command": 5, "file": 14, "line": 41, "parent": 32}, {"file": 13, "parent": 33}, {"command": 5, "file": 13, "line": 9, "parent": 34}, {"file": 12, "parent": 35}, {"command": 4, "file": 12, "line": 56, "parent": 36}, {"command": 5, "file": 14, "line": 41, "parent": 32}, {"file": 21, "parent": 38}, {"command": 6, "file": 21, "line": 21, "parent": 39}, {"file": 20, "parent": 40}, {"command": 5, "file": 20, "line": 41, "parent": 41}, {"file": 19, "parent": 42}, {"command": 5, "file": 19, "line": 9, "parent": 43}, {"file": 18, "parent": 44}, {"command": 4, "file": 18, "line": 56, "parent": 45}, {"command": 6, "file": 10, "line": 21, "parent": 9}, {"file": 24, "parent": 47}, {"command": 5, "file": 24, "line": 41, "parent": 48}, {"file": 23, "parent": 49}, {"command": 5, "file": 23, "line": 9, "parent": 50}, {"file": 22, "parent": 51}, {"command": 4, "file": 22, "line": 56, "parent": 52}, {"command": 5, "file": 24, "line": 41, "parent": 48}, {"file": 28, "parent": 54}, {"command": 6, "file": 28, "line": 21, "parent": 55}, {"file": 27, "parent": 56}, {"command": 5, "file": 27, "line": 41, "parent": 57}, {"file": 26, "parent": 58}, {"command": 5, "file": 26, "line": 9, "parent": 59}, {"file": 25, "parent": 60}, {"command": 4, "file": 25, "line": 56, "parent": 61}, {"command": 5, "file": 27, "line": 41, "parent": 57}, {"file": 32, "parent": 63}, {"command": 6, "file": 32, "line": 21, "parent": 64}, {"file": 31, "parent": 65}, {"command": 5, "file": 31, "line": 41, "parent": 66}, {"file": 30, "parent": 67}, {"command": 6, "file": 30, "line": 7, "parent": 68}, {"file": 29, "parent": 69}, {"command": 7, "file": 29, "line": 252, "parent": 70}, {"command": 6, "file": 1, "line": 39, "parent": 2}, {"file": 37, "parent": 72}, {"command": 5, "file": 37, "line": 41, "parent": 73}, {"file": 36, "parent": 74}, {"command": 6, "file": 36, "line": 21, "parent": 75}, {"file": 35, "parent": 76}, {"command": 5, "file": 35, "line": 41, "parent": 77}, {"file": 34, "parent": 78}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 33, "parent": 80}, {"command": 4, "file": 33, "line": 56, "parent": 81}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 38, "parent": 83}, {"command": 4, "file": 38, "line": 56, "parent": 84}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 39, "parent": 86}, {"command": 4, "file": 39, "line": 56, "parent": 87}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 40, "parent": 89}, {"command": 4, "file": 40, "line": 56, "parent": 90}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 41, "parent": 92}, {"command": 4, "file": 41, "line": 56, "parent": 93}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 42, "parent": 95}, {"command": 4, "file": 42, "line": 56, "parent": 96}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 43, "parent": 98}, {"command": 4, "file": 43, "line": 56, "parent": 99}, {"command": 5, "file": 34, "line": 9, "parent": 79}, {"file": 44, "parent": 101}, {"command": 4, "file": 44, "line": 56, "parent": 102}, {"command": 5, "file": 35, "line": 41, "parent": 77}, {"file": 50, "parent": 104}, {"command": 6, "file": 50, "line": 21, "parent": 105}, {"file": 49, "parent": 106}, {"command": 5, "file": 49, "line": 41, "parent": 107}, {"file": 48, "parent": 108}, {"command": 6, "file": 48, "line": 21, "parent": 109}, {"file": 47, "parent": 110}, {"command": 5, "file": 47, "line": 41, "parent": 111}, {"file": 46, "parent": 112}, {"command": 5, "file": 46, "line": 9, "parent": 113}, {"file": 45, "parent": 114}, {"command": 4, "file": 45, "line": 56, "parent": 115}, {"command": 6, "file": 48, "line": 21, "parent": 109}, {"file": 55, "parent": 117}, {"command": 5, "file": 55, "line": 41, "parent": 118}, {"file": 54, "parent": 119}, {"command": 6, "file": 54, "line": 21, "parent": 120}, {"file": 53, "parent": 121}, {"command": 5, "file": 53, "line": 41, "parent": 122}, {"file": 52, "parent": 123}, {"command": 5, "file": 52, "line": 9, "parent": 124}, {"file": 51, "parent": 125}, {"command": 4, "file": 51, "line": 56, "parent": 126}, {"command": 9, "file": 1, "line": 6, "parent": 2}, {"command": 8, "file": 56, "line": 46, "parent": 128}, {"command": 8, "file": 56, "line": 67, "parent": 128}, {"command": 10, "file": 1, "line": 94, "parent": 2}, {"command": 10, "file": 1, "line": 95, "parent": 2}, {"command": 11, "file": 2, "line": 141, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -std=c++17 -fPIC"}, {"fragment": " -fopenmp"}, {"backtrace": 129, "fragment": "-Wall"}, {"backtrace": 129, "fragment": "-Wextra"}, {"backtrace": 129, "fragment": "-Wwrite-strings"}, {"backtrace": 129, "fragment": "-Wunreachable-code"}, {"backtrace": 129, "fragment": "-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"backtrace": 129, "fragment": "-Wredundant-decls"}, {"backtrace": 129, "fragment": "-Wcast-qual"}, {"backtrace": 130, "fragment": "-Wno-maybe-uninitialized"}], "defines": [{"backtrace": 5, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 5, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 5, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "moveit_point_containment_filter_EXPORTS"}], "includes": [{"backtrace": 131, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/depth_image_octomap_updater/include"}, {"backtrace": 131, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/lazy_free_space_updater/include"}, {"backtrace": 131, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/mesh_filter/include"}, {"backtrace": 131, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/point_containment_filter/include"}, {"backtrace": 131, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/pointcloud_octomap_updater/include"}, {"backtrace": 131, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/perception/semantic_world/include"}, {"backtrace": 132, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 132, "isSystem": true, "path": "/usr/local/include/opencv4"}, {"backtrace": 133, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 133, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 133, "isSystem": true, "path": "/opt/ros/humble/include/geometric_shapes"}, {"backtrace": 133, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/eigen_stl_containers"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/resource_retriever"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 5, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [5], "standard": "17"}, "sourceIndexes": [0]}], "id": "moveit_point_containment_filter::@35254f429e5b01ce593e", "install": {"destinations": [{"backtrace": 3, "path": "lib"}, {"backtrace": 3, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_ros_perception"}}, "link": {"commandFragments": [{"fragment": "-fopenmp", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:/opt/ros/humble/lib/aarch64-linux-gnu:", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libgeometric_shapes.so.2.3.2", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libresource_retriever.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 71, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 82, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 82, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 82, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 85, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 85, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 82, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 82, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 85, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 88, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 88, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 88, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 94, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 94, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 100, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 100, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 103, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 103, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 116, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 103, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 127, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 53, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/librandom_numbers.so", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib/aarch64-linux-gnu", "role": "libraries"}], "language": "CXX"}, "name": "moveit_point_containment_filter", "nameOnDisk": "libmoveit_point_containment_filter.so", "paths": {"build": "point_containment_filter", "source": "point_containment_filter"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "point_containment_filter/src/shape_mask.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}