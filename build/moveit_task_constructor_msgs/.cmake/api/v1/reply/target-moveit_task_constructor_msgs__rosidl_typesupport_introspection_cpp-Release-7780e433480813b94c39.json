{"artifacts": [{"path": "libmoveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "target_compile_options", "add_definitions", "target_include_directories", "set_property"], "files": ["/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cppConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cExport.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 32, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 93, "parent": 4}, {"command": 4, "file": 0, "line": 146, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 128, "parent": 4}, {"command": 5, "file": 0, "line": 121, "parent": 4}, {"command": 7, "file": 3, "line": 5, "parent": 0}, {"file": 10, "parent": 20}, {"command": 1, "file": 10, "line": 41, "parent": 21}, {"file": 9, "parent": 22}, {"command": 7, "file": 9, "line": 21, "parent": 23}, {"file": 8, "parent": 24}, {"command": 1, "file": 8, "line": 41, "parent": 25}, {"file": 7, "parent": 26}, {"command": 7, "file": 7, "line": 13, "parent": 27}, {"file": 6, "parent": 28}, {"command": 1, "file": 6, "line": 41, "parent": 29}, {"file": 5, "parent": 30}, {"command": 1, "file": 5, "line": 9, "parent": 31}, {"file": 4, "parent": 32}, {"command": 6, "file": 4, "line": 56, "parent": 33}, {"command": 7, "file": 9, "line": 21, "parent": 23}, {"file": 13, "parent": 35}, {"command": 1, "file": 13, "line": 41, "parent": 36}, {"file": 12, "parent": 37}, {"command": 1, "file": 12, "line": 9, "parent": 38}, {"file": 11, "parent": 39}, {"command": 6, "file": 11, "line": 56, "parent": 40}, {"command": 1, "file": 13, "line": 41, "parent": 36}, {"file": 17, "parent": 42}, {"command": 7, "file": 17, "line": 21, "parent": 43}, {"file": 16, "parent": 44}, {"command": 1, "file": 16, "line": 41, "parent": 45}, {"file": 15, "parent": 46}, {"command": 1, "file": 15, "line": 9, "parent": 47}, {"file": 14, "parent": 48}, {"command": 6, "file": 14, "line": 56, "parent": 49}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 18, "parent": 51}, {"command": 8, "file": 18, "line": 139, "parent": 52}, {"command": 9, "file": 0, "line": 107, "parent": 4}, {"command": 5, "file": 0, "line": 117, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 21, "parent": 56}, {"command": 7, "file": 21, "line": 21, "parent": 57}, {"file": 20, "parent": 58}, {"command": 1, "file": 20, "line": 41, "parent": 59}, {"file": 19, "parent": 60}, {"command": 10, "file": 19, "line": 25, "parent": 61}, {"command": 11, "file": 0, "line": 111, "parent": 4}, {"command": 12, "file": 0, "line": 103, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -std=gnu++17 -fPIC"}, {"backtrace": 54, "fragment": "-Wall"}, {"backtrace": 54, "fragment": "-Wextra"}, {"backtrace": 54, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 55, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_BUILDING_DLL"}, {"backtrace": 62, "define": "ROS_PACKAGE_NAME=\"moveit_task_constructor_msgs\""}], "includes": [{"backtrace": 63, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp"}, {"backtrace": 55, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_cpp"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 55, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 19, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 19, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}], "language": "CXX", "languageStandard": {"backtraces": [64], "standard": "17"}, "sourceIndexes": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}], "dependencies": [{"backtrace": 53, "id": "moveit_task_constructor_msgs__cpp::@6890427a1f51a3e7e1df"}], "id": "moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs"}}, "link": {"commandFragments": [{"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 50, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "moveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp", "nameOnDisk": "libmoveit_task_constructor_msgs__rosidl_typesupport_introspection_cpp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"name": "Source Files", "sourceIndexes": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}, {"name": "CMake Rules", "sourceIndexes": [24]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/property__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution_info__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_description__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_statistics__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_solution__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_trajectory__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_description__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_statistics__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/srv/detail/get_solution__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/action/detail/execute_task_solution__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/property__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/solution_info__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_description__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_description__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/srv/detail/get_solution__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_introspection_cpp/moveit_task_constructor_msgs/msg/detail/property__rosidl_typesupport_introspection_cpp.hpp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}