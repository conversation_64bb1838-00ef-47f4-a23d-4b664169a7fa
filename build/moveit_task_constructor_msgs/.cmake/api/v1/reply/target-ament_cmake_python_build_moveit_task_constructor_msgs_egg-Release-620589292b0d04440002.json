{"backtrace": 7, "backtraceGraph": {"commands": ["add_custom_target", "_ament_cmake_python_install_package", "ament_python_install_package", "include", "ament_execute_extensions", "rosidl_generate_interfaces"], "files": ["/opt/ros/humble/share/ament_cmake_python/cmake/ament_python_install_package.cmake", "/opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt"], "nodes": [{"file": 4}, {"command": 5, "file": 4, "line": 32, "parent": 0}, {"command": 4, "file": 3, "line": 286, "parent": 1}, {"command": 3, "file": 2, "line": 48, "parent": 2}, {"file": 1, "parent": 3}, {"command": 2, "file": 1, "line": 124, "parent": 4}, {"command": 1, "file": 0, "line": 39, "parent": 5}, {"command": 0, "file": 0, "line": 141, "parent": 6}]}, "dependencies": [{"id": "ament_cmake_python_copy_moveit_task_constructor_msgs::@6890427a1f51a3e7e1df"}], "id": "ament_cmake_python_build_moveit_task_constructor_msgs_egg::@6890427a1f51a3e7e1df", "name": "ament_cmake_python_build_moveit_task_constructor_msgs_egg", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 7, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/CMakeFiles/ament_cmake_python_build_moveit_task_constructor_msgs_egg", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/CMakeFiles/ament_cmake_python_build_moveit_task_constructor_msgs_egg.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}