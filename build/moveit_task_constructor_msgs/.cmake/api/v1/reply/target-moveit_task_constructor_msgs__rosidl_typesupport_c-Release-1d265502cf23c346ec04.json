{"artifacts": [{"path": "libmoveit_task_constructor_msgs__rosidl_typesupport_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_definitions"], "files": ["/opt/ros/humble/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 32, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 90, "parent": 4}, {"command": 4, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 120, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 4, "parent": 16}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 130, "parent": 4}, {"command": 5, "file": 0, "line": 123, "parent": 4}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 137, "parent": 17}, {"command": 7, "file": 3, "line": 5, "parent": 0}, {"file": 11, "parent": 36}, {"command": 1, "file": 11, "line": 41, "parent": 37}, {"file": 10, "parent": 38}, {"command": 7, "file": 10, "line": 21, "parent": 39}, {"file": 9, "parent": 40}, {"command": 1, "file": 9, "line": 41, "parent": 41}, {"file": 8, "parent": 42}, {"command": 7, "file": 8, "line": 21, "parent": 43}, {"file": 7, "parent": 44}, {"command": 1, "file": 7, "line": 41, "parent": 45}, {"file": 6, "parent": 46}, {"command": 1, "file": 6, "line": 9, "parent": 47}, {"file": 5, "parent": 48}, {"command": 6, "file": 5, "line": 56, "parent": 49}, {"command": 6, "file": 0, "line": 103, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 14, "parent": 52}, {"command": 7, "file": 14, "line": 21, "parent": 53}, {"file": 13, "parent": 54}, {"command": 1, "file": 13, "line": 41, "parent": 55}, {"file": 12, "parent": 56}, {"command": 8, "file": 12, "line": 25, "parent": 57}, {"command": 6, "file": 0, "line": 100, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -std=gnu++14 -fPIC"}, {"backtrace": 51, "fragment": "-Wall"}], "defines": [{"backtrace": 7, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_GENERATOR_C_BUILDING_DLL_moveit_task_constructor_msgs"}, {"backtrace": 58, "define": "ROS_PACKAGE_NAME=\"moveit_task_constructor_msgs\""}], "includes": [{"backtrace": 7, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}], "language": "CXX", "languageStandard": {"backtraces": [59], "standard": "14"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "dependencies": [{"backtrace": 7, "id": "moveit_task_constructor_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "moveit_task_constructor_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs"}}, "link": {"commandFragments": [{"fragment": "-Wl,-r<PERSON>,/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "libmoveit_task_constructor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 50, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "CXX"}, "name": "moveit_task_constructor_msgs__rosidl_typesupport_c", "nameOnDisk": "libmoveit_task_constructor_msgs__rosidl_typesupport_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"name": "CMake Rules", "sourceIndexes": [12]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/property__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/solution__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/solution_info__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/stage_description__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/stage_statistics__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/sub_solution__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/sub_trajectory__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/task_description__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/task_statistics__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/trajectory_execution_info__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/srv/get_solution__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/action/execute_task_solution__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_c/moveit_task_constructor_msgs/msg/property__type_support.cpp.rule", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}