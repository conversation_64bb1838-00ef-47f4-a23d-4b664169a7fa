{"artifacts": [{"path": "rosidl_generator_py/moveit_task_constructor_msgs/libmoveit_task_constructor_msgs__rosidl_generator_py.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "set_lib_properties", "add_definitions", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 32, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 166, "parent": 4}, {"command": 4, "file": 0, "line": 302, "parent": 4}, {"command": 5, "file": 0, "line": 175, "parent": 4}, {"command": 5, "file": 0, "line": 213, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 167, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 4, "parent": 18}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 5, "parent": 21}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 0, "line": 291, "parent": 4}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 4, "line": 130, "parent": 19}, {"command": 5, "file": 5, "line": 132, "parent": 22}, {"command": 5, "file": 5, "line": 137, "parent": 22}, {"command": 7, "file": 3, "line": 5, "parent": 0}, {"file": 12, "parent": 51}, {"command": 1, "file": 12, "line": 41, "parent": 52}, {"file": 11, "parent": 53}, {"command": 7, "file": 11, "line": 21, "parent": 54}, {"file": 10, "parent": 55}, {"command": 1, "file": 10, "line": 41, "parent": 56}, {"file": 9, "parent": 57}, {"command": 7, "file": 9, "line": 21, "parent": 58}, {"file": 8, "parent": 59}, {"command": 1, "file": 8, "line": 41, "parent": 60}, {"file": 7, "parent": 61}, {"command": 1, "file": 7, "line": 9, "parent": 62}, {"file": 6, "parent": 63}, {"command": 6, "file": 6, "line": 56, "parent": 64}, {"command": 8, "file": 0, "line": 169, "parent": 4}, {"command": 9, "file": 0, "line": 294, "parent": 4}, {"command": 6, "file": 0, "line": 156, "parent": 67}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 15, "parent": 69}, {"command": 7, "file": 15, "line": 21, "parent": 70}, {"file": 14, "parent": 71}, {"command": 1, "file": 14, "line": 41, "parent": 72}, {"file": 13, "parent": 73}, {"command": 10, "file": 13, "line": 25, "parent": 74}, {"command": 11, "file": 0, "line": 179, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -fPIC"}, {"backtrace": 68, "fragment": "-Wall"}, {"backtrace": 68, "fragment": "-Wextra"}], "defines": [{"backtrace": 14, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 75, "define": "ROS_PACKAGE_NAME=\"moveit_task_constructor_msgs\""}, {"define": "moveit_task_constructor_msgs__rosidl_generator_py_EXPORTS"}], "includes": [{"backtrace": 76, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_c"}, {"backtrace": 76, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py"}, {"backtrace": 76, "path": "/usr/include/python3.10"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 14, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "dependencies": [{"backtrace": 14, "id": "moveit_task_constructor_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 66, "id": "moveit_task_constructor_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 66, "id": "moveit_task_constructor_msgs__py::@764e44f95d02a36cd862"}], "id": "moveit_task_constructor_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs"}}, "link": {"commandFragments": [{"fragment": "-Wl,-r<PERSON>,/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 8, "fragment": "libmoveit_task_constructor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 14, "fragment": "libmoveit_task_constructor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 33, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 65, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "C"}, "name": "moveit_task_constructor_msgs__rosidl_generator_py", "nameOnDisk": "libmoveit_task_constructor_msgs__rosidl_generator_py.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_property_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_solution_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_solution_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_stage_description_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_stage_statistics_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_sub_solution_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_sub_trajectory_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_task_description_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_task_statistics_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/msg/_trajectory_execution_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/srv/_get_solution_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/action/_execute_task_solution_s.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}