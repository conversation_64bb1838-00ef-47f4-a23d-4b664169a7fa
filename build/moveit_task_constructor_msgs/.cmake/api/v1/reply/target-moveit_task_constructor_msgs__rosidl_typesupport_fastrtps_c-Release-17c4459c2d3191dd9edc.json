{"artifacts": [{"path": "libmoveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "target_compile_options", "add_definitions", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cExport.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 32, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 104, "parent": 4}, {"command": 4, "file": 0, "line": 167, "parent": 4}, {"command": 5, "file": 0, "line": 131, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 4, "parent": 16}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 115, "parent": 4}, {"command": 7, "file": 3, "line": 5, "parent": 0}, {"file": 9, "parent": 30}, {"command": 1, "file": 9, "line": 41, "parent": 31}, {"file": 8, "parent": 32}, {"command": 7, "file": 8, "line": 21, "parent": 33}, {"file": 7, "parent": 34}, {"command": 1, "file": 7, "line": 41, "parent": 35}, {"file": 6, "parent": 36}, {"command": 1, "file": 6, "line": 9, "parent": 37}, {"file": 5, "parent": 38}, {"command": 6, "file": 5, "line": 56, "parent": 39}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 5, "file": 4, "line": 132, "parent": 17}, {"command": 7, "file": 8, "line": 21, "parent": 33}, {"file": 12, "parent": 46}, {"command": 1, "file": 12, "line": 41, "parent": 47}, {"file": 11, "parent": 48}, {"command": 1, "file": 11, "line": 9, "parent": 49}, {"file": 10, "parent": 50}, {"command": 6, "file": 10, "line": 56, "parent": 51}, {"command": 1, "file": 12, "line": 41, "parent": 47}, {"file": 16, "parent": 53}, {"command": 7, "file": 16, "line": 21, "parent": 54}, {"file": 15, "parent": 55}, {"command": 1, "file": 15, "line": 41, "parent": 56}, {"file": 14, "parent": 57}, {"command": 1, "file": 14, "line": 9, "parent": 58}, {"file": 13, "parent": 59}, {"command": 6, "file": 13, "line": 56, "parent": 60}, {"command": 8, "file": 0, "line": 128, "parent": 4}, {"command": 7, "file": 0, "line": 21, "parent": 4}, {"file": 18, "parent": 63}, {"command": 1, "file": 18, "line": 41, "parent": 64}, {"file": 17, "parent": 65}, {"command": 9, "file": 17, "line": 25, "parent": 66}, {"command": 10, "file": 0, "line": 134, "parent": 4}, {"command": 6, "file": 0, "line": 110, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -std=gnu++14 -fPIC"}, {"backtrace": 62, "fragment": "-Wall"}, {"backtrace": 62, "fragment": "-Wextra"}, {"backtrace": 62, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 29, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_FASTRTPS_C_BUILDING_DLL_moveit_task_constructor_msgs"}, {"backtrace": 67, "define": "ROS_PACKAGE_NAME=\"moveit_task_constructor_msgs\""}], "includes": [{"backtrace": 68, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_c"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 29, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [69], "standard": "14"}, "sourceIndexes": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]}], "dependencies": [{"backtrace": 7, "id": "moveit_task_constructor_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_task_constructor_msgs"}}, "link": {"commandFragments": [{"fragment": "-Wl,-r<PERSON>,/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "libmoveit_task_constructor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 24, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 61, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "moveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c", "nameOnDisk": "libmoveit_task_constructor_msgs__rosidl_typesupport_fastrtps_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]}, {"name": "CMake Rules", "sourceIndexes": [24]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/property__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/property__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution_info__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/solution_info__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_description__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_description__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/stage_statistics__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_solution__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_solution__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/sub_trajectory__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_description__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_description__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_statistics__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/task_statistics__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/trajectory_execution_info__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/srv/detail/get_solution__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/srv/detail/get_solution__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/action/detail/execute_task_solution__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_typesupport_fastrtps_c/moveit_task_constructor_msgs/msg/detail/property__rosidl_typesupport_fastrtps_c.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}