{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}]}, "dependencies": [{"id": "moveit_task_constructor_msgs::@6890427a1f51a3e7e1df"}], "id": "moveit_task_constructor_msgs__py::@764e44f95d02a36cd862", "name": "moveit_task_constructor_msgs__py", "paths": {"build": "moveit_task_constructor_msgs__py", "source": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/moveit_task_constructor_msgs__py/CMakeFiles/moveit_task_constructor_msgs__py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_task_constructor_msgs/rosidl_generator_py/moveit_task_constructor_msgs/_moveit_task_constructor_msgs_s.ep.rosidl_typesupport_fastrtps_c.c.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}