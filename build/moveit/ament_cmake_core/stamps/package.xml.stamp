<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>moveit</name>
  <version>2.5.9</version>
  <description>Meta package that contains all essential packages of MoveIt 2</description>
  <maintainer email="henning<PERSON><PERSON><EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://moveit.ros.org</url>
  <url type="repository">https://github.com/ros-planning/moveit2</url>
  <url type="bugtracker">https://github.com/ros-planning/moveit2/issues</url>

  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="<EMAIL>">Dave Coleman</author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <!--
  TODO(#314): Enable after porting moveit_commander
  <exec_depend>moveit_commander</exec_depend>
  -->
  <exec_depend>moveit_core</exec_depend>
  <exec_depend>moveit_planners</exec_depend>
  <exec_depend>moveit_plugins</exec_depend>
  <exec_depend>moveit_ros</exec_depend>
  <exec_depend>moveit_setup_assistant</exec_depend>


  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
