# generated from colcon_core/shell/template/command_prefix.sh.em
. "/home/<USER>/ws_moveit2/install/launch_param_builder/share/launch_param_builder/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_description/share/moveit_resources_fanuc_description/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_panda_description/share/moveit_resources_panda_description/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_pr2_description/share/moveit_resources_pr2_description/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_prbt_support/share/moveit_resources_prbt_support/package.sh"
. "/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_configs_utils/share/moveit_configs_utils/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_fanuc_moveit_config/share/moveit_resources_fanuc_moveit_config/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_panda_moveit_config/share/moveit_resources_panda_moveit_config/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/package.sh"
. "/home/<USER>/ws_moveit2/install/chomp_motion_planner/share/chomp_motion_planner/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_prbt_ikfast_manipulator_plugin/share/moveit_resources_prbt_ikfast_manipulator_plugin/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_simple_controller_manager/share/moveit_simple_controller_manager/package.sh"
. "/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner_testutils/share/pilz_industrial_motion_planner_testutils/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_planners_chomp/share/moveit_planners_chomp/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_plugins/share/moveit_plugins/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_kinematics/share/moveit_kinematics/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_planners_ompl/share/moveit_planners_ompl/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_robot_interaction/share/moveit_ros_robot_interaction/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_warehouse/share/moveit_ros_warehouse/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_benchmarks/share/moveit_ros_benchmarks/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_move_group/share/moveit_ros_move_group/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_prbt_moveit_config/share/moveit_resources_prbt_moveit_config/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_resources_prbt_pg70_support/share/moveit_resources_prbt_pg70_support/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros_visualization/share/moveit_ros_visualization/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_ros/share/moveit_ros/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_setup_framework/share/moveit_setup_framework/package.sh"
. "/home/<USER>/ws_moveit2/install/pilz_industrial_motion_planner/share/pilz_industrial_motion_planner/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_planners/share/moveit_planners/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_setup_app_plugins/share/moveit_setup_app_plugins/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_setup_controllers/share/moveit_setup_controllers/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_setup_core_plugins/share/moveit_setup_core_plugins/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_setup_srdf_plugins/share/moveit_setup_srdf_plugins/package.sh"
. "/home/<USER>/ws_moveit2/install/moveit_setup_assistant/share/moveit_setup_assistant/package.sh"
