{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-21594a440ce40b71ebbe.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "moveit_ros", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "moveit_ros_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-moveit_ros_uninstall-Release-9baad9298ec2a101eef5.json", "name": "moveit_ros_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-Release-c2f1ed5f2310eefd1e38.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ws_moveit2/build/moveit_ros", "source": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/moveit_ros"}, "version": {"major": 2, "minor": 8}}