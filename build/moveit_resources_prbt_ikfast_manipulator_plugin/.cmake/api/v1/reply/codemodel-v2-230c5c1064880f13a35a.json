{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-cb210786f39d78e3186f.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "moveit_resources_prbt_ikfast_manipulator_plugin", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "moveit_resources_prbt_ikfast_manipulator_plugin_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-moveit_resources_prbt_ikfast_manipulator_plugin_uninstall-Release-ec0e06c1cdf8b0a13fad.json", "name": "moveit_resources_prbt_ikfast_manipulator_plugin_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "prbt_manipulator_moveit_ikfast_plugin::@6890427a1f51a3e7e1df", "jsonFile": "target-prbt_manipulator_moveit_ikfast_plugin-Release-9a6725e5b1be71b733d9.json", "name": "prbt_manipulator_moveit_ikfast_plugin", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-Release-7c7e2359322b844145db.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ws_moveit2/build/moveit_resources_prbt_ikfast_manipulator_plugin", "source": "/home/<USER>/ws_moveit2/src/moveit2/moveit_planners/test_configs/prbt_ikfast_manipulator_plugin"}, "version": {"major": 2, "minor": 8}}