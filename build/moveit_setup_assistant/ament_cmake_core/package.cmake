set(_AMENT_PACKAGE_NAME "moveit_setup_assistant")
set(moveit_setup_assistant_VERSION "2.5.9")
set(moveit_setup_assistant_MAINTAINER "<PERSON><PERSON> <he<PERSON><PERSON><PERSON><PERSON>@picknik.ai>, <PERSON> <<EMAIL>>, <PERSON> <r<PERSON><PERSON><PERSON>@techfak.uni-bielefeld.de>, MoveIt Release Team <<EMAIL>>, <PERSON>! <<EMAIL>>")
set(moveit_setup_assistant_BUILD_DEPENDS "ament_index_cpp" "pluginlib" "qtbase5-dev" "rclcpp" "moveit_setup_framework" "moveit_setup_srdf_plugins")
set(moveit_setup_assistant_BUILDTOOL_DEPENDS "ament_cmake")
set(moveit_setup_assistant_BUILD_EXPORT_DEPENDS "ament_index_cpp" "pluginlib" "qtbase5-dev" "rclcpp" "moveit_setup_framework" "moveit_setup_srdf_plugins")
set(moveit_setup_assistant_BUILDTO<PERSON>_EXPORT_DEPENDS )
set(moveit_setup_assistant_EXEC_DEPENDS "moveit_setup_controllers" "moveit_setup_core_plugins" "moveit_setup_app_plugins" "ament_index_cpp" "pluginlib" "qtbase5-dev" "rclcpp" "moveit_setup_framework" "moveit_setup_srdf_plugins")
set(moveit_setup_assistant_TEST_DEPENDS "ament_lint_auto" "ament_clang_format" "ament_cmake_lint_cmake" "ament_cmake_xmllint" "moveit_resources_panda_moveit_config" "ament_cmake_gtest")
set(moveit_setup_assistant_GROUP_DEPENDS )
set(moveit_setup_assistant_MEMBER_OF_GROUPS )
set(moveit_setup_assistant_DEPRECATED "")
set(moveit_setup_assistant_EXPORT_TAGS)
list(APPEND moveit_setup_assistant_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
