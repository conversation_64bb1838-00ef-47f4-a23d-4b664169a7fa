# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/cmake/bin/cmake

# The command to remove a file.
RM = /usr/local/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ws_moveit2/src/moveit2/moveit_setup_assistant/moveit_setup_assistant

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ws_moveit2/build/moveit_setup_assistant

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/local/cmake/bin/ctest $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/local/cmake/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/cmake/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/cmake/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/cmake/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/cmake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/cmake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/cmake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/cmake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ws_moveit2/build/moveit_setup_assistant/CMakeFiles /home/<USER>/ws_moveit2/build/moveit_setup_assistant//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ws_moveit2/build/moveit_setup_assistant/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant_uninstall

# Build rule for target.
moveit_setup_assistant_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant_uninstall
.PHONY : moveit_setup_assistant_uninstall

# fast build rule for target.
moveit_setup_assistant_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_uninstall.dir/build.make CMakeFiles/moveit_setup_assistant_uninstall.dir/build
.PHONY : moveit_setup_assistant_uninstall/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant

# Build rule for target.
moveit_setup_assistant: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant
.PHONY : moveit_setup_assistant

# fast build rule for target.
moveit_setup_assistant/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/build
.PHONY : moveit_setup_assistant/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant_updater

# Build rule for target.
moveit_setup_assistant_updater: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant_updater
.PHONY : moveit_setup_assistant_updater

# fast build rule for target.
moveit_setup_assistant_updater/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/build
.PHONY : moveit_setup_assistant_updater/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant_autogen_timestamp_deps

# Build rule for target.
moveit_setup_assistant_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant_autogen_timestamp_deps
.PHONY : moveit_setup_assistant_autogen_timestamp_deps

# fast build rule for target.
moveit_setup_assistant_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_autogen_timestamp_deps.dir/build.make CMakeFiles/moveit_setup_assistant_autogen_timestamp_deps.dir/build
.PHONY : moveit_setup_assistant_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant_autogen

# Build rule for target.
moveit_setup_assistant_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant_autogen
.PHONY : moveit_setup_assistant_autogen

# fast build rule for target.
moveit_setup_assistant_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_autogen.dir/build.make CMakeFiles/moveit_setup_assistant_autogen.dir/build
.PHONY : moveit_setup_assistant_autogen/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant_updater_autogen_timestamp_deps

# Build rule for target.
moveit_setup_assistant_updater_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant_updater_autogen_timestamp_deps
.PHONY : moveit_setup_assistant_updater_autogen_timestamp_deps

# fast build rule for target.
moveit_setup_assistant_updater_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater_autogen_timestamp_deps.dir/build.make CMakeFiles/moveit_setup_assistant_updater_autogen_timestamp_deps.dir/build
.PHONY : moveit_setup_assistant_updater_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named moveit_setup_assistant_updater_autogen

# Build rule for target.
moveit_setup_assistant_updater_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 moveit_setup_assistant_updater_autogen
.PHONY : moveit_setup_assistant_updater_autogen

# fast build rule for target.
moveit_setup_assistant_updater_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater_autogen.dir/build.make CMakeFiles/moveit_setup_assistant_updater_autogen.dir/build
.PHONY : moveit_setup_assistant_updater_autogen/fast

include/moveit_setup_assistant/moc_navigation_widget.o: include/moveit_setup_assistant/moc_navigation_widget.cpp.o
.PHONY : include/moveit_setup_assistant/moc_navigation_widget.o

# target to build an object file
include/moveit_setup_assistant/moc_navigation_widget.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_navigation_widget.cpp.o
.PHONY : include/moveit_setup_assistant/moc_navigation_widget.cpp.o

include/moveit_setup_assistant/moc_navigation_widget.i: include/moveit_setup_assistant/moc_navigation_widget.cpp.i
.PHONY : include/moveit_setup_assistant/moc_navigation_widget.i

# target to preprocess a source file
include/moveit_setup_assistant/moc_navigation_widget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_navigation_widget.cpp.i
.PHONY : include/moveit_setup_assistant/moc_navigation_widget.cpp.i

include/moveit_setup_assistant/moc_navigation_widget.s: include/moveit_setup_assistant/moc_navigation_widget.cpp.s
.PHONY : include/moveit_setup_assistant/moc_navigation_widget.s

# target to generate assembly for a file
include/moveit_setup_assistant/moc_navigation_widget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_navigation_widget.cpp.s
.PHONY : include/moveit_setup_assistant/moc_navigation_widget.cpp.s

include/moveit_setup_assistant/moc_setup_assistant_widget.o: include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.o
.PHONY : include/moveit_setup_assistant/moc_setup_assistant_widget.o

# target to build an object file
include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.o
.PHONY : include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.o

include/moveit_setup_assistant/moc_setup_assistant_widget.i: include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.i
.PHONY : include/moveit_setup_assistant/moc_setup_assistant_widget.i

# target to preprocess a source file
include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.i
.PHONY : include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.i

include/moveit_setup_assistant/moc_setup_assistant_widget.s: include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.s
.PHONY : include/moveit_setup_assistant/moc_setup_assistant_widget.s

# target to generate assembly for a file
include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.s
.PHONY : include/moveit_setup_assistant/moc_setup_assistant_widget.cpp.s

moveit_setup_assistant_autogen/mocs_compilation.o: moveit_setup_assistant_autogen/mocs_compilation.cpp.o
.PHONY : moveit_setup_assistant_autogen/mocs_compilation.o

# target to build an object file
moveit_setup_assistant_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/moveit_setup_assistant_autogen/mocs_compilation.cpp.o
.PHONY : moveit_setup_assistant_autogen/mocs_compilation.cpp.o

moveit_setup_assistant_autogen/mocs_compilation.i: moveit_setup_assistant_autogen/mocs_compilation.cpp.i
.PHONY : moveit_setup_assistant_autogen/mocs_compilation.i

# target to preprocess a source file
moveit_setup_assistant_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/moveit_setup_assistant_autogen/mocs_compilation.cpp.i
.PHONY : moveit_setup_assistant_autogen/mocs_compilation.cpp.i

moveit_setup_assistant_autogen/mocs_compilation.s: moveit_setup_assistant_autogen/mocs_compilation.cpp.s
.PHONY : moveit_setup_assistant_autogen/mocs_compilation.s

# target to generate assembly for a file
moveit_setup_assistant_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/moveit_setup_assistant_autogen/mocs_compilation.cpp.s
.PHONY : moveit_setup_assistant_autogen/mocs_compilation.cpp.s

moveit_setup_assistant_updater_autogen/mocs_compilation.o: moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.o
.PHONY : moveit_setup_assistant_updater_autogen/mocs_compilation.o

# target to build an object file
moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.o
.PHONY : moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.o

moveit_setup_assistant_updater_autogen/mocs_compilation.i: moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.i
.PHONY : moveit_setup_assistant_updater_autogen/mocs_compilation.i

# target to preprocess a source file
moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.i
.PHONY : moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.i

moveit_setup_assistant_updater_autogen/mocs_compilation.s: moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.s
.PHONY : moveit_setup_assistant_updater_autogen/mocs_compilation.s

# target to generate assembly for a file
moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.s
.PHONY : moveit_setup_assistant_updater_autogen/mocs_compilation.cpp.s

src/collisions_updater.o: src/collisions_updater.cpp.o
.PHONY : src/collisions_updater.o

# target to build an object file
src/collisions_updater.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/src/collisions_updater.cpp.o
.PHONY : src/collisions_updater.cpp.o

src/collisions_updater.i: src/collisions_updater.cpp.i
.PHONY : src/collisions_updater.i

# target to preprocess a source file
src/collisions_updater.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/src/collisions_updater.cpp.i
.PHONY : src/collisions_updater.cpp.i

src/collisions_updater.s: src/collisions_updater.cpp.s
.PHONY : src/collisions_updater.s

# target to generate assembly for a file
src/collisions_updater.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant_updater.dir/build.make CMakeFiles/moveit_setup_assistant_updater.dir/src/collisions_updater.cpp.s
.PHONY : src/collisions_updater.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/navigation_widget.o: src/navigation_widget.cpp.o
.PHONY : src/navigation_widget.o

# target to build an object file
src/navigation_widget.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/navigation_widget.cpp.o
.PHONY : src/navigation_widget.cpp.o

src/navigation_widget.i: src/navigation_widget.cpp.i
.PHONY : src/navigation_widget.i

# target to preprocess a source file
src/navigation_widget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/navigation_widget.cpp.i
.PHONY : src/navigation_widget.cpp.i

src/navigation_widget.s: src/navigation_widget.cpp.s
.PHONY : src/navigation_widget.s

# target to generate assembly for a file
src/navigation_widget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/navigation_widget.cpp.s
.PHONY : src/navigation_widget.cpp.s

src/setup_assistant_widget.o: src/setup_assistant_widget.cpp.o
.PHONY : src/setup_assistant_widget.o

# target to build an object file
src/setup_assistant_widget.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/setup_assistant_widget.cpp.o
.PHONY : src/setup_assistant_widget.cpp.o

src/setup_assistant_widget.i: src/setup_assistant_widget.cpp.i
.PHONY : src/setup_assistant_widget.i

# target to preprocess a source file
src/setup_assistant_widget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/setup_assistant_widget.cpp.i
.PHONY : src/setup_assistant_widget.cpp.i

src/setup_assistant_widget.s: src/setup_assistant_widget.cpp.s
.PHONY : src/setup_assistant_widget.s

# target to generate assembly for a file
src/setup_assistant_widget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/moveit_setup_assistant.dir/build.make CMakeFiles/moveit_setup_assistant.dir/src/setup_assistant_widget.cpp.s
.PHONY : src/setup_assistant_widget.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... moveit_setup_assistant_autogen"
	@echo "... moveit_setup_assistant_autogen_timestamp_deps"
	@echo "... moveit_setup_assistant_uninstall"
	@echo "... moveit_setup_assistant_updater_autogen"
	@echo "... moveit_setup_assistant_updater_autogen_timestamp_deps"
	@echo "... uninstall"
	@echo "... moveit_setup_assistant"
	@echo "... moveit_setup_assistant_updater"
	@echo "... include/moveit_setup_assistant/moc_navigation_widget.o"
	@echo "... include/moveit_setup_assistant/moc_navigation_widget.i"
	@echo "... include/moveit_setup_assistant/moc_navigation_widget.s"
	@echo "... include/moveit_setup_assistant/moc_setup_assistant_widget.o"
	@echo "... include/moveit_setup_assistant/moc_setup_assistant_widget.i"
	@echo "... include/moveit_setup_assistant/moc_setup_assistant_widget.s"
	@echo "... moveit_setup_assistant_autogen/mocs_compilation.o"
	@echo "... moveit_setup_assistant_autogen/mocs_compilation.i"
	@echo "... moveit_setup_assistant_autogen/mocs_compilation.s"
	@echo "... moveit_setup_assistant_updater_autogen/mocs_compilation.o"
	@echo "... moveit_setup_assistant_updater_autogen/mocs_compilation.i"
	@echo "... moveit_setup_assistant_updater_autogen/mocs_compilation.s"
	@echo "... src/collisions_updater.o"
	@echo "... src/collisions_updater.i"
	@echo "... src/collisions_updater.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/navigation_widget.o"
	@echo "... src/navigation_widget.i"
	@echo "... src/navigation_widget.s"
	@echo "... src/setup_assistant_widget.o"
	@echo "... src/setup_assistant_widget.i"
	@echo "... src/setup_assistant_widget.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

