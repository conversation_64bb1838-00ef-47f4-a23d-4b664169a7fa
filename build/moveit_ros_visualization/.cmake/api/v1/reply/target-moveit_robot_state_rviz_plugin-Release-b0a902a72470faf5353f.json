{"artifacts": [{"path": "robot_state_rviz_plugin/libmoveit_robot_state_rviz_plugin.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "set_property", "_populate_Widgets_target_properties", "find_dependency", "boost_find_component", "add_compile_options", "moveit_package", "add_definitions", "include_directories", "target_include_directories"], "files": ["robot_state_rviz_plugin/CMakeLists.txt", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "rviz_plugin_render_tools/CMakeLists.txt", "/opt/ros/humble/share/rviz_common/cmake/rviz_commonExport.cmake", "/opt/ros/humble/share/rviz_common/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rviz_common/cmake/rviz_commonConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Qt5/Qt5Config.cmake", "/opt/ros/humble/share/rviz_common/cmake/rviz_common-extras.cmake", "/opt/ros/humble/share/rviz_ogre_vendor/cmake/rviz_ogre_vendor-extras.cmake", "/opt/ros/humble/share/rviz_ogre_vendor/cmake/rviz_ogre_vendorConfig.cmake", "/opt/ros/humble/share/rviz_rendering/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rviz_rendering/cmake/rviz_renderingConfig.cmake", "/opt/ros/humble/share/rviz_common/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rviz_default_plugins/cmake/rviz_default_pluginsExport.cmake", "/opt/ros/humble/share/rviz_default_plugins/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rviz_default_plugins/cmake/rviz_default_pluginsConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/export_moveit_ros_planningExport.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/export_moveit_coreExport.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library-extras.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_libraryConfig.cmake", "/home/<USER>/ws_moveit2/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/export_pluginlibExport.cmake", "/opt/ros/humble/share/pluginlib/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_eigen/cmake/tf2_eigenConfig.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfig.cmake", "/opt/ros/humble/share/rcl_logging_spdlog/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_logging_spdlog/cmake/rcl_logging_spdlogConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fcl/fcl-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fcl/fcl-config.cmake", "/opt/ros/humble/share/octomap/octomap-targets.cmake", "/opt/ros/humble/share/octomap/octomap-config.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverExport.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverConfig.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindCURL.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendor-extras.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendorConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/export_geometric_shapesExport.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rviz_rendering/cmake/rviz_renderingExport.cmake", "/opt/ros/humble/share/rviz_rendering/cmake/ament_cmake_export_targets-extras.cmake", "/usr/lib/aarch64-linux-gnu/cmake/assimp-5.2/assimpTargets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/assimp-5.2/assimpConfig.cmake", "/opt/ros/humble/share/rviz_assimp_vendor/cmake/rviz_assimp_vendor-extras.cmake", "/opt/ros/humble/share/rviz_assimp_vendor/cmake/rviz_assimp_vendorConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake/urdfdomExport.cmake", "/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake/urdfdom-config.cmake", "/opt/ros/humble/share/urdf/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/urdf/cmake/urdfConfig.cmake", "/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/ws_moveit2/install/srdfdom/share/srdfdom/cmake/srdfdomConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.74.0/boost_thread-config.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindBoost.cmake", "/home/<USER>/ws_moveit2/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgsConfig.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/std_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/std_msgs/cmake/std_msgsConfig.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/geometry_msgsConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rcpputils/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcpputils/cmake/rcpputilsConfig.cmake", "/opt/ros/humble/share/class_loader/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/ws_moveit2/install/moveit_common/share/moveit_common/cmake/moveit_package.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 20, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 115, "parent": 2}, {"command": 2, "file": 0, "line": 22, "parent": 0}, {"command": 2, "file": 0, "line": 6, "parent": 0}, {"file": 3}, {"command": 3, "file": 3, "line": 33, "parent": 6}, {"command": 2, "file": 2, "line": 145, "parent": 7}, {"command": 6, "file": 1, "line": 32, "parent": 2}, {"file": 6, "parent": 9}, {"command": 5, "file": 6, "line": 41, "parent": 10}, {"file": 5, "parent": 11}, {"command": 5, "file": 5, "line": 9, "parent": 12}, {"file": 4, "parent": 13}, {"command": 4, "file": 4, "line": 56, "parent": 14}, {"command": 2, "file": 3, "line": 29, "parent": 6}, {"command": 5, "file": 6, "line": 41, "parent": 10}, {"file": 9, "parent": 17}, {"command": 6, "file": 9, "line": 30, "parent": 18}, {"file": 8, "parent": 19}, {"command": 6, "file": 8, "line": 28, "parent": 20}, {"file": 7, "parent": 21}, {"command": 8, "file": 7, "line": 213, "parent": 22}, {"command": 7, "file": 7, "line": 57, "parent": 23}, {"command": 5, "file": 6, "line": 41, "parent": 10}, {"file": 14, "parent": 25}, {"command": 6, "file": 14, "line": 21, "parent": 26}, {"file": 13, "parent": 27}, {"command": 5, "file": 13, "line": 41, "parent": 28}, {"file": 12, "parent": 29}, {"command": 6, "file": 12, "line": 21, "parent": 30}, {"file": 11, "parent": 31}, {"command": 5, "file": 11, "line": 41, "parent": 32}, {"file": 10, "parent": 33}, {"command": 2, "file": 10, "line": 130, "parent": 34}, {"command": 6, "file": 1, "line": 33, "parent": 2}, {"file": 17, "parent": 36}, {"command": 5, "file": 17, "line": 41, "parent": 37}, {"file": 16, "parent": 38}, {"command": 5, "file": 16, "line": 9, "parent": 39}, {"file": 15, "parent": 40}, {"command": 4, "file": 15, "line": 56, "parent": 41}, {"command": 3, "file": 0, "line": 9, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 43}, {"command": 6, "file": 1, "line": 15, "parent": 2}, {"file": 20, "parent": 45}, {"command": 5, "file": 20, "line": 41, "parent": 46}, {"file": 19, "parent": 47}, {"command": 5, "file": 19, "line": 9, "parent": 48}, {"file": 18, "parent": 49}, {"command": 4, "file": 18, "line": 101, "parent": 50}, {"command": 5, "file": 20, "line": 41, "parent": 46}, {"file": 24, "parent": 52}, {"command": 6, "file": 24, "line": 21, "parent": 53}, {"file": 23, "parent": 54}, {"command": 5, "file": 23, "line": 41, "parent": 55}, {"file": 22, "parent": 56}, {"command": 5, "file": 22, "line": 9, "parent": 57}, {"file": 21, "parent": 58}, {"command": 4, "file": 21, "line": 78, "parent": 59}, {"command": 5, "file": 23, "line": 41, "parent": 55}, {"file": 30, "parent": 61}, {"command": 6, "file": 30, "line": 21, "parent": 62}, {"file": 29, "parent": 63}, {"command": 5, "file": 29, "line": 41, "parent": 64}, {"file": 28, "parent": 65}, {"command": 6, "file": 28, "line": 33, "parent": 66}, {"file": 27, "parent": 67}, {"command": 5, "file": 27, "line": 41, "parent": 68}, {"file": 26, "parent": 69}, {"command": 5, "file": 26, "line": 9, "parent": 70}, {"file": 25, "parent": 71}, {"command": 4, "file": 25, "line": 56, "parent": 72}, {"command": 4, "file": 21, "line": 61, "parent": 59}, {"command": 4, "file": 21, "line": 119, "parent": 59}, {"command": 4, "file": 21, "line": 86, "parent": 59}, {"command": 4, "file": 18, "line": 61, "parent": 50}, {"command": 6, "file": 24, "line": 21, "parent": 53}, {"file": 33, "parent": 78}, {"command": 5, "file": 33, "line": 41, "parent": 79}, {"file": 32, "parent": 80}, {"command": 5, "file": 32, "line": 9, "parent": 81}, {"file": 31, "parent": 82}, {"command": 4, "file": 31, "line": 56, "parent": 83}, {"command": 4, "file": 21, "line": 239, "parent": 59}, {"command": 4, "file": 18, "line": 117, "parent": 50}, {"command": 6, "file": 24, "line": 21, "parent": 53}, {"file": 38, "parent": 87}, {"command": 5, "file": 38, "line": 41, "parent": 88}, {"file": 37, "parent": 89}, {"command": 6, "file": 37, "line": 21, "parent": 90}, {"file": 36, "parent": 91}, {"command": 5, "file": 36, "line": 41, "parent": 92}, {"file": 35, "parent": 93}, {"command": 5, "file": 35, "line": 9, "parent": 94}, {"file": 34, "parent": 95}, {"command": 4, "file": 34, "line": 56, "parent": 96}, {"command": 5, "file": 36, "line": 41, "parent": 92}, {"file": 42, "parent": 98}, {"command": 6, "file": 42, "line": 21, "parent": 99}, {"file": 41, "parent": 100}, {"command": 5, "file": 41, "line": 41, "parent": 101}, {"file": 40, "parent": 102}, {"command": 5, "file": 40, "line": 9, "parent": 103}, {"file": 39, "parent": 104}, {"command": 4, "file": 39, "line": 56, "parent": 105}, {"command": 3, "file": 0, "line": 23, "parent": 0}, {"command": 2, "file": 2, "line": 145, "parent": 107}, {"command": 6, "file": 1, "line": 13, "parent": 2}, {"file": 47, "parent": 109}, {"command": 5, "file": 47, "line": 41, "parent": 110}, {"file": 46, "parent": 111}, {"command": 6, "file": 46, "line": 21, "parent": 112}, {"file": 45, "parent": 113}, {"command": 5, "file": 45, "line": 41, "parent": 114}, {"file": 44, "parent": 115}, {"command": 5, "file": 44, "line": 9, "parent": 116}, {"file": 43, "parent": 117}, {"command": 4, "file": 43, "line": 56, "parent": 118}, {"command": 5, "file": 45, "line": 41, "parent": 114}, {"file": 53, "parent": 120}, {"command": 6, "file": 53, "line": 21, "parent": 121}, {"file": 52, "parent": 122}, {"command": 5, "file": 52, "line": 41, "parent": 123}, {"file": 51, "parent": 124}, {"command": 6, "file": 51, "line": 21, "parent": 125}, {"file": 50, "parent": 126}, {"command": 5, "file": 50, "line": 41, "parent": 127}, {"file": 49, "parent": 128}, {"command": 5, "file": 49, "line": 9, "parent": 129}, {"file": 48, "parent": 130}, {"command": 4, "file": 48, "line": 56, "parent": 131}, {"command": 5, "file": 50, "line": 41, "parent": 127}, {"file": 60, "parent": 133}, {"command": 6, "file": 60, "line": 21, "parent": 134}, {"file": 59, "parent": 135}, {"command": 5, "file": 59, "line": 41, "parent": 136}, {"file": 58, "parent": 137}, {"command": 6, "file": 58, "line": 21, "parent": 138}, {"file": 57, "parent": 139}, {"command": 9, "file": 57, "line": 47, "parent": 140}, {"command": 6, "file": 56, "line": 78, "parent": 141}, {"file": 55, "parent": 142}, {"command": 5, "file": 55, "line": 37, "parent": 143}, {"file": 54, "parent": 144}, {"command": 4, "file": 54, "line": 66, "parent": 145}, {"command": 6, "file": 60, "line": 21, "parent": 134}, {"file": 63, "parent": 147}, {"command": 5, "file": 63, "line": 41, "parent": 148}, {"file": 62, "parent": 149}, {"command": 5, "file": 62, "line": 9, "parent": 150}, {"file": 61, "parent": 151}, {"command": 4, "file": 61, "line": 56, "parent": 152}, {"command": 4, "file": 21, "line": 102, "parent": 59}, {"command": 6, "file": 30, "line": 21, "parent": 62}, {"file": 65, "parent": 155}, {"command": 5, "file": 65, "line": 52, "parent": 156}, {"file": 64, "parent": 157}, {"command": 4, "file": 64, "line": 66, "parent": 158}, {"command": 6, "file": 46, "line": 21, "parent": 112}, {"file": 67, "parent": 160}, {"command": 5, "file": 67, "line": 77, "parent": 161}, {"file": 66, "parent": 162}, {"command": 4, "file": 66, "line": 69, "parent": 163}, {"command": 6, "file": 46, "line": 21, "parent": 112}, {"file": 70, "parent": 165}, {"command": 5, "file": 70, "line": 41, "parent": 166}, {"file": 69, "parent": 167}, {"command": 5, "file": 69, "line": 9, "parent": 168}, {"file": 68, "parent": 169}, {"command": 4, "file": 68, "line": 56, "parent": 170}, {"command": 5, "file": 70, "line": 41, "parent": 166}, {"file": 74, "parent": 172}, {"command": 6, "file": 74, "line": 21, "parent": 173}, {"file": 73, "parent": 174}, {"command": 5, "file": 73, "line": 41, "parent": 175}, {"file": 72, "parent": 176}, {"command": 6, "file": 72, "line": 7, "parent": 177}, {"file": 71, "parent": 178}, {"command": 7, "file": 71, "line": 252, "parent": 179}, {"command": 5, "file": 47, "line": 41, "parent": 110}, {"file": 76, "parent": 181}, {"command": 5, "file": 76, "line": 9, "parent": 182}, {"file": 75, "parent": 183}, {"command": 4, "file": 75, "line": 56, "parent": 184}, {"command": 5, "file": 13, "line": 41, "parent": 28}, {"file": 78, "parent": 186}, {"command": 5, "file": 78, "line": 9, "parent": 187}, {"file": 77, "parent": 188}, {"command": 4, "file": 77, "line": 56, "parent": 189}, {"command": 6, "file": 12, "line": 21, "parent": 30}, {"file": 82, "parent": 191}, {"command": 5, "file": 82, "line": 41, "parent": 192}, {"file": 81, "parent": 193}, {"command": 6, "file": 81, "line": 13, "parent": 194}, {"file": 80, "parent": 195}, {"command": 5, "file": 80, "line": 39, "parent": 196}, {"file": 79, "parent": 197}, {"command": 4, "file": 79, "line": 66, "parent": 198}, {"command": 4, "file": 21, "line": 94, "parent": 59}, {"command": 6, "file": 1, "line": 12, "parent": 2}, {"file": 85, "parent": 201}, {"command": 5, "file": 85, "line": 41, "parent": 202}, {"file": 84, "parent": 203}, {"command": 5, "file": 84, "line": 9, "parent": 204}, {"file": 83, "parent": 205}, {"command": 4, "file": 83, "line": 56, "parent": 206}, {"command": 6, "file": 24, "line": 21, "parent": 53}, {"file": 91, "parent": 208}, {"command": 5, "file": 91, "line": 41, "parent": 209}, {"file": 90, "parent": 210}, {"command": 6, "file": 90, "line": 21, "parent": 211}, {"file": 89, "parent": 212}, {"command": 5, "file": 89, "line": 41, "parent": 213}, {"file": 88, "parent": 214}, {"command": 6, "file": 88, "line": 21, "parent": 215}, {"file": 87, "parent": 216}, {"command": 5, "file": 87, "line": 42, "parent": 217}, {"file": 86, "parent": 218}, {"command": 4, "file": 86, "line": 77, "parent": 219}, {"command": 2, "file": 2, "line": 151, "parent": 107}, {"command": 5, "file": 20, "line": 41, "parent": 46}, {"file": 95, "parent": 222}, {"command": 6, "file": 95, "line": 3, "parent": 223}, {"file": 94, "parent": 224}, {"command": 6, "file": 94, "line": 609, "parent": 225}, {"file": 93, "parent": 226}, {"command": 10, "file": 93, "line": 258, "parent": 227}, {"command": 6, "file": 93, "line": 141, "parent": 228}, {"file": 92, "parent": 229}, {"command": 7, "file": 92, "line": 101, "parent": 230}, {"command": 4, "file": 18, "line": 77, "parent": 50}, {"command": 4, "file": 21, "line": 135, "parent": 59}, {"command": 6, "file": 30, "line": 21, "parent": 62}, {"file": 98, "parent": 234}, {"command": 5, "file": 98, "line": 41, "parent": 235}, {"file": 97, "parent": 236}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 96, "parent": 238}, {"command": 4, "file": 96, "line": 56, "parent": 239}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 99, "parent": 241}, {"command": 4, "file": 99, "line": 56, "parent": 242}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 100, "parent": 244}, {"command": 4, "file": 100, "line": 56, "parent": 245}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 101, "parent": 247}, {"command": 4, "file": 101, "line": 56, "parent": 248}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 102, "parent": 250}, {"command": 4, "file": 102, "line": 56, "parent": 251}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 103, "parent": 253}, {"command": 4, "file": 103, "line": 56, "parent": 254}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 104, "parent": 256}, {"command": 4, "file": 104, "line": 56, "parent": 257}, {"command": 5, "file": 97, "line": 9, "parent": 237}, {"file": 105, "parent": 259}, {"command": 4, "file": 105, "line": 56, "parent": 260}, {"command": 6, "file": 46, "line": 21, "parent": 112}, {"file": 114, "parent": 262}, {"command": 5, "file": 114, "line": 41, "parent": 263}, {"file": 113, "parent": 264}, {"command": 6, "file": 113, "line": 21, "parent": 265}, {"file": 112, "parent": 266}, {"command": 5, "file": 112, "line": 41, "parent": 267}, {"file": 111, "parent": 268}, {"command": 6, "file": 111, "line": 21, "parent": 269}, {"file": 110, "parent": 270}, {"command": 5, "file": 110, "line": 41, "parent": 271}, {"file": 109, "parent": 272}, {"command": 6, "file": 109, "line": 21, "parent": 273}, {"file": 108, "parent": 274}, {"command": 5, "file": 108, "line": 41, "parent": 275}, {"file": 107, "parent": 276}, {"command": 5, "file": 107, "line": 9, "parent": 277}, {"file": 106, "parent": 278}, {"command": 4, "file": 106, "line": 56, "parent": 279}, {"command": 5, "file": 85, "line": 41, "parent": 202}, {"file": 120, "parent": 281}, {"command": 6, "file": 120, "line": 21, "parent": 282}, {"file": 119, "parent": 283}, {"command": 5, "file": 119, "line": 41, "parent": 284}, {"file": 118, "parent": 285}, {"command": 6, "file": 118, "line": 21, "parent": 286}, {"file": 117, "parent": 287}, {"command": 5, "file": 117, "line": 41, "parent": 288}, {"file": 116, "parent": 289}, {"command": 5, "file": 116, "line": 9, "parent": 290}, {"file": 115, "parent": 291}, {"command": 4, "file": 115, "line": 56, "parent": 292}, {"command": 12, "file": 1, "line": 6, "parent": 2}, {"command": 11, "file": 121, "line": 46, "parent": 294}, {"command": 11, "file": 121, "line": 67, "parent": 294}, {"command": 13, "file": 1, "line": 9, "parent": 2}, {"command": 13, "file": 1, "line": 49, "parent": 2}, {"command": 14, "file": 1, "line": 90, "parent": 2}, {"command": 15, "file": 0, "line": 29, "parent": 0}, {"command": 15, "file": 2, "line": 141, "parent": 107}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG -std=c++17 -fPIC"}, {"backtrace": 295, "fragment": "-Wall"}, {"backtrace": 295, "fragment": "-Wextra"}, {"backtrace": 295, "fragment": "-Wwrite-strings"}, {"backtrace": 295, "fragment": "-Wunreachable-code"}, {"backtrace": 295, "fragment": "-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"backtrace": 295, "fragment": "-Wredundant-decls"}, {"backtrace": 295, "fragment": "-Wcast-qual"}, {"backtrace": 296, "fragment": "-Wno-maybe-uninitialized"}, {"backtrace": 4, "fragment": "-fPIC"}], "defines": [{"backtrace": 4, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 4, "define": "BOOST_ATOMIC_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_CHRONO_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_DATE_TIME_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_IOSTREAMS_DYN_LINK"}, {"backtrace": 297, "define": "BOOST_MATH_DISABLE_FLOAT128"}, {"backtrace": 4, "define": "BOOST_PROGRAM_OPTIONS_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_REGEX_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SERIALIZATION_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_THREAD_DYN_LINK"}, {"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "FMT_LOCALE"}, {"backtrace": 4, "define": "FMT_SHARED"}, {"backtrace": 4, "define": "QT_CORE_LIB"}, {"backtrace": 4, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_NO_DEBUG"}, {"backtrace": 298, "define": "QT_NO_KEYWORDS"}, {"backtrace": 4, "define": "QT_WIDGETS_LIB"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "moveit_robot_state_rviz_plugin_EXPORTS"}], "includes": [{"path": "/home/<USER>/ws_moveit2/build/moveit_ros_visualization/robot_state_rviz_plugin"}, {"path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization/robot_state_rviz_plugin"}, {"backtrace": 0, "path": "/home/<USER>/ws_moveit2/build/moveit_ros_visualization/robot_state_rviz_plugin/moveit_robot_state_rviz_plugin_autogen/include"}, {"backtrace": 299, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization/rviz_plugin_render_tools/include"}, {"backtrace": 299, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization/robot_state_rviz_plugin/include"}, {"backtrace": 299, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization/planning_scene_rviz_plugin/include"}, {"backtrace": 299, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization/motion_planning_rviz_plugin/include"}, {"backtrace": 299, "path": "/home/<USER>/ws_moveit2/src/moveit2/moveit_ros/visualization/trajectory_rviz_plugin/include"}, {"backtrace": 300, "path": "/opt/ros/humble/opt/rviz_ogre_vendor/include"}, {"backtrace": 301, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 301, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtWidgets"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtGui"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/aarch64-linux-gnu/qt5/QtCore"}, {"backtrace": 4, "isSystem": true, "path": "/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/bullet"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/moveit_core/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdf"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdf_parser_plugin"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom_headers"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/eigen_stl_containers"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_eigen"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/geometric_shapes"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/resource_retriever"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/angles"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/parameter_traits"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rsl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/kdl_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rviz_common"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/opt/rviz_ogre_vendor/include/OGRE"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rviz_rendering"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rviz_default_plugins"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/image_transport"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/interactive_markers"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/laser_geometry"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/map_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/nav_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/include"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/include"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4, 4], "standard": "17"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 4, "id": "moveit_rviz_plugin_render_tools::@11af576cd519c4ccae31"}, {"backtrace": 4, "id": "moveit_robot_state_rviz_plugin_core::@3cccd1ee7f0cc842f242"}, {"id": "moveit_robot_state_rviz_plugin_autogen_timestamp_deps::@3cccd1ee7f0cc842f242"}, {"backtrace": 0, "id": "moveit_robot_state_rviz_plugin_autogen::@3cccd1ee7f0cc842f242"}], "id": "moveit_robot_state_rviz_plugin::@3cccd1ee7f0cc842f242", "install": {"destinations": [{"backtrace": 3, "path": "lib"}, {"backtrace": 3, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_moveit2/install/moveit_ros_visualization"}}, "link": {"commandFragments": [{"fragment": "-Wl,-rpath,/home/<USER>/ws_moveit2/build/moveit_ros_visualization/robot_state_rviz_plugin:/home/<USER>/ws_moveit2/build/moveit_ros_visualization/rviz_plugin_render_tools:/opt/ros/humble/lib/aarch64-linux-gnu:/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit2/install/moveit_core/lib:/opt/ros/humble/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/home/<USER>/ws_moveit2/install/srdfdom/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "libmoveit_robot_state_rviz_plugin_core.so.2.5.9", "role": "libraries"}, {"backtrace": 5, "fragment": "../rviz_plugin_render_tools/libmoveit_rviz_plugin_render_tools.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/librviz_default_plugins.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/librviz_common.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/usr/lib/aarch64-linux-gnu/libyaml-cpp.so.0.7.0", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/librviz_rendering.so", "role": "libraries"}, {"backtrace": 16, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5Widgets.so.5.15.3", "role": "libraries"}, {"backtrace": 24, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5Gui.so.5.15.3", "role": "libraries"}, {"backtrace": 24, "fragment": "/usr/lib/aarch64-linux-gnu/libQt5Core.so.5.15.3", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libfreetype.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libOpenGL.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libGLX.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libGLU.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libSM.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libICE.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libX11.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libXext.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libXt.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libXrandr.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libXaw.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/libimage_transport.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libinteractive_markers.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblaser_geometry.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_constraint_sampler_manager_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_plan_execution.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_default_planning_request_adapter_plugins.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_cpp.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_pipeline.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_trajectory_execution_manager.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_planning_scene_monitor.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_robot_model_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_kinematics_plugin_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_rdf_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_planning/lib/libmoveit_collision_plugin_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 51, "fragment": "/home/<USER>/ws_moveit2/install/moveit_ros_occupancy_map_monitor/lib/libmoveit_ros_occupancy_map_monitor.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libcollision_detector_bullet_plugin.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_butterworth_filter.so.2.5.9", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 73, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/opt/ros/humble/lib/librsl.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_distance_field.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_detection_bullet.so.2.5.9", "role": "libraries"}, {"backtrace": 74, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletDynamics.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletCollision.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/usr/lib/aarch64-linux-gnu/libLinearMath.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletSoftBody.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_dynamics_solver.so.2.5.9", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/humble/lib/libkdl_parser.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_constraint_samplers.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_distance_field.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_kinematics_metrics.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_planning_interface.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_planning_request_adapter.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_planning_scene.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_kinematic_constraints.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_detection_fcl.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_collision_detection.so.2.5.9", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_smoothing_base.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_test_utils.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_trajectory_processing.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_robot_trajectory.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_robot_state.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_robot_model.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_exceptions.so.2.5.9", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_kinematics_base.so", "role": "libraries"}, {"backtrace": 77, "fragment": "/home/<USER>/ws_moveit2/install/srdfdom/lib/libsrdfdom.so.2.0.8", "role": "libraries"}, {"backtrace": 75, "fragment": "/usr/lib/aarch64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 77, "fragment": "/opt/ros/humble/lib/liburdf.so", "role": "libraries"}, {"backtrace": 84, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 84, "fragment": "/usr/lib/aarch64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 85, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/libruckig.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_transforms.so.2.5.9", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 86, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 106, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 97, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/opt/ros/humble/lib/libgeometric_shapes.so.2.3.2", "role": "libraries"}, {"backtrace": 108, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 132, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1", "role": "libraries"}, {"backtrace": 146, "fragment": "-Wl,--as-needed", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 153, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 74, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 154, "fragment": "/usr/lib/aarch64-linux-gnu/libfcl.so.0.7.0", "role": "libraries"}, {"backtrace": 159, "fragment": "/usr/lib/aarch64-linux-gnu/libccd.so", "role": "libraries"}, {"backtrace": 159, "fragment": "/usr/lib/aarch64-linux-gnu/libm.so", "role": "libraries"}, {"backtrace": 159, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so.1.9.8", "role": "libraries"}, {"backtrace": 164, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so.1.9.8", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/libresource_retriever.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 171, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 180, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 185, "fragment": "/opt/ros/humble/lib/librandom_numbers.so", "role": "libraries"}, {"backtrace": 190, "fragment": "/usr/lib/aarch64-linux-gnu/libassimp.so.5.2.0", "role": "libraries"}, {"backtrace": 35, "fragment": "/usr/lib/aarch64-linux-gnu/libz.so", "role": "libraries"}, {"backtrace": 199, "fragment": "/usr/lib/aarch64-linux-gnu/libdraco.so.4.0.0", "role": "libraries"}, {"backtrace": 199, "fragment": "-l<PERSON>zip", "role": "libraries"}, {"backtrace": 199, "fragment": "/usr/lib/aarch64-linux-gnu/librt.a", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_sensor.so.3.0", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_model_state.so.3.0", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_model.so.3.0", "role": "libraries"}, {"backtrace": 200, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_world.so.3.0", "role": "libraries"}, {"backtrace": 207, "fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 220, "fragment": "/usr/lib/aarch64-linux-gnu/libtinyxml.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/home/<USER>/ws_moveit2/install/moveit_core/lib/libmoveit_utils.so.2.5.9", "role": "libraries"}, {"backtrace": 221, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_thread.so.1.74.0", "role": "libraries"}, {"backtrace": 231, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.74.0", "role": "libraries"}, {"backtrace": 221, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.74.0", "role": "libraries"}, {"backtrace": 221, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_system.so.1.74.0", "role": "libraries"}, {"backtrace": 221, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 232, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.74.0", "role": "libraries"}, {"backtrace": 232, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.74.0", "role": "libraries"}, {"backtrace": 233, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_iostreams.so.1.74.0", "role": "libraries"}, {"backtrace": 233, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_regex.so.1.74.0", "role": "libraries"}, {"backtrace": 233, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_serialization.so.1.74.0", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 240, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 246, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 249, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 243, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 252, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 252, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 252, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 252, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 252, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 252, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 255, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 258, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 255, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 258, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 255, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 258, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 255, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 258, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 255, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 258, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 255, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 258, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 280, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 261, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 119, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 293, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 255, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}], "language": "CXX"}, "name": "moveit_robot_state_rviz_plugin", "nameOnDisk": "libmoveit_robot_state_rviz_plugin.so", "paths": {"build": "robot_state_rviz_plugin", "source": "robot_state_rviz_plugin"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "", "sourceIndexes": [2]}, {"name": "CMake Rules", "sourceIndexes": [3]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_ros_visualization/robot_state_rviz_plugin/moveit_robot_state_rviz_plugin_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "robot_state_rviz_plugin/src/plugin_init.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_ros_visualization/robot_state_rviz_plugin/moveit_robot_state_rviz_plugin_autogen/timestamp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_moveit2/build/moveit_ros_visualization/robot_state_rviz_plugin/moveit_robot_state_rviz_plugin_autogen/timestamp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}