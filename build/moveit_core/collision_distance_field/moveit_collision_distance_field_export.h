
#ifndef MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT_H
#define MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT_H

#ifdef MOVEIT_COLLISION_DISTANCE_FIELD_STATIC_DEFINE
#  define MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT
#  define MOVEIT_COLLISION_DISTANCE_FIELD_NO_EXPORT
#else
#  ifndef MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT
#    ifdef moveit_collision_distance_field_EXPORTS
        /* We are building this library */
#      define MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT __attribute__((visibility("default")))
#    else
        /* We are using this library */
#      define MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT __attribute__((visibility("default")))
#    endif
#  endif

#  ifndef MOVEIT_COLLISION_DISTANCE_FIELD_NO_EXPORT
#    define MOVEIT_COLLISION_DISTANCE_FIELD_NO_EXPORT __attribute__((visibility("hidden")))
#  endif
#endif

#ifndef MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED
#  define MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED __attribute__ ((__deprecated__))
#endif

#ifndef MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED_EXPORT
#  define MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED_EXPORT MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED
#endif

#ifndef MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED_NO_EXPORT
#  define MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED_NO_EXPORT MOVEIT_COLLISION_DISTANCE_FIELD_NO_EXPORT MOVEIT_COLLISION_DISTANCE_FIELD_DEPRECATED
#endif

/* NOLINTNEXTLINE(readability-avoid-unconditional-preprocessor-if) */
#if 0 /* DEFINE_NO_DEPRECATED */
#  ifndef MOVEIT_COLLISION_DISTANCE_FIELD_NO_DEPRECATED
#    define MOVEIT_COLLISION_DISTANCE_FIELD_NO_DEPRECATED
#  endif
#endif

#endif /* MOVEIT_COLLISION_DISTANCE_FIELD_EXPORT_H */
