# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CX<PERSON> with /usr/bin/c++
CXX_DEFINES = -DBOOST_ALL_NO_LIB -DBOOST_ATOMIC_DYN_LINK -DBOOST_CHRONO_DYN_LINK -DBOOST_DATE_TIME_DYN_LINK -DBOOST_FILESYSTEM_DYN_LINK -DBOOST_IOSTREAMS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_REGEX_DYN_LINK -DBOOST_SERIALIZATION_DYN_LINK -DBOOST_SYSTEM_DYN_LINK -DBOOST_THREAD_DYN_LINK -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DRCUTILS_ENABLE_FAULT_INJECTION

CXX_INCLUDES = -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/exceptions/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection_fcl/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection_bullet/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/constraint_samplers/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/controller_manager/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/distance_field/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_distance_field/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/dynamics_solver/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematics_base/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematics_metrics/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_model/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/transforms/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_state/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_trajectory/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematic_constraints/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/macros/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/online_signal_smoothing/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_interface/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_request_adapter/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_scene/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/sensor_manager/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/trajectory_processing/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/utils/include -I/home/<USER>/ws_moveit2/build/moveit_core/include -I/home/<USER>/ws_moveit2/build/moveit_core/kinematics_base -I/home/<USER>/ws_moveit2/build/moveit_core/collision_detection_fcl -I/home/<USER>/ws_moveit2/build/moveit_core/collision_detection -I/home/<USER>/ws_moveit2/build/moveit_core/planning_scene -isystem /usr/include/eigen3 -isystem /opt/ros/humble/src/gtest_vendor/include -isystem /opt/ros/humble/include/angles -isystem /opt/ros/humble/include/moveit_msgs -isystem /opt/ros/humble/include/action_msgs -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/unique_identifier_msgs -isystem /opt/ros/humble/include/std_msgs -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/shape_msgs -isystem /opt/ros/humble/include/object_recognition_msgs -isystem /opt/ros/humble/include/octomap_msgs -isystem /opt/ros/humble/include/trajectory_msgs -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/humble/include/eigen_stl_containers -isystem /opt/ros/humble/include/geometric_shapes -isystem /opt/ros/humble/include -isystem /opt/ros/humble/include/rclcpp -isystem /opt/ros/humble/include/ament_index_cpp -isystem /opt/ros/humble/include/libstatistics_collector -isystem /opt/ros/humble/include/rcl -isystem /opt/ros/humble/include/rcl_interfaces -isystem /opt/ros/humble/include/rcl_logging_interface -isystem /opt/ros/humble/include/rcl_yaml_param_parser -isystem /opt/ros/humble/include/libyaml_vendor -isystem /opt/ros/humble/include/tracetools -isystem /opt/ros/humble/include/rcpputils -isystem /opt/ros/humble/include/statistics_msgs -isystem /opt/ros/humble/include/rosgraph_msgs -isystem /opt/ros/humble/include/rosidl_typesupport_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_c -isystem /opt/ros/humble/include/resource_retriever -isystem /opt/ros/humble/include/visualization_msgs -isystem /opt/ros/humble/include/urdf -isystem /opt/ros/humble/include/urdf_parser_plugin -isystem /opt/ros/humble/include/urdfdom_headers -isystem /opt/ros/humble/include/urdfdom -isystem /opt/ros/humble/include/pluginlib -isystem /opt/ros/humble/include/class_loader -isystem /home/<USER>/ws_moveit2/install/srdfdom/include/srdfdom -isystem /opt/ros/humble/include/tf2_geometry_msgs -isystem /opt/ros/humble/include/tf2 -isystem /opt/ros/humble/include/tf2_ros -isystem /opt/ros/humble/include/message_filters -isystem /opt/ros/humble/include/rclcpp_action -isystem /opt/ros/humble/include/rcl_action -isystem /opt/ros/humble/include/tf2_msgs -isystem /opt/ros/humble/include/tf2_eigen

CXX_FLAGS = -O3 -DNDEBUG -std=c++17 -Wall -Wextra -Wwrite-strings -Wunreachable-code -Wpointer-arith -Wredundant-decls -Wcast-qual -Wno-maybe-uninitialized -Wno-deprecated-declarations

