# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = 

CXX_INCLUDES = -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/exceptions/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection_fcl/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection_bullet/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/constraint_samplers/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/controller_manager/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/distance_field/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_distance_field/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/dynamics_solver/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematics_base/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematics_metrics/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_model/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/transforms/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_state/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/robot_trajectory/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/kinematic_constraints/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/macros/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/online_signal_smoothing/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_interface/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_request_adapter/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/planning_scene/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/sensor_manager/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/trajectory_processing/include -I/home/<USER>/ws_moveit2/src/moveit2/moveit_core/utils/include -I/home/<USER>/ws_moveit2/build/moveit_core/include -I/opt/ros/humble/src/gmock_vendor/include -I/opt/ros/humble/src/gmock_vendor/. -I/opt/ros/humble/src/gtest_vendor/include -I/opt/ros/humble/src/gtest_vendor -isystem /usr/include/eigen3 -isystem /opt/ros/humble/include/angles -isystem /opt/ros/humble/include/tf2_kdl -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/tf2 -isystem /opt/ros/humble/include/tf2_ros

CXX_FLAGS = -O3 -DNDEBUG -std=c++17 -Wall -Wextra -Wwrite-strings -Wunreachable-code -Wpointer-arith -Wredundant-decls -Wcast-qual -Wno-maybe-uninitialized

