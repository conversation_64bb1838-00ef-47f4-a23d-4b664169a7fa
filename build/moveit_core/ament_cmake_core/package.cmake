set(_AMENT_PACKAGE_NAME "moveit_core")
set(moveit_core_VERSION "2.5.9")
set(moveit_core_MAINTAINER "<PERSON><PERSON> <he<PERSON><PERSON><PERSON><PERSON>@picknik.ai>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, MoveIt Release Team <<EMAIL>>")
set(moveit_core_BUILD_DEPENDS "angles" "assimp" "boost" "bullet" "common_interfaces" "eigen_stl_containers" "eigen" "generate_parameter_library" "geometric_shapes" "geometry_msgs" "kdl_parser" "libfcl-dev" "moveit_common" "moveit_msgs" "octomap_msgs" "octomap" "pluginlib" "pybind11_vendor" "random_numbers" "rclcpp" "ruckig" "sensor_msgs" "shape_msgs" "srdfdom" "std_msgs" "tf2_eigen" "tf2_geometry_msgs" "tf2_kdl" "tf2" "trajectory_msgs" "urdf" "urdfdom_headers" "urdfdom" "visualization_msgs")
set(moveit_core_BUILDTOOL_DEPENDS "ament_cmake" "pkg-config" "eigen3_cmake_module")
set(moveit_core_BUILD_EXPORT_DEPENDS "angles" "assimp" "boost" "bullet" "common_interfaces" "eigen_stl_containers" "eigen" "generate_parameter_library" "geometric_shapes" "geometry_msgs" "kdl_parser" "libfcl-dev" "moveit_common" "moveit_msgs" "octomap_msgs" "octomap" "pluginlib" "pybind11_vendor" "random_numbers" "rclcpp" "ruckig" "sensor_msgs" "shape_msgs" "srdfdom" "std_msgs" "tf2_eigen" "tf2_geometry_msgs" "tf2_kdl" "tf2" "trajectory_msgs" "urdf" "urdfdom_headers" "urdfdom" "visualization_msgs")
set(moveit_core_BUILDTOOL_EXPORT_DEPENDS "eigen3_cmake_module")
set(moveit_core_EXEC_DEPENDS "angles" "assimp" "boost" "bullet" "common_interfaces" "eigen_stl_containers" "eigen" "generate_parameter_library" "geometric_shapes" "geometry_msgs" "kdl_parser" "libfcl-dev" "moveit_common" "moveit_msgs" "octomap_msgs" "octomap" "pluginlib" "pybind11_vendor" "random_numbers" "rclcpp" "ruckig" "sensor_msgs" "shape_msgs" "srdfdom" "std_msgs" "tf2_eigen" "tf2_geometry_msgs" "tf2_kdl" "tf2" "trajectory_msgs" "urdf" "urdfdom_headers" "urdfdom" "visualization_msgs")
set(moveit_core_TEST_DEPENDS "moveit_resources_panda_moveit_config" "moveit_resources_pr2_description" "angles" "orocos_kdl_vendor" "ament_cmake_gtest" "ament_cmake_gmock" "ament_index_cpp" "ament_lint_auto" "ament_lint_common")
set(moveit_core_GROUP_DEPENDS )
set(moveit_core_MEMBER_OF_GROUPS )
set(moveit_core_DEPRECATED "")
set(moveit_core_EXPORT_TAGS)
list(APPEND moveit_core_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
