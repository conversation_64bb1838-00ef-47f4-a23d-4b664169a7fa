<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>moveit_core</name>
  <version>2.5.9</version>
  <description>Core libraries used by MoveIt</description>

  <maintainer email="henning<PERSON><PERSON><EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">MoveIt Release Team</maintainer>

  <license>BSD</license>

  <url type="website">http://moveit.ros.org</url>
  <url type="bugtracker">https://github.com/ros-planning/moveit2/issues</url>
  <url type="repository">https://github.com/ros-planning/moveit2</url>

  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="<EMAIL>">Acorn Pooley</author>
  <author email="<EMAIL>">Dave Coleman</author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>pkg-config</buildtool_depend>
  <buildtool_depend>eigen3_cmake_module</buildtool_depend>
  <buildtool_export_depend>eigen3_cmake_module</buildtool_export_depend>
  <depend>angles</depend>
  <depend>assimp</depend>
  <depend>boost</depend>
  <depend>bullet</depend>
  <depend>common_interfaces</depend>
  <depend>eigen_stl_containers</depend>
  <depend>eigen</depend>
  <depend>generate_parameter_library</depend>
  <depend>geometric_shapes</depend>
  <depend>geometry_msgs</depend>
  <depend>kdl_parser</depend>
  <depend>libfcl-dev</depend>
  <depend>moveit_common</depend>
  <depend>moveit_msgs</depend>
  <depend>octomap_msgs</depend>
  <depend>octomap</depend>
  <depend>pluginlib</depend>
  <depend>pybind11_vendor</depend>
  <depend>random_numbers</depend>
  <depend>rclcpp</depend>
  <depend>ruckig</depend>
  <depend>sensor_msgs</depend>
  <depend>shape_msgs</depend>
  <depend>srdfdom</depend>
  <depend>std_msgs</depend>
  <depend>tf2_eigen</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>tf2_kdl</depend>
  <depend>tf2</depend>
  <depend>trajectory_msgs</depend>
  <depend>urdf</depend>
  <depend>urdfdom_headers</depend>
  <depend>urdfdom</depend>
  <depend>visualization_msgs</depend>

  <doc_depend>python3-sphinx-rtd-theme</doc_depend>

  <test_depend>moveit_resources_panda_moveit_config</test_depend>
  <test_depend>moveit_resources_pr2_description</test_depend>
  <test_depend>angles</test_depend>
  <test_depend>orocos_kdl_vendor</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_index_cpp</test_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
