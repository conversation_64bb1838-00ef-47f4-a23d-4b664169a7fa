# CMake generated Testfile for 
# Source directory: /home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection
# Build directory: /home/<USER>/ws_moveit2/build/moveit_core/collision_detection
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test([=[test_world]=] "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/home/<USER>/ws_moveit2/build/moveit_core/test_results/moveit_core/test_world.gtest.xml" "--package-name" "moveit_core" "--output-file" "/home/<USER>/ws_moveit2/build/moveit_core/ament_cmake_gtest/test_world.txt" "--append-env" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/../robot_model" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/../utils" "--command" "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/test_world" "--gtest_output=xml:/home/<USER>/ws_moveit2/build/moveit_core/test_results/moveit_core/test_world.gtest.xml")
set_tests_properties([=[test_world]=] PROPERTIES  LABELS "gtest" REQUIRED_FILES "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/test_world" TIMEOUT "60" WORKING_DIRECTORY "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_add_gtest_test.cmake;86;ament_add_test;/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_add_gtest.cmake;93;ament_add_gtest_test;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/CMakeLists.txt;43;ament_add_gtest;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/CMakeLists.txt;0;")
add_test([=[test_world_diff]=] "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/home/<USER>/ws_moveit2/build/moveit_core/test_results/moveit_core/test_world_diff.gtest.xml" "--package-name" "moveit_core" "--output-file" "/home/<USER>/ws_moveit2/build/moveit_core/ament_cmake_gtest/test_world_diff.txt" "--append-env" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/../robot_model" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/../utils" "--command" "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/test_world_diff" "--gtest_output=xml:/home/<USER>/ws_moveit2/build/moveit_core/test_results/moveit_core/test_world_diff.gtest.xml")
set_tests_properties([=[test_world_diff]=] PROPERTIES  LABELS "gtest" REQUIRED_FILES "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/test_world_diff" TIMEOUT "60" WORKING_DIRECTORY "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_add_gtest_test.cmake;86;ament_add_test;/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_add_gtest.cmake;93;ament_add_gtest_test;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/CMakeLists.txt;47;ament_add_gtest;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/CMakeLists.txt;0;")
add_test([=[test_all_valid]=] "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/home/<USER>/ws_moveit2/build/moveit_core/test_results/moveit_core/test_all_valid.gtest.xml" "--package-name" "moveit_core" "--output-file" "/home/<USER>/ws_moveit2/build/moveit_core/ament_cmake_gtest/test_all_valid.txt" "--append-env" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/../robot_model" "LD_LIBRARY_PATH=/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/../utils" "--command" "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/test_all_valid" "--gtest_output=xml:/home/<USER>/ws_moveit2/build/moveit_core/test_results/moveit_core/test_all_valid.gtest.xml")
set_tests_properties([=[test_all_valid]=] PROPERTIES  LABELS "gtest" REQUIRED_FILES "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection/test_all_valid" TIMEOUT "60" WORKING_DIRECTORY "/home/<USER>/ws_moveit2/build/moveit_core/collision_detection" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_add_gtest_test.cmake;86;ament_add_test;/opt/ros/humble/share/ament_cmake_gtest/cmake/ament_add_gtest.cmake;93;ament_add_gtest_test;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/CMakeLists.txt;51;ament_add_gtest;/home/<USER>/ws_moveit2/src/moveit2/moveit_core/collision_detection/CMakeLists.txt;0;")
